/*! @license DOMPurify 3.2.6 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:Tt,setPrototypeOf:rt,isFrozen:Bt,getPrototypeOf:Yt,getOwnPropertyDescriptor:Xt}=Object;let{freeze:S,seal:y,create:_t}=Object,{apply:we,construct:xe}=typeof Reflect!="undefined"&&Reflect;S||(S=function(o){return o});y||(y=function(o){return o});we||(we=function(o,l,s){return o.apply(l,s)});xe||(xe=function(o,l){return new o(...l)});const le=R(Array.prototype.forEach),jt=R(Array.prototype.lastIndexOf),st=R(Array.prototype.pop),$=R(Array.prototype.push),Vt=R(Array.prototype.splice),fe=R(String.prototype.toLowerCase),Ne=R(String.prototype.toString),lt=R(String.prototype.match),q=R(String.prototype.replace),$t=R(String.prototype.indexOf),qt=R(String.prototype.trim),L=R(Object.prototype.hasOwnProperty),A=R(RegExp.prototype.test),K=Kt(TypeError);function R(r){return function(o){o instanceof RegExp&&(o.lastIndex=0);for(var l=arguments.length,s=new Array(l>1?l-1:0),T=1;T<l;T++)s[T-1]=arguments[T];return we(r,o,s)}}function Kt(r){return function(){for(var o=arguments.length,l=new Array(o),s=0;s<o;s++)l[s]=arguments[s];return xe(r,l)}}function a(r,o){let l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:fe;rt&&rt(r,null);let s=o.length;for(;s--;){let T=o[s];if(typeof T=="string"){const C=l(T);C!==T&&(Bt(o)||(o[s]=C),T=C)}r[T]=!0}return r}function Zt(r){for(let o=0;o<r.length;o++)L(r,o)||(r[o]=null);return r}function M(r){const o=_t(null);for(const[l,s]of Tt(r))L(r,l)&&(Array.isArray(s)?o[l]=Zt(s):s&&typeof s=="object"&&s.constructor===Object?o[l]=M(s):o[l]=s);return o}function Z(r,o){for(;r!==null;){const s=Xt(r,o);if(s){if(s.get)return R(s.get);if(typeof s.value=="function")return R(s.value)}r=Yt(r)}function l(){return null}return l}const ct=S(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),be=S(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ie=S(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Jt=S(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Me=S(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Qt=S(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ft=S(["#text"]),ut=S(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Ce=S(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),mt=S(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ce=S(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),en=y(/\{\{[\w\W]*|[\w\W]*\}\}/gm),tn=y(/<%[\w\W]*|[\w\W]*%>/gm),nn=y(/\$\{[\w\W]*/gm),on=y(/^data-[\-\w.\u00B7-\uFFFF]+$/),an=y(/^aria-[\-\w]+$/),Et=y(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),rn=y(/^(?:\w+script|data):/i),sn=y(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),gt=y(/^html$/i),ln=y(/^[a-z][.\w]*(-[.\w]+)+$/i);var pt=Object.freeze({__proto__:null,ARIA_ATTR:an,ATTR_WHITESPACE:sn,CUSTOM_ELEMENT:ln,DATA_ATTR:on,DOCTYPE_NAME:gt,ERB_EXPR:tn,IS_ALLOWED_URI:Et,IS_SCRIPT_OR_DATA:rn,MUSTACHE_EXPR:en,TMPLIT_EXPR:nn});const J={element:1,text:3,progressingInstruction:7,comment:8,document:9},cn=function(){return typeof window=="undefined"?null:window},fn=function(o,l){if(typeof o!="object"||typeof o.createPolicy!="function")return null;let s=null;const T="data-tt-policy-suffix";l&&l.hasAttribute(T)&&(s=l.getAttribute(T));const C="dompurify"+(s?"#"+s:"");try{return o.createPolicy(C,{createHTML(x){return x},createScriptURL(x){return x}})}catch(x){return null}},dt=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function ht(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:cn();const o=i=>ht(i);if(o.version="3.2.6",o.removed=[],!r||!r.document||r.document.nodeType!==J.document||!r.Element)return o.isSupported=!1,o;let{document:l}=r;const s=l,T=s.currentScript,{DocumentFragment:C,HTMLTemplateElement:x,Node:ue,Element:Pe,NodeFilter:G,NamedNodeMap:At=r.NamedNodeMap||r.MozNamedAttrMap,HTMLFormElement:St,DOMParser:Rt,trustedTypes:Q}=r,W=Pe.prototype,Ot=Z(W,"cloneNode"),yt=Z(W,"remove"),Lt=Z(W,"nextSibling"),Dt=Z(W,"childNodes"),ee=Z(W,"parentNode");if(typeof x=="function"){const i=l.createElement("template");i.content&&i.content.ownerDocument&&(l=i.content.ownerDocument)}let g,B="";const{implementation:me,createNodeIterator:Nt,createDocumentFragment:bt,getElementsByTagName:It}=l,{importNode:Mt}=s;let h=dt();o.isSupported=typeof Tt=="function"&&typeof ee=="function"&&me&&me.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:pe,ERB_EXPR:de,TMPLIT_EXPR:Te,DATA_ATTR:Ct,ARIA_ATTR:wt,IS_SCRIPT_OR_DATA:xt,ATTR_WHITESPACE:ve,CUSTOM_ELEMENT:Pt}=pt;let{IS_ALLOWED_URI:ke}=pt,m=null;const Ue=a({},[...ct,...be,...Ie,...Me,...ft]);let d=null;const Fe=a({},[...ut,...Ce,...mt,...ce]);let f=Object.seal(_t(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Y=null,_e=null,He=!0,Ee=!0,ze=!1,Ge=!0,P=!1,te=!0,w=!1,ge=!1,he=!1,v=!1,ne=!1,oe=!1,We=!0,Be=!1;const vt="user-content-";let Ae=!0,X=!1,k={},U=null;const Ye=a({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Xe=null;const je=a({},["audio","video","img","source","image","track"]);let Se=null;const Ve=a({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ie="http://www.w3.org/1998/Math/MathML",ae="http://www.w3.org/2000/svg",N="http://www.w3.org/1999/xhtml";let F=N,Re=!1,Oe=null;const kt=a({},[ie,ae,N],Ne);let re=a({},["mi","mo","mn","ms","mtext"]),se=a({},["annotation-xml"]);const Ut=a({},["title","style","font","a","script"]);let j=null;const Ft=["application/xhtml+xml","text/html"],Ht="text/html";let p=null,H=null;const zt=l.createElement("form"),$e=function(e){return e instanceof RegExp||e instanceof Function},ye=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(H&&H===e)){if((!e||typeof e!="object")&&(e={}),e=M(e),j=Ft.indexOf(e.PARSER_MEDIA_TYPE)===-1?Ht:e.PARSER_MEDIA_TYPE,p=j==="application/xhtml+xml"?Ne:fe,m=L(e,"ALLOWED_TAGS")?a({},e.ALLOWED_TAGS,p):Ue,d=L(e,"ALLOWED_ATTR")?a({},e.ALLOWED_ATTR,p):Fe,Oe=L(e,"ALLOWED_NAMESPACES")?a({},e.ALLOWED_NAMESPACES,Ne):kt,Se=L(e,"ADD_URI_SAFE_ATTR")?a(M(Ve),e.ADD_URI_SAFE_ATTR,p):Ve,Xe=L(e,"ADD_DATA_URI_TAGS")?a(M(je),e.ADD_DATA_URI_TAGS,p):je,U=L(e,"FORBID_CONTENTS")?a({},e.FORBID_CONTENTS,p):Ye,Y=L(e,"FORBID_TAGS")?a({},e.FORBID_TAGS,p):M({}),_e=L(e,"FORBID_ATTR")?a({},e.FORBID_ATTR,p):M({}),k=L(e,"USE_PROFILES")?e.USE_PROFILES:!1,He=e.ALLOW_ARIA_ATTR!==!1,Ee=e.ALLOW_DATA_ATTR!==!1,ze=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ge=e.ALLOW_SELF_CLOSE_IN_ATTR!==!1,P=e.SAFE_FOR_TEMPLATES||!1,te=e.SAFE_FOR_XML!==!1,w=e.WHOLE_DOCUMENT||!1,v=e.RETURN_DOM||!1,ne=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,he=e.FORCE_BODY||!1,We=e.SANITIZE_DOM!==!1,Be=e.SANITIZE_NAMED_PROPS||!1,Ae=e.KEEP_CONTENT!==!1,X=e.IN_PLACE||!1,ke=e.ALLOWED_URI_REGEXP||Et,F=e.NAMESPACE||N,re=e.MATHML_TEXT_INTEGRATION_POINTS||re,se=e.HTML_INTEGRATION_POINTS||se,f=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&$e(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(f.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&$e(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(f.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(f.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),P&&(Ee=!1),ne&&(v=!0),k&&(m=a({},ft),d=[],k.html===!0&&(a(m,ct),a(d,ut)),k.svg===!0&&(a(m,be),a(d,Ce),a(d,ce)),k.svgFilters===!0&&(a(m,Ie),a(d,Ce),a(d,ce)),k.mathMl===!0&&(a(m,Me),a(d,mt),a(d,ce))),e.ADD_TAGS&&(m===Ue&&(m=M(m)),a(m,e.ADD_TAGS,p)),e.ADD_ATTR&&(d===Fe&&(d=M(d)),a(d,e.ADD_ATTR,p)),e.ADD_URI_SAFE_ATTR&&a(Se,e.ADD_URI_SAFE_ATTR,p),e.FORBID_CONTENTS&&(U===Ye&&(U=M(U)),a(U,e.FORBID_CONTENTS,p)),Ae&&(m["#text"]=!0),w&&a(m,["html","head","body"]),m.table&&(a(m,["tbody"]),delete Y.tbody),e.TRUSTED_TYPES_POLICY){if(typeof e.TRUSTED_TYPES_POLICY.createHTML!="function")throw K('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof e.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw K('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');g=e.TRUSTED_TYPES_POLICY,B=g.createHTML("")}else g===void 0&&(g=fn(Q,T)),g!==null&&typeof B=="string"&&(B=g.createHTML(""));S&&S(e),H=e}},qe=a({},[...be,...Ie,...Jt]),Ke=a({},[...Me,...Qt]),Gt=function(e){let t=ee(e);(!t||!t.tagName)&&(t={namespaceURI:F,tagName:"template"});const n=fe(e.tagName),c=fe(t.tagName);return Oe[e.namespaceURI]?e.namespaceURI===ae?t.namespaceURI===N?n==="svg":t.namespaceURI===ie?n==="svg"&&(c==="annotation-xml"||re[c]):!!qe[n]:e.namespaceURI===ie?t.namespaceURI===N?n==="math":t.namespaceURI===ae?n==="math"&&se[c]:!!Ke[n]:e.namespaceURI===N?t.namespaceURI===ae&&!se[c]||t.namespaceURI===ie&&!re[c]?!1:!Ke[n]&&(Ut[n]||!qe[n]):!!(j==="application/xhtml+xml"&&Oe[e.namespaceURI]):!1},D=function(e){$(o.removed,{element:e});try{ee(e).removeChild(e)}catch(t){yt(e)}},z=function(e,t){try{$(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(n){$(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),e==="is")if(v||ne)try{D(t)}catch(n){}else try{t.setAttribute(e,"")}catch(n){}},Ze=function(e){let t=null,n=null;if(he)e="<remove></remove>"+e;else{const u=lt(e,/^[\r\n\t ]+/);n=u&&u[0]}j==="application/xhtml+xml"&&F===N&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const c=g?g.createHTML(e):e;if(F===N)try{t=new Rt().parseFromString(c,j)}catch(u){}if(!t||!t.documentElement){t=me.createDocument(F,"template",null);try{t.documentElement.innerHTML=Re?B:c}catch(u){}}const _=t.body||t.documentElement;return e&&n&&_.insertBefore(l.createTextNode(n),_.childNodes[0]||null),F===N?It.call(t,w?"html":"body")[0]:w?t.documentElement:_},Je=function(e){return Nt.call(e.ownerDocument||e,e,G.SHOW_ELEMENT|G.SHOW_COMMENT|G.SHOW_TEXT|G.SHOW_PROCESSING_INSTRUCTION|G.SHOW_CDATA_SECTION,null)},Le=function(e){return e instanceof St&&(typeof e.nodeName!="string"||typeof e.textContent!="string"||typeof e.removeChild!="function"||!(e.attributes instanceof At)||typeof e.removeAttribute!="function"||typeof e.setAttribute!="function"||typeof e.namespaceURI!="string"||typeof e.insertBefore!="function"||typeof e.hasChildNodes!="function")},Qe=function(e){return typeof ue=="function"&&e instanceof ue};function b(i,e,t){le(i,n=>{n.call(o,e,t,H)})}const et=function(e){let t=null;if(b(h.beforeSanitizeElements,e,null),Le(e))return D(e),!0;const n=p(e.nodeName);if(b(h.uponSanitizeElement,e,{tagName:n,allowedTags:m}),te&&e.hasChildNodes()&&!Qe(e.firstElementChild)&&A(/<[/\w!]/g,e.innerHTML)&&A(/<[/\w!]/g,e.textContent)||e.nodeType===J.progressingInstruction||te&&e.nodeType===J.comment&&A(/<[/\w]/g,e.data))return D(e),!0;if(!m[n]||Y[n]){if(!Y[n]&&nt(n)&&(f.tagNameCheck instanceof RegExp&&A(f.tagNameCheck,n)||f.tagNameCheck instanceof Function&&f.tagNameCheck(n)))return!1;if(Ae&&!U[n]){const c=ee(e)||e.parentNode,_=Dt(e)||e.childNodes;if(_&&c){const u=_.length;for(let O=u-1;O>=0;--O){const I=Ot(_[O],!0);I.__removalCount=(e.__removalCount||0)+1,c.insertBefore(I,Lt(e))}}}return D(e),!0}return e instanceof Pe&&!Gt(e)||(n==="noscript"||n==="noembed"||n==="noframes")&&A(/<\/no(script|embed|frames)/i,e.innerHTML)?(D(e),!0):(P&&e.nodeType===J.text&&(t=e.textContent,le([pe,de,Te],c=>{t=q(t,c," ")}),e.textContent!==t&&($(o.removed,{element:e.cloneNode()}),e.textContent=t)),b(h.afterSanitizeElements,e,null),!1)},tt=function(e,t,n){if(We&&(t==="id"||t==="name")&&(n in l||n in zt))return!1;if(!(Ee&&!_e[t]&&A(Ct,t))){if(!(He&&A(wt,t))){if(!d[t]||_e[t]){if(!(nt(e)&&(f.tagNameCheck instanceof RegExp&&A(f.tagNameCheck,e)||f.tagNameCheck instanceof Function&&f.tagNameCheck(e))&&(f.attributeNameCheck instanceof RegExp&&A(f.attributeNameCheck,t)||f.attributeNameCheck instanceof Function&&f.attributeNameCheck(t))||t==="is"&&f.allowCustomizedBuiltInElements&&(f.tagNameCheck instanceof RegExp&&A(f.tagNameCheck,n)||f.tagNameCheck instanceof Function&&f.tagNameCheck(n))))return!1}else if(!Se[t]){if(!A(ke,q(n,ve,""))){if(!((t==="src"||t==="xlink:href"||t==="href")&&e!=="script"&&$t(n,"data:")===0&&Xe[e])){if(!(ze&&!A(xt,q(n,ve,"")))){if(n)return!1}}}}}}return!0},nt=function(e){return e!=="annotation-xml"&&lt(e,Pt)},ot=function(e){b(h.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Le(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:d,forceKeepAttr:void 0};let c=t.length;for(;c--;){const _=t[c],{name:u,namespaceURI:O,value:I}=_,V=p(u),De=I;let E=u==="value"?De:qt(De);if(n.attrName=V,n.attrValue=E,n.keepAttr=!0,n.forceKeepAttr=void 0,b(h.uponSanitizeAttribute,e,n),E=n.attrValue,Be&&(V==="id"||V==="name")&&(z(u,e),E=vt+E),te&&A(/((--!?|])>)|<\/(style|title)/i,E)){z(u,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){z(u,e);continue}if(!Ge&&A(/\/>/i,E)){z(u,e);continue}P&&le([pe,de,Te],at=>{E=q(E,at," ")});const it=p(e.nodeName);if(!tt(it,V,E)){z(u,e);continue}if(g&&typeof Q=="object"&&typeof Q.getAttributeType=="function"&&!O)switch(Q.getAttributeType(it,V)){case"TrustedHTML":{E=g.createHTML(E);break}case"TrustedScriptURL":{E=g.createScriptURL(E);break}}if(E!==De)try{O?e.setAttributeNS(O,u,E):e.setAttribute(u,E),Le(e)?D(e):st(o.removed)}catch(at){z(u,e)}}b(h.afterSanitizeAttributes,e,null)},Wt=function i(e){let t=null;const n=Je(e);for(b(h.beforeSanitizeShadowDOM,e,null);t=n.nextNode();)b(h.uponSanitizeShadowNode,t,null),et(t),ot(t),t.content instanceof C&&i(t.content);b(h.afterSanitizeShadowDOM,e,null)};return o.sanitize=function(i){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=null,n=null,c=null,_=null;if(Re=!i,Re&&(i="<!-->"),typeof i!="string"&&!Qe(i))if(typeof i.toString=="function"){if(i=i.toString(),typeof i!="string")throw K("dirty is not a string, aborting")}else throw K("toString is not a function");if(!o.isSupported)return i;if(ge||ye(e),o.removed=[],typeof i=="string"&&(X=!1),X){if(i.nodeName){const I=p(i.nodeName);if(!m[I]||Y[I])throw K("root node is forbidden and cannot be sanitized in-place")}}else if(i instanceof ue)t=Ze("<!---->"),n=t.ownerDocument.importNode(i,!0),n.nodeType===J.element&&n.nodeName==="BODY"||n.nodeName==="HTML"?t=n:t.appendChild(n);else{if(!v&&!P&&!w&&i.indexOf("<")===-1)return g&&oe?g.createHTML(i):i;if(t=Ze(i),!t)return v?null:oe?B:""}t&&he&&D(t.firstChild);const u=Je(X?i:t);for(;c=u.nextNode();)et(c),ot(c),c.content instanceof C&&Wt(c.content);if(X)return i;if(v){if(ne)for(_=bt.call(t.ownerDocument);t.firstChild;)_.appendChild(t.firstChild);else _=t;return(d.shadowroot||d.shadowrootmode)&&(_=Mt.call(s,_,!0)),_}let O=w?t.outerHTML:t.innerHTML;return w&&m["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&A(gt,t.ownerDocument.doctype.name)&&(O="<!DOCTYPE "+t.ownerDocument.doctype.name+`>
`+O),P&&le([pe,de,Te],I=>{O=q(O,I," ")}),g&&oe?g.createHTML(O):O},o.setConfig=function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};ye(i),ge=!0},o.clearConfig=function(){H=null,ge=!1},o.isValidAttribute=function(i,e,t){H||ye({});const n=p(i),c=p(e);return tt(n,c,t)},o.addHook=function(i,e){typeof e=="function"&&$(h[i],e)},o.removeHook=function(i,e){if(e!==void 0){const t=jt(h[i],e);return t===-1?void 0:Vt(h[i],t,1)[0]}return st(h[i])},o.removeHooks=function(i){h[i]=[]},o.removeAllHooks=function(){h=dt()},o}var un=ht();export{un as default};
