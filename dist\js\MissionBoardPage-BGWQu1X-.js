var U=Object.defineProperty,ee=Object.defineProperties;var se=Object.getOwnPropertyDescriptors;var O=Object.getOwnPropertySymbols;var ae=Object.prototype.hasOwnProperty,te=Object.prototype.propertyIsEnumerable;var K=(a,i,l)=>i in a?U(a,i,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[i]=l,S=(a,i)=>{for(var l in i||(i={}))ae.call(i,l)&&K(a,l,i[l]);if(O)for(var l of O(i))te.call(i,l)&&K(a,l,i[l]);return a},C=(a,i)=>ee(a,se(i));var R=(a,i,l)=>new Promise((f,h)=>{var N=o=>{try{p(l.next(o))}catch(y){h(y)}},c=o=>{try{p(l.throw(o))}catch(y){h(y)}},p=o=>o.done?f(o.value):Promise.resolve(o.value).then(N,c);p((l=l.apply(a,i)).next())});import{r as m,j as e,c as b,b as v,p as ie,u as T,v as x,A as le,h as u,m as A,w as re,e as P,y as ne,G as ce,S as oe,T as k}from"./chunk-DX4Z_LyS.js";import{U as V,s as H,d as de}from"../assets/main-CGUKzV0x.js";import{c as D}from"./chunk-D8IZ3rty.js";import{a as me}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const xe=({projectId:a,className:i="",filterByUser:l=null,filterByStatus:f=null,hideHeader:h=!1,hideStats:N=!1})=>{const{currentUser:c}=m.useContext(V),[p,o]=m.useState([]),[y,g]=m.useState([]),[w,q]=m.useState(!0),[M,E]=m.useState(""),[fe,B]=m.useState(null),[he,pe]=m.useState(!1),[n,z]=m.useState({status:"all",difficulty:"all",type:"all",assignee:"all",reward:"all"}),[_,I]=m.useState({total:0,available:0,inProgress:0,completed:0,myMissions:0}),F=()=>R(null,null,function*(){try{q(!0);let s=H.from("tasks").select(`
          *,
          assignee:assignee_id(id, display_name, full_name, email),
          project:project_id(id, name, description)
        `);a&&(s=s.eq("project_id",a));const{data:t,error:r}=yield s.order("created_at",{ascending:!1});if(r)throw r;const j=t.map(d=>C(S({},d),{missionType:d.task_type||"general",difficultyRating:d.difficulty_level||"medium",rewardOrbs:G(d),estimatedDuration:d.estimated_hours||0,skillsRequired:$(d.description),urgency:J(d),missionStatus:L(d.status),claimable:d.status==="todo"&&!d.assignee_id,progress:Q(d)}));o(j),g(j),W(j)}catch(s){D.error("Failed to load missions")}finally{q(!1)}}),G=s=>{const r={easy:1,medium:1.5,hard:2,expert:3},j=(s.estimated_hours||1)*.5;return Math.round(10*(r[s.difficulty_level]||1.5)*j)},$=s=>s?["react","javascript","typescript","node.js","python","design","ui/ux","backend","frontend","database","api","testing"].filter(j=>s.toLowerCase().includes(j.toLowerCase())).slice(0,3):[],J=s=>{const t=Math.floor((Date.now()-new Date(s.created_at))/864e5),r=s.estimated_hours||8;return t>7&&r<4?"high":t>3?"medium":"low"},L=s=>({todo:"available",in_progress:"active",review:"review",done:"completed",blocked:"blocked"})[s]||"available",Q=s=>{if(s.status==="done")return 100;if(s.status==="review")return 90;if(s.status==="in_progress"){const t=s.logged_hours||0,r=s.estimated_hours||8;return Math.min(Math.round(t/r*80),80)}return 0},W=s=>{const t={total:s.length,available:s.filter(r=>r.missionStatus==="available").length,inProgress:s.filter(r=>r.missionStatus==="active").length,completed:s.filter(r=>r.missionStatus==="completed").length,myMissions:s.filter(r=>r.assignee_id===(c==null?void 0:c.id)).length};I(t)},X=()=>{let s=[...p];if(l&&(s=s.filter(t=>t.assignee_id===l)),f){const t=L(f);s=s.filter(r=>r.missionStatus===t)}if(M){const t=M.toLowerCase();s=s.filter(r=>{var j,d;return((j=r.title)==null?void 0:j.toLowerCase().includes(t))||((d=r.description)==null?void 0:d.toLowerCase().includes(t))||r.skillsRequired.some(Z=>Z.toLowerCase().includes(t))})}!f&&n.status!=="all"&&(s=s.filter(t=>t.missionStatus===n.status)),n.difficulty!=="all"&&(s=s.filter(t=>t.difficultyRating===n.difficulty)),n.type!=="all"&&(s=s.filter(t=>t.missionType===n.type)),l||(n.assignee==="mine"?s=s.filter(t=>t.assignee_id===(c==null?void 0:c.id)):n.assignee==="unassigned"&&(s=s.filter(t=>!t.assignee_id))),g(s)},Y=s=>R(null,null,function*(){try{const{error:t}=yield H.from("tasks").update({assignee_id:c.id,status:"in_progress"}).eq("id",s);if(t)throw t;D.success("Mission claimed successfully!"),F()}catch(t){D.error("Failed to claim mission")}});return m.useEffect(()=>{F()},[a]),m.useEffect(()=>{X()},[p,M,n]),w?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading missions..."})]})}):e.jsxs("div",{className:`mission-board ${i}`,children:[!h&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2",children:"🎯 Mission Board"}),e.jsx("p",{className:"text-default-600",children:"Discover and claim missions to earn ORBs and advance your skills"})]}),!N&&e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-6",children:[e.jsx(b,{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20",children:e.jsxs(v,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:_.total}),e.jsx("div",{className:"text-sm text-default-600",children:"Total Missions"})]})}),e.jsx(b,{className:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",children:e.jsxs(v,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:_.available}),e.jsx("div",{className:"text-sm text-default-600",children:"Available"})]})}),e.jsx(b,{className:"bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20",children:e.jsxs(v,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:_.inProgress}),e.jsx("div",{className:"text-sm text-default-600",children:"In Progress"})]})}),e.jsx(b,{className:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",children:e.jsxs(v,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:_.completed}),e.jsx("div",{className:"text-sm text-default-600",children:"Completed"})]})}),e.jsx(b,{className:"bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20",children:e.jsxs(v,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-pink-600",children:_.myMissions}),e.jsx("div",{className:"text-sm text-default-600",children:"My Missions"})]})})]}),e.jsx(b,{className:"mb-6",children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(ie,{placeholder:"Search missions by title, description, or skills...",value:M,onChange:s=>E(s.target.value),startContent:e.jsx("span",{className:"text-default-400",children:"🔍"}),className:"w-full"})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs(T,{placeholder:"Status",selectedKeys:[n.status],onSelectionChange:s=>z(C(S({},n),{status:Array.from(s)[0]})),className:"w-32",size:"sm",children:[e.jsx(x,{children:"All Status"},"all"),e.jsx(x,{children:"Available"},"available"),e.jsx(x,{children:"Active"},"active"),e.jsx(x,{children:"Review"},"review"),e.jsx(x,{children:"Completed"},"completed")]}),e.jsxs(T,{placeholder:"Difficulty",selectedKeys:[n.difficulty],onSelectionChange:s=>z(C(S({},n),{difficulty:Array.from(s)[0]})),className:"w-32",size:"sm",children:[e.jsx(x,{children:"All Levels"},"all"),e.jsx(x,{children:"Easy"},"easy"),e.jsx(x,{children:"Medium"},"medium"),e.jsx(x,{children:"Hard"},"hard"),e.jsx(x,{children:"Expert"},"expert")]}),e.jsxs(T,{placeholder:"Assignee",selectedKeys:[n.assignee],onSelectionChange:s=>z(C(S({},n),{assignee:Array.from(s)[0]})),className:"w-32",size:"sm",children:[e.jsx(x,{children:"All"},"all"),e.jsx(x,{children:"My Missions"},"mine"),e.jsx(x,{children:"Unassigned"},"unassigned")]})]})]})})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.jsx(le,{children:y.map((s,t)=>e.jsx(ue,{mission:s,index:t,onClaim:()=>Y(s.id),onView:()=>B(s),currentUser:c},s.id))})}),y.length===0&&e.jsx(b,{className:"mt-8",children:e.jsxs(v,{className:"p-8 text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎯"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No missions found"}),e.jsx("p",{className:"text-default-600 mb-4",children:M||Object.values(n).some(s=>s!=="all")?"Try adjusting your search or filters":"No missions available at the moment"}),(M||Object.values(n).some(s=>s!=="all"))&&e.jsx(u,{color:"primary",variant:"flat",onClick:()=>{E(""),z({status:"all",difficulty:"all",type:"all",assignee:"all",reward:"all"})},children:"Clear Filters"})]})})]})},ue=({mission:a,index:i,onClaim:l,onView:f,currentUser:h})=>{const N=g=>({easy:"success",medium:"warning",hard:"danger",expert:"secondary"})[g]||"default",c=g=>({available:"success",active:"primary",review:"warning",completed:"secondary",blocked:"danger"})[g]||"default",p=g=>({high:"🔥",medium:"⚡",low:"📅"})[g]||"📅",o=a.assignee_id===(h==null?void 0:h.id),y=a.claimable&&!o;return e.jsx(A.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,delay:i*.05},whileHover:{scale:1.02},className:"h-full",children:e.jsxs(b,{className:"h-full hover:shadow-lg transition-shadow duration-200",children:[e.jsx(re,{className:"pb-2",children:e.jsxs("div",{className:"flex justify-between items-start w-full",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold line-clamp-2 mb-1",children:a.title}),e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(P,{color:c(a.missionStatus),size:"sm",variant:"flat",children:a.missionStatus}),e.jsx(P,{color:N(a.difficultyRating),size:"sm",variant:"flat",children:a.difficultyRating})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:"text-sm text-default-500",children:p(a.urgency)})})]})}),e.jsxs(v,{className:"pt-0",children:[e.jsx("p",{className:"text-sm text-default-600 line-clamp-3 mb-3",children:a.description||"No description provided"}),a.skillsRequired.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-default-500 mb-1",children:"Skills Required:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:a.skillsRequired.map((g,w)=>e.jsx(P,{size:"sm",variant:"bordered",className:"text-xs",children:g},w))})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-yellow-500",children:"💰"}),e.jsxs("span",{className:"font-medium",children:[a.rewardOrbs," ORBs"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-blue-500",children:"⏱️"}),e.jsxs("span",{children:[a.estimatedDuration,"h"]})]})]}),a.missionStatus==="active"&&e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex justify-between text-xs mb-1",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[a.progress,"%"]})]}),e.jsx(ne,{value:a.progress,color:"primary",size:"sm"})]}),a.assignee&&e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(ce,{src:a.assignee.avatar_url,name:a.assignee.display_name||a.assignee.full_name,size:"sm"}),e.jsx("span",{className:"text-sm text-default-600",children:a.assignee.display_name||a.assignee.full_name||"Unknown"})]}),e.jsxs("div",{className:"flex gap-2 mt-auto",children:[e.jsx(u,{size:"sm",variant:"flat",onClick:f,className:"flex-1",children:"View Details"}),y&&e.jsx(u,{size:"sm",color:"primary",onClick:l,className:"flex-1",children:"Claim Mission"}),o&&a.missionStatus==="active"&&e.jsx(u,{size:"sm",color:"success",variant:"flat",className:"flex-1",children:"Continue"})]})]})]})})},_e=()=>{const{currentUser:a}=m.useContext(V),i=me(),[l,f]=m.useState("all-missions"),[h,N]=m.useState(null),c=()=>{i("/project/wizard")},p=()=>{f("my-missions")},o=()=>{i("/analytics/contributions")};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20",children:e.jsxs("div",{className:"flex min-h-screen",children:[e.jsxs(A.div,{initial:{x:-50,opacity:0},animate:{x:0,opacity:1},transition:{duration:.3},className:"w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-6 space-y-4",children:[e.jsx("div",{className:"text-2xl mb-4",children:"🎯"}),e.jsx(de,{size:"sm",variant:"flat",onClick:c,className:"p-3 rounded-lg hover:bg-primary/10 transition-colors",title:"Create Mission",children:"➕"}),e.jsx(u,{size:"sm",variant:"flat",onClick:p,className:"p-3 rounded-lg hover:bg-success/10 transition-colors",title:"My Missions",children:"👤"}),e.jsx(u,{size:"sm",variant:"flat",onClick:o,className:"p-3 rounded-lg hover:bg-warning/10 transition-colors",title:"Mission Analytics",children:"📊"}),e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/teams"),className:"p-3 rounded-lg hover:bg-secondary/10 transition-colors",title:"Team Missions",children:"👥"}),e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/earn"),className:"p-3 rounded-lg hover:bg-success/10 transition-colors",title:"Earnings",children:"💰"})]}),e.jsx("div",{className:"flex-1 p-6",children:e.jsxs(A.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.4,delay:.1},children:[e.jsx(b,{className:"mb-6",children:e.jsx(v,{className:"p-4",children:e.jsxs(oe,{selectedKey:l,onSelectionChange:f,variant:"underlined",color:"primary",children:[e.jsx(k,{title:"🌟 All Missions"},"all-missions"),e.jsx(k,{title:"👤 My Missions"},"my-missions"),e.jsx(k,{title:"🎯 Available"},"available"),e.jsx(k,{title:"⚡ In Progress"},"in-progress"),e.jsx(k,{title:"✅ Completed"},"completed")]})})}),e.jsx(xe,{projectId:h,className:"mission-board-container",filterByUser:l==="my-missions"?a==null?void 0:a.id:null,filterByStatus:l==="available"?"todo":l==="in-progress"?"in_progress":l==="completed"?"done":null})]})}),e.jsxs(A.div,{initial:{x:50,opacity:0},animate:{x:0,opacity:1},transition:{duration:.3,delay:.2},className:"w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-6 space-y-4",children:[e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/projects"),className:"p-3 rounded-lg hover:bg-primary/10 transition-colors",title:"All Projects",children:"📁"}),e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/contributions"),className:"p-3 rounded-lg hover:bg-warning/10 transition-colors",title:"Track Time",children:"⏱️"}),e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/validation/metrics"),className:"p-3 rounded-lg hover:bg-success/10 transition-colors",title:"Validation",children:"✅"}),e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/profile"),className:"p-3 rounded-lg hover:bg-secondary/10 transition-colors",title:"Profile",children:"👤"}),e.jsx(u,{size:"sm",variant:"flat",onClick:()=>i("/settings"),className:"p-3 rounded-lg hover:bg-default/10 transition-colors",title:"Settings",children:"⚙️"})]})]})})};export{_e as default};
