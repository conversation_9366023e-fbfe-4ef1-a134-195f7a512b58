import{j as e,m as o,c,w as m,b as l,h as n}from"./chunk-DX4Z_LyS.js";import{a as x}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";const w=()=>{const t=x(),d=[{title:"Start New Project",description:"Create a new project and set up your team",icon:"🚀",color:"from-blue-500 to-purple-600",action:()=>t("/project/wizard"),category:"Create"},{title:"Track Contribution",description:"Log your work and track time spent",icon:"⏱️",color:"from-green-500 to-emerald-600",action:()=>t("/track"),category:"Track"},{title:"View Analytics",description:"See insights and performance metrics",icon:"📊",color:"from-orange-500 to-red-600",action:()=>t("/analytics/contributions"),category:"Analyze"},{title:"Manage Revenue",description:"Track earnings and royalty distributions",icon:"💰",color:"from-yellow-500 to-orange-500",action:()=>t("/earn"),category:"Earn"},{title:"Browse Projects",description:"Explore and join existing projects",icon:"📁",color:"from-cyan-500 to-blue-600",action:()=>t("/projects"),category:"Explore"},{title:"Learning Center",description:"Tutorials and best practices",icon:"🎓",color:"from-indigo-500 to-purple-600",action:()=>t("/learn"),category:"Learn"}].reduce((i,s)=>{const r=s.category;return i[r]||(i[r]=[]),i[r].push(s),i},{});return e.jsx("div",{className:"p-6",children:e.jsx(o.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsxs(c,{className:"bg-white/10 backdrop-blur-md border-white/20",children:[e.jsx(m,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500 flex items-center justify-center",children:e.jsx("span",{className:"text-xl",children:"⚡"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Quick Actions"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Jump into your most common workflows"})]})]})}),e.jsxs(l,{children:[e.jsx("div",{className:"space-y-6",children:Object.entries(d).map(([i,s],r)=>e.jsxs(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*r},children:[e.jsx("h3",{className:"text-white/80 text-sm font-medium mb-3 uppercase tracking-wider",children:i}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map((a,h)=>e.jsx(o.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.1*r+.05*h},whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(c,{className:"bg-white/5 hover:bg-white/10 border-white/10 hover:border-white/20 transition-all duration-300 cursor-pointer group",onClick:a.action,children:e.jsx(l,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${a.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`,children:e.jsx("span",{className:"text-xl",children:a.icon})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-white font-medium mb-1 group-hover:text-white/90 transition-colors",children:a.title}),e.jsx("p",{className:"text-white/60 text-sm leading-relaxed",children:a.description})]}),e.jsx("div",{className:"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:e.jsx("svg",{className:"w-5 h-5 text-white/60",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})})})},a.title))})]},i))}),e.jsxs(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.6},className:"mt-8 pt-6 border-t border-white/10",children:[e.jsx("h3",{className:"text-white/80 text-sm font-medium mb-4 uppercase tracking-wider",children:"Quick Links"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(n,{size:"sm",variant:"flat",className:"bg-white/10 text-white hover:bg-white/20",onClick:()=>t("/settings"),children:"Settings"}),e.jsx(n,{size:"sm",variant:"flat",className:"bg-white/10 text-white hover:bg-white/20",onClick:()=>t("/profile"),children:"Profile"}),e.jsx(n,{size:"sm",variant:"flat",className:"bg-white/10 text-white hover:bg-white/20",onClick:()=>t("/notifications"),children:"Notifications"}),e.jsx(n,{size:"sm",variant:"flat",className:"bg-white/10 text-white hover:bg-white/20",onClick:()=>t("/bugs"),children:"Report Bug"})]})]})]})]})})})};export{w as default};
