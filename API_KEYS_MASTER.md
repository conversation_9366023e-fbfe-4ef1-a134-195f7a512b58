# 🔑 API Keys Master Document
**⚠️ DEVELOPMENT ONLY - DO NOT COMMIT TO PUBLIC REPOS**

This document contains all API keys and secrets needed for Royaltea development. For production deployment, these should be set as environment variables in Netlify.

---

## 🚀 Quick Setup Instructions

1. **Copy this file** to `client/.env.local` (for frontend)
2. **Fill in your actual API keys** (replace placeholder values)
3. **Never commit** the actual `.env.local` file to version control
4. **For production**: Set these as environment variables in Netlify dashboard

---

## 📋 Current API Keys Status

### ✅ **CONFIGURED SERVICES**
- [x] Supabase (Database & Auth) - ✅ URL + Anon Key
- [x] Google OAuth (Authentication) - ✅ WORKING (secrets in Supabase dashboard)
- [x] GitHub OAuth (Authentication) - ✅ WORKING (secrets in Supabase dashboard)
- [x] Google Analytics (Tracking) - ✅ Tracking ID
- [x] Teller (Payment Processing) - ✅ Certificates Added (replacing Plaid)
- [ ] Email Service (Notifications) - ⚠️ Need SMTP Credentials

### 🔄 **SERVICES TO ADD**
- [ ] OpenAI/Claude (AI Features)
- [ ] Discord (Team Communication)
- [ ] Slack (Team Communication)
- [ ] AWS S3 (File Storage)
- [ ] Jira/Trello (Project Management)
- [ ] Twilio (SMS Notifications)
- [ ] Push Notifications
- [ ] Social Media APIs

### ✅ **TELLER INTEGRATION COMPLETE**
- [x] Teller Integration - ✅ Certificates configured
- [x] Update payment functions to use Teller API - ✅ Complete
- [x] Update frontend components to use Teller - ✅ Complete
- [x] Update all documentation to reference Teller - ✅ Complete
- [ ] Test Teller integration end-to-end - ⚠️ Ready for testing

---

## 🔧 Environment Variables Template

```bash
# ==========================================
# CORE INFRASTRUCTURE
# ==========================================

# Supabase Configuration (REQUIRED)
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKmain-aYNTaVsZ.js:113 ✅ Supabase client initialized successfully
main-aYNTaVsZ.js:314 [Loading] Visibility tracking initialized
main-aYNTaVsZ.js:2768 🚨 GLOBAL TEST: JavaScript is executing!
main-aYNTaVsZ.js:2768 🚨 CRITICAL DEBUG: main.jsx file is executing!
main-aYNTaVsZ.js:2768 🚨 CRITICAL DEBUG: React import: Object
main-aYNTaVsZ.js:2768 🚨 CRITICAL DEBUG: createRoot import: ƒ (f,h){if(!Ly(f))throw Error(r(299));var k=!1,H="",X=GE;return h!=null&&(h.unstable_strictMode===!0&&(k=!0),h.identifierPrefix!==void 0&&(H=h.identifierPrefix),h.onRecoverableError!==void 0&&(X=h.onRe…
main-aYNTaVsZ.js:2768 🔍 DEBUG: main.jsx executing - React should be working
main-aYNTaVsZ.js:2768 🔍 DEBUG: Attempting to create React root...
main-aYNTaVsZ.js:2768 🔍 DEBUG: React root created successfully: rg
main-aYNTaVsZ.js:2768 🔍 DEBUG: Attempting to render React app...
main-aYNTaVsZ.js:2768 🔍 DEBUG: React app render call completed
main-aYNTaVsZ.js:2768 Current location: /
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/
main-aYNTaVsZ.js:2768 🔍 APP.JSX: isLoading state changed to: true
main-aYNTaVsZ.js:2768 🔍 APP.JSX: Loading triggered from: Error
    at https://royalty.technology/assets/main-aYNTaVsZ.js:2768:21408
    at YN (https://royalty.technology/assets/main-aYNTaVsZ.js:41:25046)
    at Ah (https://royalty.technology/assets/main-aYNTaVsZ.js:41:43848)
    at https://royalty.technology/assets/main-aYNTaVsZ.js:41:42018
    at S (https://royalty.technology/assets/main-aYNTaVsZ.js:26:1615)
    at MessagePort.P (https://royalty.technology/assets/main-aYNTaVsZ.js:26:1980)
main-aYNTaVsZ.js:113 Auth state changed: SIGNED_IN 93cbbbed-2772-4922-b7d7-d07fdc1aa62b
main-aYNTaVsZ.js:113 User signed in: <EMAIL>
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2768 🔍 APP.JSX: isLoading state changed to: false
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
(index):65 Realtime setup error: tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance
console.error @ (index):65
main-aYNTaVsZ.js:111 WebSocket connection to 'wss://hqqlrrqvjcetoxbdjgzx.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ&eventsPerSecond=10&vsn=1.0.0' failed: WebSocket is closed before the connection is established.
disconnect @ main-aYNTaVsZ.js:111
main-aYNTaVsZ.js:113 Auth state changed: INITIAL_SESSION 93cbbbed-2772-4922-b7d7-d07fdc1aa62b
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:111 WebSocket connection to 'wss://hqqlrrqvjcetoxbdjgzx.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ&eventsPerSecond=10&vsn=1.0.0' failed: WebSocket is closed before the connection is established.
disconnect @ main-aYNTaVsZ.js:111
(index):65 Error fetching analytics overview: SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON
console.error @ (index):65
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /start
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Showing loading indicator, isLoading: false localLoading: true
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /start
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /start
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/start
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /start
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:883 Visibility handler initialized, current state: visible
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - saveProject (Saving project data (step 1)) [1 active]
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - saveProject (407ms, success) [0 active]
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/2
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/2
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/2
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/2
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/2
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (729ms, success) [0 active]
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - saveProject (Saving project data (step 2)) [1 active]
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - saveProject (177ms, success) [0 active]
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/3
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/3
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/3
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/3
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/3
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (455ms, success) [0 active]
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - saveProject (Saving project data (step 3)) [1 active]
hqqlrrqvjcetoxbdjgzx.supabase.co/rest/v1/rpc/check_column_exists:1  Failed to load resource: the server responded with a status of 404 ()
(index):73 Could not check if model_schema column exists: Object
console.warn @ (index):73
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - saveProject (342ms, success) [0 active]
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/4
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/4
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/4
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/4
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/4
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/4
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (395ms, success) [0 active]
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - saveProject (Saving project data (step 4)) [1 active]
main-aYNTaVsZ.js:1979 💾 Saving revenue tranches: Array(1)
main-aYNTaVsZ.js:1979 🗑️ Deleting existing revenue tranches for project: 2f2e9473-2122-406a-8f8e-8608bd71b60b
main-aYNTaVsZ.js:1979 ✅ Successfully deleted existing revenue tranches
main-aYNTaVsZ.js:1979 💾 Saving tranche 1: Object
main-aYNTaVsZ.js:1979 📤 Inserting tranche data: Object
main-aYNTaVsZ.js:1979 ✅ Successfully created revenue tranche: Array(1)
main-aYNTaVsZ.js:1979 🎉 All revenue tranches saved successfully
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - saveProject (222ms, success) [0 active]
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/5
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/5
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/5
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/5
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/5
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (382ms, success) [0 active]
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - saveProject (Saving project data (step 5)) [1 active]
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - saveProject (73ms, success) [0 active]
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/6
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/6
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/6
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/6
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/6
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (380ms, success) [0 active]
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:883 Window blurred
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:883 Window focused
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:883 Window blurred
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:883 Window focused
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:883 Window blurred
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:883 Window focused
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:883 Window blurred
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window focus: false -> true
main-aYNTaVsZ.js:883 Window focused
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - saveProject (Saving project data (step 6)) [1 active]
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - saveProject (879ms, success) [0 active]
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
(index):73 Select: Keys "standard" passed to "selectedKeys" are not present in the collection.
console.warn @ (index):73
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:2768 Current location: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
main-aYNTaVsZ.js:2768 Current URL before fix: https://royalty.technology/#/project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
(index):73 Select: Keys "standard" passed to "selectedKeys" are not present in the collection.
console.warn @ (index):73
main-aYNTaVsZ.js:1859 🔄 regenerateAgreement called with: Object
main-aYNTaVsZ.js:1859 🔄 regenerateAgreement called with: Object
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (432ms, success) [0 active]
main-aYNTaVsZ.js:1859 🔄 regenerateAgreement called with: Object
hqqlrrqvjcetoxbdjgzx.supabase.co/rest/v1/project_contributors?select=*%2Cusers%28id%2Cemail%2Cdisplay_name%2Caddress%2Cstate%2Ccounty%2Ctitle%29&project_id=eq.2f2e9473-2122-406a-8f8e-8608bd71b60b:1  Failed to load resource: the server responded with a status of 400 ()
(index):65 Error generating agreement: Object
console.error @ (index):65
hqqlrrqvjcetoxbdjgzx.supabase.co/rest/v1/project_contributors?select=*%2Cusers%28id%2Cemail%2Cdisplay_name%2Caddress%2Cstate%2Ccounty%2Ctitle%29&project_id=eq.2f2e9473-2122-406a-8f8e-8608bd71b60b:1  Failed to load resource: the server responded with a status of 400 ()
(index):65 Error generating agreement: Object
console.error @ (index):65
hqqlrrqvjcetoxbdjgzx.supabase.co/rest/v1/project_contributors?select=*%2Cusers%28id%2Cemail%2Cdisplay_name%2Caddress%2Cstate%2Ccounty%2Ctitle%29&project_id=eq.2f2e9473-2122-406a-8f8e-8608bd71b60b:1  Failed to load resource: the server responded with a status of 400 ()
(index):65 Error generating agreement: Object
console.error @ (index):65
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Window blur: true -> false
main-aYNTaVsZ.js:883 Window blurred
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:2072 Uncaught TypeError: Failed to execute 'getComputedStyle' on 'Window': parameter 1 is not of type 'Element'.
    at Nhe (main-aYNTaVsZ.js:88:42554)
    at Object.g [as current] (main-aYNTaVsZ.js:88:44005)
    at i (main-aYNTaVsZ.js:88:43091)
    at o (main-aYNTaVsZ.js:2072:6998)
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:2072 Uncaught TypeError: Failed to execute 'getComputedStyle' on 'Window': parameter 1 is not of type 'Element'.
    at Nhe (main-aYNTaVsZ.js:88:42554)
    at Object.g [as current] (main-aYNTaVsZ.js:88:44005)
    at i (main-aYNTaVsZ.js:88:43091)
    at o (main-aYNTaVsZ.js:2072:6998)
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:2072 Uncaught TypeError: Failed to execute 'getComputedStyle' on 'Window': parameter 1 is not of type 'Element'.
    at Nhe (main-aYNTaVsZ.js:88:42554)
    at Object.g [as current] (main-aYNTaVsZ.js:88:44005)
    at i (main-aYNTaVsZ.js:88:43091)
    at o (main-aYNTaVsZ.js:2072:6998)
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Visibility changed: false -> false
main-aYNTaVsZ.js:883 Visibility changed to: hidden
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
main-aYNTaVsZ.js:314 [Loading] Visibility changed: false -> true
main-aYNTaVsZ.js:883 Visibility changed to: visible
main-aYNTaVsZ.js:113 Auth state changed: SIGNED_IN 93cbbbed-2772-4922-b7d7-d07fdc1aa62b
main-aYNTaVsZ.js:113 User signed in: <EMAIL>
main-aYNTaVsZ.js:2768 🔍 DEBUG: Routes component rendering for path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/create route matched!
main-aYNTaVsZ.js:2768 🔍 DEBUG: /project/wizard route matched!
main-aYNTaVsZ.js:2753 🔍 DEBUG: AuthRoute executing for path: /
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: User authenticated, rendering protected content for /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Cleaning up timer
main-aYNTaVsZ.js:314 [Loading] START: ProjectWizard - fetchProject (Loading project 2f2e9473-2122-406a-8f8e-8608bd71b60b) [1 active]
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth state - authenticated
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Current path: /project/wizard/2f2e9473-2122-406a-8f8e-8608bd71b60b/step/7
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state
main-aYNTaVsZ.js:111 WebSocket connection to 'wss://hqqlrrqvjcetoxbdjgzx.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ&eventsPerSecond=10&vsn=1.0.0' failed: WebSocket is closed before the connection is established.
disconnect @ main-aYNTaVsZ.js:111
(anonymous) @ main-aYNTaVsZ.js:111
o @ main-aYNTaVsZ.js:2
Promise.then
l @ main-aYNTaVsZ.js:2
(anonymous) @ main-aYNTaVsZ.js:2
ue @ main-aYNTaVsZ.js:2
removeChannel @ main-aYNTaVsZ.js:111
removeChannel @ main-aYNTaVsZ.js:113
(anonymous) @ main-aYNTaVsZ.js:2753
GN @ main-aYNTaVsZ.js:41
I_ @ main-aYNTaVsZ.js:41
Ah @ main-aYNTaVsZ.js:41
LE @ main-aYNTaVsZ.js:41
se @ main-aYNTaVsZ.js:39
KC @ main-aYNTaVsZ.js:41
Xb @ main-aYNTaVsZ.js:41
qv @ main-aYNTaVsZ.js:41
S @ main-aYNTaVsZ.js:26
P @ main-aYNTaVsZ.js:26
main-aYNTaVsZ.js:2753 🔍 AUTH ROUTE: Timer complete, setting local loading to false
main-aYNTaVsZ.js:314 [Loading] END: ProjectWizard - fetchProject (682ms, success) [0 active]
main-aYNTaVsZ.js:1859 🔄 regenerateAgreement called with: {projectId: '2f2e9473-2122-406a-8f8e-8608bd71b60b', projectData: 'present', projectName: 'agrement test', agreementTemplate: 'loaded'}
main-aYNTaVsZ.js:111  GET https://hqqlrrqvjcetoxbdjgzx.supabase.co/rest/v1/project_contributors?select=*%2Cusers%28id%2Cemail%2Cdisplay_name%2Caddress%2Cstate%2Ccounty%2Ctitle%29&project_id=eq.2f2e9473-2122-406a-8f8e-8608bd71b60b 400 (Bad Request)
(anonymous) @ main-aYNTaVsZ.js:111
(anonymous) @ main-aYNTaVsZ.js:111
l @ main-aYNTaVsZ.js:111
Promise.then
c @ main-aYNTaVsZ.js:111
(anonymous) @ main-aYNTaVsZ.js:111
dye @ main-aYNTaVsZ.js:111
(anonymous) @ main-aYNTaVsZ.js:111
then @ main-aYNTaVsZ.js:106
(index):65 Error generating agreement: {code: 'PGRST200', details: "Searched for a foreign key relationship between 'p…n the schema 'public', but no matches were found.", hint: null, message: "Could not find a relationship between 'project_contributors' and 'users' in the schema cache"}
console.error @ (index):65
overrideMethod @ hook.js:608
kMe @ main-aYNTaVsZ.js:2072
(anonymous) @ main-aYNTaVsZ.js:1865
o @ main-aYNTaVsZ.js:2
Promise.then
l @ main-aYNTaVsZ.js:2
o @ main-aYNTaVsZ.js:2
Promise.then
l @ main-aYNTaVsZ.js:2
(anonymous) @ main-aYNTaVsZ.js:2
ue @ main-aYNTaVsZ.js:2
M @ main-aYNTaVsZ.js:1859
(anonymous) @ main-aYNTaVsZ.js:1865
YN @ main-aYNTaVsZ.js:41
Ah @ main-aYNTaVsZ.js:41
(anonymous) @ main-aYNTaVsZ.js:41
S @ main-aYNTaVsZ.js:26
P @ main-aYNTaVsZ.js:26
main-aYNTaVsZ.js:314 Activity logging temporarily disabled for testing
n42a_brChpKQ
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs
SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co

# Database Direct Access
supabase_database_password=E6welg22749i5QFG

# Site Configuration
SITE_URL=https://royalty.technology
NODE_ENV=development
VITE_DEBUG=false

# ==========================================
# AUTHENTICATION & OAUTH
# ==========================================

# Google OAuth (CONFIGURED - Working via Supabase)
# Note: OAuth secrets are stored in Supabase dashboard, not environment variables
GOOGLE_CLIENT_ID=807950544313-4gq4f6jrvpv8vjl28gblfclqbhdr5fcg.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=managed_by_supabase_dashboard

# GitHub OAuth (CONFIGURED - Working via Supabase)
# Note: OAuth secrets are stored in Supabase dashboard, not environment variables
GITHUB_CLIENT_ID=Ov23li1gCLryRzy97ZgL
# GITHUB_CLIENT_SECRET=managed_by_supabase_dashboard

# Additional OAuth Providers (OPTIONAL)
DISCORD_CLIENT_ID=1382570089655832676
DISCORD_CLIENT_SECRET=FRfjQv5eUV4CblFLgTh4l1IsFBm8LkF0
DISCORD_APPLICATION_ID=1382570089655832676
DISCORD_PUBLIC_KEY=30eaf1bf336ef52113d69486b6268f46271426966b55e73db0a3761c14fba72b
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here

# ==========================================
# PAYMENT PROCESSING
# ==========================================

# Teller Configuration (PRIMARY PAYMENT PROCESSOR)
# Note: Teller uses certificate-based authentication
TELLER_APPLICATION_ID=app_pelk82mrrofp6upddo000
TELLER_ENVIRONMENT=sandbox
TELLER_WEBHOOK_URL=https://royalty.technology/.netlify/functions/teller-webhook
TELLER_CERTIFICATE_PATH=./teller/certificate.pem
TELLER_PRIVATE_KEY_PATH=./teller/private_key.pem



# Payment Security
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_here
PAYMENT_ENCRYPTION_KEY=your_encryption_key_here

# Stripe (OPTIONAL - Alternative Payment Processor)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# ==========================================
# AI & MACHINE LEARNING
# ==========================================

# OpenAI (For AI Features)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here

# Anthropic Claude (Alternative AI)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google AI/Gemini
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# ==========================================
# COMMUNICATION & NOTIFICATIONS
# ==========================================

# Email Service (CONFIGURED)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your_email_username_here
EMAIL_PASS=your_email_password_here
EMAIL_FROM=<EMAIL>

# Twilio (SMS & WhatsApp)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# Discord Bot (Team Communication)
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_discord_guild_id_here

# Slack Integration
SLACK_BOT_TOKEN=your_slack_bot_token_here
SLACK_SIGNING_SECRET=your_slack_signing_secret_here
SLACK_CLIENT_ID=your_slack_client_id_here
SLACK_CLIENT_SECRET=your_slack_client_secret_here

# Email Service (SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
EMAIL_FROM=<EMAIL>

# Push Notifications
VAPID_PUBLIC_KEY=your_vapid_public_key_here
VAPID_PRIVATE_KEY=your_vapid_private_key_here
VAPID_SUBJECT=mailto:<EMAIL>

# ==========================================
# ANALYTICS & MONITORING
# ==========================================

# Google Analytics (CONFIGURED)
VITE_GA_TRACKING_ID=G-S7SFML469V
GA_MEASUREMENT_ID=G-S7SFML469V

# Additional Analytics
MIXPANEL_TOKEN=your_mixpanel_token_here
AMPLITUDE_API_KEY=your_amplitude_api_key_here
HOTJAR_ID=your_hotjar_id_here

# Error Monitoring
SENTRY_DSN=your_sentry_dsn_here
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# ==========================================
# FILE STORAGE & CDN
# ==========================================

# AWS S3 (File Storage)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=royaltea-files

# Cloudinary (Image/Video Processing)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name_here
CLOUDINARY_API_KEY=your_cloudinary_api_key_here
CLOUDINARY_API_SECRET=your_cloudinary_api_secret_here

# ==========================================
# PROJECT MANAGEMENT INTEGRATIONS
# ==========================================

# Jira Integration
JIRA_BASE_URL=your_jira_instance_url_here
JIRA_EMAIL=<EMAIL>
JIRA_API_TOKEN=ATATT3xFfGF0ve7GFFNDs32jP4K8_Q1qcOYPBZtUk-xrdPsp7nlpVjw7KsAitCQY5W9uK_D6CgnE69HnBdiEaaIsDCTxLJk99cXZ02CTHcgBrs24GQoSrn2t0rG8Gu2ApkKKimTYa54rFeKQe0j4OiXFyTT5mceJvqI3pbpzdUMgo9HFBPlH_jc=19C505FF

# Trello Integration
TRELLO_API_KEY=********************************
TRELLO_TOKEN=8287f6477cd651c80e52b3fa419af75e26a6704e3dd6a5ccea3c7ea1ed22c4ff

# GitHub Integration (Beyond OAuth)
GITHUB_PERSONAL_ACCESS_TOKEN=your_github_pat_here

# ==========================================
# SOCIAL MEDIA & EXTERNAL APIs
# ==========================================

# Twitter/X API
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# LinkedIn API
LINKEDIN_API_KEY=your_linkedin_api_key_here
LINKEDIN_API_SECRET=your_linkedin_api_secret_here

# ==========================================
# DEVELOPMENT & TESTING
# ==========================================

# Testing Keys (Use sandbox/test versions)
TEST_TELLER_APPLICATION_ID=your_test_teller_app_id_here
TEST_TELLER_CERTIFICATE_PATH=./teller/test_certificate.pem
TEST_STRIPE_KEY=your_test_stripe_key_here

# Local Development
VITE_LOCAL_API_URL=http://localhost:8888/.netlify/functions
LOCAL_SUPABASE_URL=http://localhost:54321
LOCAL_SUPABASE_ANON_KEY=your_local_supabase_anon_key_here
```

> **⚠️ IMPORTANT NOTES:**
> - **OAuth Authentication**: Google & GitHub OAuth are WORKING! Secrets are managed in Supabase dashboard, not env vars
> - **Supabase Anon Key**: This is your actual working key from the project
> - **OAuth Client IDs**: These are real and properly configured
> - **Teller Integration**: Certificates are configured! Teller offers better dev experience + 100 free API calls
> - **Payment Migration**: Moving from Plaid to Teller for better testing and free tier
> - **Local Development**: The "dummy-secret" values in supabase/config.toml are just for local dev

---

## 📚 Where to Get Each API Key

### **Core Infrastructure**
- **Supabase**: [Dashboard](https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/settings/api)
- **Netlify**: [Site Settings](https://app.netlify.com/sites/royalty-technology/settings/env)

### **Authentication**
- **Google OAuth**: [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
- **GitHub OAuth**: [GitHub Developer Settings](https://github.com/settings/developers)
- **Discord OAuth**: [Discord Developer Portal](https://discord.com/developers/applications)

### **Payments**
- **Teller**: [Teller Console](https://teller.io/console) - Certificate-based auth (already configured)
- **Stripe**: [Stripe Dashboard](https://dashboard.stripe.com/apikeys)

### **AI Services**
- **OpenAI**: [OpenAI API Keys](https://platform.openai.com/api-keys)
- **Anthropic**: [Anthropic Console](https://console.anthropic.com/)
- **Google AI**: [Google AI Studio](https://makersuite.google.com/app/apikey)

### **Communication**
- **Twilio**: [Twilio Console](https://console.twilio.com/)
- **Discord Bot**: [Discord Developer Portal](https://discord.com/developers/applications)
- **Slack**: [Slack API](https://api.slack.com/apps)

### **Analytics & Monitoring**
- **Google Analytics**: [GA4 Admin](https://analytics.google.com/analytics/web/)
- **Sentry**: [Sentry Settings](https://sentry.io/settings/)
- **Mixpanel**: [Mixpanel Settings](https://mixpanel.com/settings/project)

### **File Storage**
- **AWS S3**: [AWS IAM Console](https://console.aws.amazon.com/iam/)
- **Cloudinary**: [Cloudinary Console](https://cloudinary.com/console)

### **Project Management**
- **Jira**: [Atlassian API Tokens](https://id.atlassian.com/manage-profile/security/api-tokens)
- **Trello**: [Trello Developer API Keys](https://trello.com/app-key)

---

## 🔒 Security Best Practices

1. **Never commit** actual API keys to version control
2. **Use environment variables** for all sensitive data
3. **Rotate keys regularly** (especially for production)
4. **Use least privilege** - only grant necessary permissions
5. **Monitor usage** - set up alerts for unusual API activity
6. **Use different keys** for development vs production
7. **Store backup copies** securely (password manager, encrypted files)

---

## 🚀 Production Deployment

For production deployment on Netlify, set these environment variables in:
**Netlify Dashboard → Site Settings → Environment Variables**

**Required for Production:**
- All Supabase keys
- Plaid production keys
- Email service credentials
- Google Analytics ID
- OAuth client secrets

---

## 📞 Support & Resources

- **Supabase Support**: [docs.supabase.com](https://docs.supabase.com)
- **Teller Support**: [teller.io/docs](https://teller.io/docs)
- **Netlify Support**: [docs.netlify.com](https://docs.netlify.com)
- **Project Issues**: [GitHub Issues](https://github.com/CityOfGamers/royaltea/issues)

---

**Last Updated**: December 2024  
**Maintained By**: Development Team  
**Status**: Active Development
