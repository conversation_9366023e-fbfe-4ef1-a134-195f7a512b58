# Netlify SPA routing
# These redirects are for client-side routing (SPA)

# Explicitly handle auth callback route
/auth/callback    /index.html   200!

# Specific redirects for common routes
/profile/*    /index.html   200
/admin/*      /index.html   200
/debug/*      /index.html   200
/settings     /index.html   200
/project/create /index.html   200
/start        /index.html   200
/track        /index.html   200
/earn         /index.html   200
/learn        /index.html   200
/contribution/* /index.html   200
/validation/*   /index.html   200
/analytics/*    /index.html   200

# Redirect all other routes to index.html with a 200 status code
/*    /index.html   200
