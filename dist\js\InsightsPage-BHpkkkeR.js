import{r as j,j as e,m as i,c as n,b as r,S as b,T as c,y as g,e as l,h as d,w as v}from"./chunk-DX4Z_LyS.js";import"./chunk-Cai8ouo_.js";const w=()=>{const[a,o]=j.useState("predictions"),m=[{title:"Revenue Forecast",prediction:"Your earnings are projected to increase by 35% next month",confidence:87,trend:"up",details:"Based on current contribution patterns and project momentum"},{title:"Optimal Work Schedule",prediction:"You're most productive between 9-11 AM and 2-4 PM",confidence:92,trend:"neutral",details:"Analysis of 3 months of time tracking data"},{title:"Skill Development",prediction:"Focus on UI/UX skills for maximum royalty impact",confidence:78,trend:"up",details:"Market demand analysis and your current skill set"}],x=[{type:"productivity",title:"Increase Task Granularity",description:"Break down large tasks into smaller 2-4 hour chunks for better tracking accuracy",impact:"High",effort:"Low"},{type:"collaboration",title:"Join Design Reviews",description:"Participate in weekly design reviews to increase your project influence",impact:"Medium",effort:"Medium"},{type:"skills",title:"Learn React Native",description:"Mobile development skills are in high demand for your current projects",impact:"High",effort:"High"}],h=[{area:"Time Allocation",current:"Frontend: 60%, Backend: 40%",suggested:"Frontend: 70%, Backend: 30%",reason:"Frontend tasks have 25% higher royalty multiplier",savings:"+$450/month"},{area:"Project Selection",current:"2 active projects",suggested:"3 active projects",reason:"Diversification reduces risk and increases opportunities",savings:"+$200/month"}],p=t=>{switch(t){case"up":return"📈";case"down":return"📉";case"neutral":return"➡️";default:return"📊"}},u=t=>{switch(t.toLowerCase()){case"high":return"success";case"medium":return"warning";case"low":return"default";default:return"default"}};return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-violet-900",children:[e.jsx(i.div,{className:"relative z-10 pt-8 pb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsx("div",{className:"container mx-auto px-6",children:e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(i.div,{className:"text-6xl mb-4",animate:{scale:[1,1.1,1],rotate:[0,10,-10,0]},transition:{duration:3,repeat:1/0,repeatType:"reverse"},children:"🤖"}),e.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:"AI Insights"}),e.jsx("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Powered by machine learning to optimize your contributions and maximize earnings."})]})})}),e.jsx("div",{className:"container mx-auto px-6 pb-12",children:e.jsxs(i.div,{className:"max-w-6xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:[e.jsx(n,{className:"mb-8 bg-white/5 border border-white/10",children:e.jsx(r,{className:"p-6",children:e.jsxs(b,{selectedKey:a,onSelectionChange:o,variant:"underlined",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-gradient-to-r from-purple-500 to-violet-500",tab:"max-w-fit px-0 h-12",tabContent:"group-data-[selected=true]:text-white text-white/70"},children:[e.jsx(c,{title:"🔮 Predictions"},"predictions"),e.jsx(c,{title:"💡 Recommendations"},"recommendations"),e.jsx(c,{title:"⚡ Optimization"},"optimization"),e.jsx(c,{title:"📊 Forecasting"},"forecasting")]})})}),e.jsxs(i.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:[a==="predictions"&&e.jsx("div",{className:"space-y-6",children:m.map((t,s)=>e.jsx(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:s*.1},children:e.jsx(n,{className:"bg-white/5 border border-white/10",children:e.jsxs(r,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"text-3xl",children:p(t.trend)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:t.title}),e.jsx("p",{className:"text-white/70",children:t.details})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-white/70 mb-1",children:"Confidence"}),e.jsxs("div",{className:"text-2xl font-bold text-purple-400",children:[t.confidence,"%"]})]})]}),e.jsx("div",{className:"bg-white/5 rounded-lg p-4 mb-4",children:e.jsx("p",{className:"text-white text-lg",children:t.prediction})}),e.jsx(g,{value:t.confidence,color:"secondary",className:"max-w-full",label:"AI Confidence Level"})]})})},s))}),a==="recommendations"&&e.jsx("div",{className:"space-y-6",children:x.map((t,s)=>e.jsx(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:s*.1},children:e.jsx(n,{className:"bg-white/5 border border-white/10",children:e.jsxs(r,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:t.title}),e.jsx("p",{className:"text-white/80",children:t.description})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(l,{size:"sm",color:u(t.impact),variant:"flat",children:[t.impact," Impact"]}),e.jsxs(l,{size:"sm",variant:"flat",className:"bg-white/10 text-white",children:[t.effort," Effort"]})]})]}),e.jsx(d,{className:"bg-purple-500 hover:bg-purple-600 text-white",children:"Implement Suggestion"})]})})},s))}),a==="optimization"&&e.jsx("div",{className:"space-y-6",children:h.map((t,s)=>e.jsx(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:s*.1},children:e.jsxs(n,{className:"bg-white/5 border border-white/10",children:[e.jsx(v,{className:"pb-3",children:e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:t.area}),e.jsx(l,{color:"success",variant:"flat",className:"text-green-400",children:t.savings})]})}),e.jsxs(r,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-white/70 mb-1",children:"Current"}),e.jsx("div",{className:"text-white",children:t.current})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-white/70 mb-1",children:"Suggested"}),e.jsx("div",{className:"text-green-400 font-medium",children:t.suggested})]})]}),e.jsxs("div",{className:"bg-white/5 rounded-lg p-3",children:[e.jsx("div",{className:"text-sm text-white/70 mb-1",children:"Reasoning"}),e.jsx("p",{className:"text-white",children:t.reason})]}),e.jsx(d,{className:"bg-green-500 hover:bg-green-600 text-white",children:"Apply Optimization"})]})]})},s))}),a==="forecasting"&&e.jsx(n,{className:"bg-white/5 border border-white/10",children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"📊"}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Advanced Forecasting"}),e.jsx("p",{className:"text-white/70 mb-6",children:"Detailed revenue and contribution forecasting models coming soon"}),e.jsx(d,{className:"bg-purple-500 hover:bg-purple-600 text-white",children:"Request Beta Access"})]})})})]},a)]})})]})};export{w as default};
