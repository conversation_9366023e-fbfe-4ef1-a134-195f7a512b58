import { test, expect } from '@playwright/test';

test.describe('Agreement Generation System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the production site
    await page.goto('https://royalty.technology');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');

    // Wait for login to complete
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  });

  test('should test agreement generation functionality', async ({ page }) => {
    console.log('🧪 Testing Agreement Generation System');

    // Take initial screenshot
    await page.screenshot({ path: 'initial-dashboard.png', fullPage: true });

    // Look for any existing projects or navigation to project wizard
    const startButton = page.locator('text=Start, button:has-text("Start")');
    const projectsLink = page.locator('text=Projects, a:has-text("Projects")');
    const createButton = page.locator('text=Create, button:has-text("Create")');

    console.log('🔍 Looking for navigation elements...');

    if (await startButton.count() > 0) {
      console.log('✅ Found Start button');
      await startButton.first().click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Take screenshot after clicking Start
      await page.screenshot({ path: 'after-start-click.png', fullPage: true });

    } else if (await projectsLink.count() > 0) {
      console.log('✅ Found Projects link');
      await projectsLink.first().click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

    } else if (await createButton.count() > 0) {
      console.log('✅ Found Create button');
      await createButton.first().click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }

    // Look for project creation or existing projects
    const createProjectButton = page.locator('text=Create New Project, button:has-text("Create Project"), text=New Project');
    const existingProjects = page.locator('[data-testid="project-card"], .project-card, .project-item');

    if (await createProjectButton.count() > 0) {
      console.log('✅ Found Create Project button');
      await createProjectButton.first().click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Fill in basic project info
      const nameField = page.locator('input[name="name"], input[placeholder*="name"], input[id*="name"]');
      const descField = page.locator('textarea[name="description"], textarea[placeholder*="description"]');

      if (await nameField.count() > 0) {
        await nameField.first().fill('Agreement Test Project');
        console.log('✅ Filled project name');
      }

      if (await descField.count() > 0) {
        await descField.first().fill('Testing agreement generation functionality');
        console.log('✅ Filled project description');
      }

      // Look for Next button and navigate through wizard
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');

      for (let step = 0; step < 8; step++) {
        if (await nextButton.count() > 0) {
          console.log(`📋 Step ${step + 1}: Clicking Next`);
          await nextButton.first().click();
          await page.waitForTimeout(2000);

          // Check if we've reached the agreement page
          const agreementContent = page.locator('.agreement-content, .agreement-text, [data-testid="agreement"]');
          const signatureCanvas = page.locator('canvas, [data-testid="signature-canvas"]');
          const templateSelector = page.locator('select, [data-testid="template-selector"]');

          if (await agreementContent.count() > 0 || await signatureCanvas.count() > 0 || await templateSelector.count() > 0) {
            console.log('🎯 Reached agreement page!');
            break;
          }
        } else {
          console.log(`⚠️ No Next button found at step ${step + 1}`);
          break;
        }
      }

    } else if (await existingProjects.count() > 0) {
      console.log('✅ Found existing projects');
      await existingProjects.first().click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }

    // Take screenshot of current page
    await page.screenshot({ path: 'current-page-state.png', fullPage: true });

    // Test agreement generation functionality
    await testAgreementGeneration(page);
  });
});

async function testAgreementGeneration(page) {
    console.log('🔧 Testing Agreement Generation Components');

    // Look for agreement-related elements
    const agreementText = page.locator('.agreement-content, .agreement-text, [data-testid="agreement-text"]');
    const signatureCanvas = page.locator('canvas, [data-testid="signature-canvas"], .signature-canvas');
    const templateSelector = page.locator('select, [data-testid="template-selector"], .template-selector');
    const generateButton = page.locator('button:has-text("Generate"), button:has-text("Regenerate")');
    const nameField = page.locator('input[placeholder*="name"], input[id*="name"]');

    // Test 1: Check for agreement text
    if (await agreementText.count() > 0) {
      const textContent = await agreementText.first().textContent();
      console.log('✅ Agreement text found, length:', textContent?.length || 0);

      if (textContent && textContent.length > 1000) {
        console.log('✅ Agreement appears to be substantial');

        // Check for key legal sections
        const keyTerms = ['CONTRIBUTOR AGREEMENT', 'Definitions', 'Work Product', 'Schedule'];
        let foundTerms = 0;

        for (const term of keyTerms) {
          if (textContent.includes(term)) {
            console.log(`✅ Found legal section: ${term}`);
            foundTerms++;
          }
        }

        if (foundTerms >= 2) {
          console.log('✅ Agreement contains expected legal sections');
        } else {
          console.log('⚠️ Agreement may be incomplete - few legal sections found');
        }
      } else {
        console.log('⚠️ Agreement text seems short or incomplete');
      }
    } else {
      console.log('❌ No agreement text found');
    }

    // Test 2: Check signature functionality
    if (await signatureCanvas.count() > 0) {
      console.log('✅ Signature canvas found');

      // Try to interact with signature canvas
      const canvas = signatureCanvas.first();
      const box = await canvas.boundingBox();

      if (box) {
        // Draw a simple signature
        await page.mouse.move(box.x + 50, box.y + 30);
        await page.mouse.down();
        await page.mouse.move(box.x + 150, box.y + 50);
        await page.mouse.up();
        console.log('✅ Drew test signature on canvas');
      }
    } else {
      console.log('❌ No signature canvas found');
    }

    // Test 3: Check template selector
    if (await templateSelector.count() > 0) {
      console.log('✅ Template selector found');

      // Try to interact with template selector
      await templateSelector.first().click();
      await page.waitForTimeout(500);

      const options = await page.locator('option, [role="option"]').allTextContents();
      console.log('Template options:', options);
    } else {
      console.log('❌ No template selector found');
    }

    // Test 4: Check name field
    if (await nameField.count() > 0) {
      console.log('✅ Name field found');
      await nameField.first().fill('Test User Signature');
      console.log('✅ Filled name field');
    } else {
      console.log('❌ No name field found');
    }

    // Test 5: Check generate/regenerate button
    if (await generateButton.count() > 0) {
      console.log('✅ Generate/Regenerate button found');

      // Click the button to test regeneration
      await generateButton.first().click();
      await page.waitForTimeout(3000);
      console.log('✅ Clicked generate/regenerate button');

      // Check for any changes or loading indicators
      const loadingIndicator = page.locator('.loading, .spinner, text=/generating/i');
      if (await loadingIndicator.count() > 0) {
        console.log('✅ Loading indicator appeared');
      }
    } else {
      console.log('❌ No generate/regenerate button found');
    }

    // Take final screenshot
    await page.screenshot({ path: 'agreement-generation-test-complete.png', fullPage: true });

    // Basic assertion - at least one agreement component should be present
    const hasAgreementComponents = (await agreementText.count() > 0) ||
                                  (await signatureCanvas.count() > 0) ||
                                  (await templateSelector.count() > 0);

    expect(hasAgreementComponents).toBe(true);
    console.log('✅ Agreement generation test completed');
}
