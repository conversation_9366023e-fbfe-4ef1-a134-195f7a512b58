var v=(r,u,a)=>new Promise((s,x)=>{var b=t=>{try{n(a.next(t))}catch(o){x(o)}},h=t=>{try{n(a.throw(t))}catch(o){x(o)}},n=t=>t.done?s(t.value):Promise.resolve(t.value).then(b,h);n((a=a.apply(r,u)).next())});import{r as l,j as e,c,b as d,x as T,h as p,m as U,w as _}from"./chunk-DX4Z_LyS.js";import{U as q,s as k,y as A}from"../assets/main-CGUKzV0x.js";import{c as D,a as L}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";import"./chunk-BiNxGM8y.js";const H=()=>{var g,w;const{id:r}=D(),u=L(),{currentUser:a}=l.useContext(q),[s,x]=l.useState(null),[b,h]=l.useState(!0),[n,t]=l.useState(null),[o,C]=l.useState(!1);l.useEffect(()=>{r&&a&&f()},[r,a]);const f=()=>v(null,null,function*(){try{h(!0),t(null);const{data:{session:i}}=yield k.auth.getSession(),y=i==null?void 0:i.access_token;if(!y)throw new Error("Authentication required");const m=yield fetch(`/.netlify/functions/companies/${r}`,{headers:{Authorization:`Bearer ${y}`,"Content-Type":"application/json"}});if(!m.ok){if(m.status===404)throw new Error("Company not found");if(m.status===403)throw new Error("You do not have permission to manage this company");{const B=yield m.json();throw new Error(B.error||"Failed to fetch company data")}}const E=yield m.json();x(E.company);const{data:N,error:S}=yield k.from("teams").select(`
          id,
          team_members!inner(user_id, is_admin)
        `).eq("company_id",r).eq("team_members.user_id",a.id).eq("team_members.is_admin",!0);S||C(N&&N.length>0)}catch(i){t(i.message),A.error(i.message)}finally{h(!1)}}),j=()=>{u("/teams")};return b?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:e.jsx(c,{className:"bg-white/10 backdrop-blur-md",children:e.jsxs(d,{className:"p-8 text-center",children:[e.jsx(T,{size:"lg",color:"primary"}),e.jsx("p",{className:"text-white mt-4",children:"Loading company data..."})]})})}):n?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:e.jsx(c,{className:"bg-white/10 backdrop-blur-md max-w-md",children:e.jsxs(d,{className:"p-8 text-center",children:[e.jsx("span",{className:"text-6xl mb-4 block",children:"❌"}),e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Company"}),e.jsx("p",{className:"text-white/60 mb-4",children:n}),e.jsxs("div",{className:"flex gap-2 justify-center",children:[e.jsx(p,{onClick:j,className:"bg-white/20 text-white hover:bg-white/30",children:"Back to Studios"}),e.jsx(p,{onClick:f,color:"primary",children:"Try Again"})]})]})})}):s?e.jsx(U.div,{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx(p,{onClick:j,className:"mb-4 bg-white/10 text-white hover:bg-white/20",startContent:e.jsx("span",{children:"←"}),children:"Back to Studios"}),e.jsx(c,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsx(_,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"🏢"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:s.legal_name}),e.jsxs("p",{className:"text-white/60",children:[(g=s.company_type)==null?void 0:g.toUpperCase()," • ",s.tax_id]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("div",{className:`px-3 py-1 rounded-full text-sm font-medium ${s.compliance_status==="active"?"bg-green-500/20 text-green-300":"bg-yellow-500/20 text-yellow-300"}`,children:(w=s.compliance_status)==null?void 0:w.toUpperCase()})})]})})})]}),e.jsxs(c,{className:"bg-white/10 backdrop-blur-md border-white/20",children:[e.jsx(_,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xl",children:"📋"}),e.jsx("h2",{className:"text-lg font-semibold text-white",children:"Business Entity Details"})]})}),e.jsx(d,{className:"pt-0",children:e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("p",{className:"text-gray-600",children:"Company details component temporarily unavailable."}),e.jsxs("p",{className:"text-sm text-gray-500 mt-2",children:["Company: ",(s==null?void 0:s.legal_name)||"Unknown"]})]})})]}),!o&&e.jsx(c,{className:"bg-yellow-500/10 backdrop-blur-md border-yellow-500/20 mt-6",children:e.jsx(d,{className:"p-4",children:e.jsxs("div",{className:"flex items-center gap-2 text-yellow-300",children:[e.jsx("span",{children:"⚠️"}),e.jsx("p",{className:"text-sm",children:"You have read-only access to this company. Contact an studio administrator to make changes."})]})})})]})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:e.jsx(c,{className:"bg-white/10 backdrop-blur-md max-w-md",children:e.jsxs(d,{className:"p-8 text-center",children:[e.jsx("span",{className:"text-6xl mb-4 block",children:"🏢"}),e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Company Not Found"}),e.jsx("p",{className:"text-white/60 mb-4",children:"The requested company could not be found."}),e.jsx(p,{onClick:j,className:"bg-white/20 text-white hover:bg-white/30",children:"Back to Studios"})]})})})};export{H as default};
