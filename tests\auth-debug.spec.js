import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TestPassword123!';

test('Debug Authentication State', async ({ page }) => {
  console.log('🔍 Starting authentication debug...');
  
  // Go to the site
  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  console.log('📍 Current URL:', page.url());
  
  // Check if we're already authenticated
  const isOnDashboard = page.url().includes('/dashboard') || page.url() === PRODUCTION_URL + '/';
  console.log('🏠 On dashboard/home:', isOnDashboard);
  
  if (!isOnDashboard) {
    console.log('🔐 Need to authenticate...');

    // Look for Sign In button
    const signInButton = page.locator('text="Sign In"').first();
    const signInVisible = await signInButton.isVisible();
    console.log('👆 Sign In button visible:', signInVisible);

    if (signInVisible) {
      await signInButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }

    // Look for Log In button
    const logInButton = page.locator('text="Log In"');
    const logInVisible = await logInButton.isVisible();
    console.log('👆 Log In button visible:', logInVisible);

    if (logInVisible) {
      console.log('📝 Filling in credentials...');
      await page.fill('input[type="email"]', TEST_EMAIL);
      await page.fill('input[type="password"]', TEST_PASSWORD);

      console.log('🔑 Clicking Log In button...');
      await logInButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(5000); // Wait longer for auth to complete

      console.log('📍 After login URL:', page.url());

      // Check if login was successful by looking for auth token
      const postLoginAuthState = await page.evaluate(() => {
        const supabaseKeys = Object.keys(localStorage).filter(key => key.includes('supabase') || key.includes('sb-'));
        return {
          hasSupabaseToken: supabaseKeys.length > 0,
          supabaseKeys: supabaseKeys,
          currentUrl: window.location.href
        };
      });

      console.log('🔍 Post-login auth state:', JSON.stringify(postLoginAuthState, null, 2));
    }
  }
  
  console.log('📍 After auth URL:', page.url());
  
  // Check authentication state in browser console
  const authState = await page.evaluate(() => {
    // Check if React context is available
    const reactFiberKey = Object.keys(document.querySelector('#root')).find(key => key.startsWith('__reactFiber'));
    if (reactFiberKey) {
      console.log('React fiber found');
    }
    
    // Check localStorage for auth tokens
    const supabaseAuth = localStorage.getItem('sb-ixqjqjqjqjqjqjqjqj-auth-token');
    
    return {
      hasSupabaseToken: !!supabaseAuth,
      currentUrl: window.location.href,
      localStorage: Object.keys(localStorage),
      sessionStorage: Object.keys(sessionStorage)
    };
  });
  
  console.log('🔍 Auth state:', JSON.stringify(authState, null, 2));
  
  // Try to navigate to dashboard
  console.log('🧭 Navigating to dashboard...');
  await page.goto(PRODUCTION_URL + '/dashboard');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  console.log('📍 Dashboard URL:', page.url());
  
  // Check if we're redirected to login
  const redirectedToLogin = page.url().includes('/login');
  console.log('🔄 Redirected to login:', redirectedToLogin);
  
  if (redirectedToLogin) {
    console.log('❌ Authentication failed - redirected to login');
  } else {
    console.log('✅ Authentication successful - on dashboard');
  }
  
  // Try to navigate to /start
  console.log('🧭 Navigating to /start...');
  await page.goto(PRODUCTION_URL + '/start');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  console.log('📍 Start page URL:', page.url());
  const startRedirectedToLogin = page.url().includes('/login');
  console.log('🔄 Start redirected to login:', startRedirectedToLogin);
  
  // Get console logs
  const consoleLogs = [];
  page.on('console', msg => {
    if (msg.type() === 'log' && msg.text().includes('AUTH')) {
      consoleLogs.push(msg.text());
    }
  });
  
  // Wait a bit more to capture any auth logs
  await page.waitForTimeout(2000);
  
  console.log('📝 Auth-related console logs:');
  consoleLogs.forEach(log => console.log('  ', log));
  
  // Take a screenshot for debugging
  await page.screenshot({ path: 'auth-debug-screenshot.png', fullPage: true });
  console.log('📸 Screenshot saved as auth-debug-screenshot.png');
});
