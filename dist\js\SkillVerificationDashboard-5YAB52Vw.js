var he=Object.defineProperty,ue=Object.defineProperties;var xe=Object.getOwnPropertyDescriptors;var le=Object.getOwnPropertySymbols;var pe=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable;var ce=(l,r,s)=>r in l?he(l,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):l[r]=s,P=(l,r)=>{for(var s in r||(r={}))pe.call(r,s)&&ce(l,s,r[s]);if(le)for(var s of le(r))fe.call(r,s)&&ce(l,s,r[s]);return l},M=(l,r)=>ue(l,xe(r));var j=(l,r,s)=>new Promise((m,c)=>{var g=_=>{try{C(s.next(_))}catch(f){c(f)}},y=_=>{try{C(s.throw(_))}catch(f){c(f)}},C=_=>_.done?m(_.value):Promise.resolve(_.value).then(g,y);C((s=s.apply(l,r)).next())});import{r as p,j as e,C as G,l as O,o as H,n as te,S as ne,T as V,E as ie,h as N,p as ve,u as ae,v as X,m as F,c as R,b as L,e as E,y as Z,F as Q,G as W,a4 as ge,a5 as je,w as oe}from"./chunk-DX4Z_LyS.js";import{s as B,U as ye,g as be,h as we,i as Ne}from"../assets/main-CGUKzV0x.js";import{c as A}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const _e=()=>{const{currentUser:l}=useAuth(),[r,s]=p.useState(!1),[m,c]=p.useState(null);p.useEffect(()=>{c(null)},[l]);const g=p.useCallback(x=>j(null,null,function*(){if(!l)throw new Error("User not authenticated");s(!0),c(null);try{const{data:n,error:o}=yield supabase.from("vetting_applications").insert(M(P({user_id:l.id},x),{status:"pending",submitted_at:new Date().toISOString()})).select().single();if(o)throw o;return yield supabase.from("vetting_workflow_logs").insert({application_id:n.id,user_id:l.id,action:"application_submitted",to_status:"pending",details:{application_type:x.application_type}}),n}catch(n){throw c(n.message),n}finally{s(!1)}}),[supabase,l]),y=p.useCallback((x=null)=>j(null,null,function*(){const n=x||(l==null?void 0:l.id);if(!n)throw new Error("User ID required");s(!0),c(null);try{const{data:o,error:v}=yield supabase.from("vetting_applications").select(`
          *,
          reviews:vetting_reviews(
            *,
            reviewer:reviewer_id(full_name, avatar_url)
          )
        `).eq("user_id",n).order("created_at",{ascending:!1});if(v)throw v;return o||[]}catch(o){throw c(o.message),o}finally{s(!1)}}),[supabase,l]),C=p.useCallback((x=null)=>j(null,null,function*(){const n=x||(l==null?void 0:l.id);if(!n)throw new Error("Reviewer ID required");s(!0),c(null);try{const{data:o,error:v}=yield supabase.from("reviewer_assignments").select(`
          *,
          application:application_id(
            *,
            user:user_id(full_name, avatar_url, email),
            reviews:vetting_reviews(*)
          )
        `).eq("reviewer_id",n).is("completed_at",null).order("assigned_at",{ascending:!0});if(v)throw v;return(o==null?void 0:o.map(a=>a.application))||[]}catch(o){throw c(o.message),o}finally{s(!1)}}),[supabase,l]),_=p.useCallback((x,n)=>j(null,null,function*(){if(!l)throw new Error("User not authenticated");s(!0),c(null);try{const{data:o,error:v}=yield supabase.from("vetting_reviews").insert(M(P({application_id:x,reviewer_id:l.id},n),{review_completed_at:new Date().toISOString(),is_final_review:!0})).select().single();if(v)throw v;const{error:a}=yield supabase.from("reviewer_assignments").update({completed_at:new Date().toISOString()}).eq("application_id",x).eq("reviewer_id",l.id);if(a)throw a;let h="under_review";n.decision==="approve"?h="approved":n.decision==="reject"&&(h="rejected");const{error:t}=yield supabase.from("vetting_applications").update(P({status:h,reviewed_at:new Date().toISOString()},h==="approved"&&{approved_at:new Date().toISOString()})).eq("id",x);if(t)throw t;const{error:i}=yield supabase.from("vetting_workflow_logs").insert({application_id:x,user_id:l.id,action:"review_completed",to_status:h,details:{review_id:o.id,decision:n.decision}});if(i)throw i;return o}catch(o){throw c(o.message),o}finally{s(!1)}}),[supabase,l]),f=p.useCallback((x=null)=>j(null,null,function*(){s(!0),c(null);try{let n=supabase.from("vetting_criteria").select("*").eq("is_active",!0).order("weight",{ascending:!1});x&&(n=n.eq("application_type",x));const{data:o,error:v}=yield n;if(v)throw v;return o||[]}catch(n){throw c(n.message),n}finally{s(!1)}}),[supabase]),T=p.useCallback((x=null)=>j(null,null,function*(){const n=x||(l==null?void 0:l.id);if(!n)throw new Error("User ID required");s(!0),c(null);try{const{data:o,error:v}=yield supabase.from("verification_badges").select("*").eq("user_id",n).eq("is_visible",!0).order("display_order",{ascending:!0});if(v)throw v;return o||[]}catch(o){throw c(o.message),o}finally{s(!1)}}),[supabase,l]),u=p.useCallback((x,n)=>j(null,null,function*(){if(!l)throw new Error("User not authenticated");s(!0),c(null);try{const{data:o,error:v}=yield supabase.from("verification_badges").insert(P({user_id:x,verified_by:l.id,verified_at:new Date().toISOString()},n)).select().single();if(v)throw v;return o}catch(o){throw c(o.message),o}finally{s(!1)}}),[supabase,l]),S=p.useCallback((x=null)=>j(null,null,function*(){const n=x||(l==null?void 0:l.id);if(!n)throw new Error("User ID required");s(!0),c(null);try{const{data:o,error:v}=yield supabase.from("skill_assessments").select("*").eq("user_id",n).order("created_at",{ascending:!1});if(v)throw v;return o||[]}catch(o){throw c(o.message),o}finally{s(!1)}}),[supabase,l]),w=p.useCallback(x=>j(null,null,function*(){if(!l)throw new Error("User not authenticated");s(!0),c(null);try{const{data:n,error:o}=yield supabase.from("skill_assessments").upsert(P({user_id:l.id},x),{onConflict:"user_id,skill_name,assessment_type"}).select().single();if(o)throw o;return n}catch(n){throw c(n.message),n}finally{s(!1)}}),[supabase,l]),q=p.useCallback(()=>j(null,null,function*(){s(!0),c(null);try{const{data:x,error:n}=yield supabase.from("vetting_applications").select("status").then(({data:t,error:i})=>{if(i)throw i;const b={};return t==null||t.forEach(z=>{b[z.status]=(b[z.status]||0)+1}),{data:b,error:null}});if(n)throw n;const{data:o,error:v}=yield supabase.rpc("get_average_review_time");if(v)throw v;const{data:a,error:h}=yield supabase.from("reviewer_assignments").select("reviewer_id").is("completed_at",null).then(({data:t,error:i})=>{if(i)throw i;const b={};return t==null||t.forEach(z=>{b[z.reviewer_id]=(b[z.reviewer_id]||0)+1}),{data:b,error:null}});if(h)throw h;return{statusCounts:x||{},averageReviewTime:o||0,reviewerWorkload:a||{}}}catch(x){throw c(x.message),x}finally{s(!1)}}),[supabase]),I=p.useCallback((x=null)=>j(null,null,function*(){const n=x||(l==null?void 0:l.id);if(!n)return!1;try{const{data:o,error:v}=yield supabase.from("vetting_applications").select("status").eq("user_id",n).eq("status","approved").limit(1);if(v)throw v;return o&&o.length>0}catch(o){return!1}}),[supabase,l]);return{loading:r,error:m,submitApplication:g,getUserApplications:y,getAssignedApplications:C,submitReview:_,getVettingCriteria:f,getUserBadges:T,awardBadge:u,getSkillAssessments:S,submitSkillAssessment:w,getVettingStats:q,isUserVerified:I}};var se={};class ke{constructor(){this.apiKey=se.REACT_APP_LINKEDIN_LEARNING_API_KEY,this.baseUrl="https://api.linkedin.com/v2/learningAssets",this.authUrl="https://www.linkedin.com/oauth/v2/authorization",this.tokenUrl="https://www.linkedin.com/oauth/v2/accessToken",this.clientId=se.REACT_APP_LINKEDIN_CLIENT_ID,this.clientSecret=se.REACT_APP_LINKEDIN_CLIENT_SECRET,this.redirectUri=se.REACT_APP_LINKEDIN_REDIRECT_URI}initiateOAuth(r,s=null){const m=new URLSearchParams({response_type:"code",client_id:this.clientId,redirect_uri:this.redirectUri,scope:"r_liteprofile r_emailaddress w_member_social learning_api",state:s||`user_${r}_${Date.now()}`}),c=`${this.authUrl}?${m.toString()}`;window.location.href=c}exchangeCodeForToken(r,s){return j(this,null,function*(){try{const m=yield fetch(this.tokenUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"authorization_code",code:r,redirect_uri:this.redirectUri,client_id:this.clientId,client_secret:this.clientSecret})});if(!m.ok)throw new Error("Failed to exchange code for token");const c=yield m.json();return yield this.storeUserToken(s,c),c}catch(m){throw m}})}storeUserToken(r,s){return j(this,null,function*(){try{const m=this.extractUserIdFromState(r),{error:c}=yield B.from("user_integrations").upsert({user_id:m,integration_type:"linkedin_learning",access_token:s.access_token,refresh_token:s.refresh_token,expires_at:new Date(Date.now()+s.expires_in*1e3).toISOString(),integration_data:s,is_active:!0},{onConflict:"user_id,integration_type"});if(c)throw c}catch(m){throw m}})}getUserToken(r){return j(this,null,function*(){try{const{data:s,error:m}=yield B.from("user_integrations").select("*").eq("user_id",r).eq("integration_type","linkedin_learning").eq("is_active",!0).single();if(m)throw m;return s&&new Date(s.expires_at)<=new Date?yield this.refreshToken(s):s}catch(s){return null}})}refreshToken(r){return j(this,null,function*(){try{const s=yield fetch(this.tokenUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"refresh_token",refresh_token:r.refresh_token,client_id:this.clientId,client_secret:this.clientSecret})});if(!s.ok)throw new Error("Failed to refresh token");const m=yield s.json(),{data:c,error:g}=yield B.from("user_integrations").update({access_token:m.access_token,refresh_token:m.refresh_token||r.refresh_token,expires_at:new Date(Date.now()+m.expires_in*1e3).toISOString(),integration_data:P(P({},r.integration_data),m)}).eq("id",r.id).select().single();if(g)throw g;return c}catch(s){throw s}})}searchCourses(m){return j(this,arguments,function*(r,s={}){try{const c=new URLSearchParams({q:"search",keywords:r,count:s.count||25,start:s.start||0});s.difficulty&&c.append("difficulty",s.difficulty),s.duration&&c.append("duration",s.duration),s.language&&c.append("language",s.language);const g=yield fetch(`${this.baseUrl}?${c.toString()}`,{headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"}});if(!g.ok)throw new Error("Failed to search courses");const y=yield g.json();return this.formatCourseData(y.elements||[])}catch(c){throw c}})}getCourseDetails(r){return j(this,null,function*(){try{const s=yield fetch(`${this.baseUrl}/${r}`,{headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"}});if(!s.ok)throw new Error("Failed to get course details");const m=yield s.json();return this.formatCourseData([m])[0]}catch(s){throw s}})}getUserProgress(r,s=null){return j(this,null,function*(){try{const m=yield this.getUserToken(r);if(!m)throw new Error("User not connected to LinkedIn Learning");let c="https://api.linkedin.com/v2/learningProgress";s&&(c+=`?q=learningAsset&learningAsset=${s}`);const g=yield fetch(c,{headers:{Authorization:`Bearer ${m.access_token}`,"Content-Type":"application/json"}});if(!g.ok)throw new Error("Failed to get user progress");return(yield g.json()).elements||[]}catch(m){throw m}})}trackCompletion(r,s,m){return j(this,null,function*(){try{const{data:c,error:g}=yield B.from("learning_progress").upsert({user_id:r,course_id:s,course_provider:"linkedin_learning",completion_percentage:m.percentage||100,completed_at:m.completed_at||new Date().toISOString(),time_spent_minutes:m.time_spent_minutes||0,progress_data:m},{onConflict:"user_id,course_id,course_provider"});if(g)throw g;return yield this.updateSkillProgress(r,s,m),c}catch(c){throw c}})}updateSkillProgress(r,s,m){return j(this,null,function*(){try{const{data:c,error:g}=yield B.from("course_skill_mappings").select("*").eq("course_id",s);if(g)throw g;for(const y of c||[])yield B.from("skill_assessments").upsert({user_id:r,skill_name:y.skill_name,skill_category:y.skill_category,assessment_type:"course_completion",score:m.percentage||100,verification_level:"self_reported",assessment_data:{course_id:s,course_provider:"linkedin_learning",completion_data:m}},{onConflict:"user_id,skill_name,assessment_type"})}catch(c){}})}getRecommendedCourses(m){return j(this,arguments,function*(r,s=[]){try{const{data:c,error:g}=yield B.from("skill_assessments").select("skill_name, skill_category, score").eq("user_id",r);if(g)throw g;const{data:y,error:C}=yield B.from("learning_progress").select("course_id").eq("user_id",r).eq("course_provider","linkedin_learning").gte("completion_percentage",80);if(C)throw C;const _=(y==null?void 0:y.map(u=>u.course_id))||[],f=[];for(const u of s){const w=(yield this.searchCourses(u.skill_name,{difficulty:u.target_level||"intermediate",count:5})).filter(q=>!_.includes(q.id));f.push(...w)}return f.filter((u,S,w)=>S===w.findIndex(q=>q.id===u.id)).slice(0,10)}catch(c){throw c}})}formatCourseData(r){return r.map(s=>{var m,c,g,y,C,_,f,T,u;return{id:s.urn||s.id,title:((c=(m=s.title)==null?void 0:m.localized)==null?void 0:c.en_US)||s.title,description:((y=(g=s.description)==null?void 0:g.localized)==null?void 0:y.en_US)||s.description,duration:s.duration,difficulty:s.difficultyLevel,instructor:(_=(C=s.authors)==null?void 0:C[0])==null?void 0:_.name,thumbnail:(T=(f=s.images)==null?void 0:f[0])==null?void 0:T.url,url:s.url,skills:s.skills||[],language:((u=s.locale)==null?void 0:u.language)||"en",publishedAt:s.publishedAt,updatedAt:s.lastModifiedAt,rating:s.rating,provider:"linkedin_learning"}})}extractUserIdFromState(r){const s=r.match(/user_([^_]+)_/);return s?s[1]:null}isUserConnected(r){return j(this,null,function*(){try{return!!(yield this.getUserToken(r))}catch(s){return!1}})}disconnectUser(r){return j(this,null,function*(){try{const{error:s}=yield B.from("user_integrations").update({is_active:!1}).eq("user_id",r).eq("integration_type","linkedin_learning");if(s)throw s;return!0}catch(s){throw s}})}}const Se=new ke,Ce=({isOpen:l,onClose:r,currentUser:s,userSkills:m,onUpdate:c})=>{const[g,y]=p.useState(!0),[C,_]=p.useState("recommended"),[f,T]=p.useState(""),[u,S]=p.useState("all"),[w,q]=p.useState({recommendedCourses:[],learningPaths:[],inProgressCourses:[],completedCourses:[],categories:[]});p.useEffect(()=>{l&&I()},[l]);const I=()=>j(null,null,function*(){try{y(!0);const i=m.map($=>$.name),b=yield Se.getRecommendedCourses(i);q({recommendedCourses:b,learningPaths:[{id:"path_1",title:"Frontend Development Mastery",description:"Complete path from beginner to expert in modern frontend development",courses:8,duration:"40 hours",difficulty:"Intermediate",skills:["React","TypeScript","CSS","JavaScript"],progress:25,category:"Development"},{id:"path_2",title:"Full-Stack JavaScript",description:"Master both frontend and backend JavaScript development",courses:12,duration:"60 hours",difficulty:"Advanced",skills:["Node.js","React","MongoDB","Express"],progress:0,category:"Development"},{id:"path_3",title:"UI/UX Design Fundamentals",description:"Learn the principles of user interface and experience design",courses:6,duration:"30 hours",difficulty:"Beginner",skills:["Figma","Design Systems","User Research"],progress:60,category:"Design"}],inProgressCourses:[{id:"course_1",title:"Advanced React Patterns",provider:"LinkedIn Learning",duration:"4 hours",progress:75,category:"Development",instructor:"Eve Porcello"},{id:"course_2",title:"TypeScript Essential Training",provider:"LinkedIn Learning",duration:"3 hours",progress:40,category:"Development",instructor:"Jess Chadwick"}],completedCourses:[{id:"course_3",title:"React.js Essential Training",provider:"LinkedIn Learning",duration:"5 hours",completedAt:new Date("2025-01-10"),category:"Development",certificate:!0}],categories:["Development","Design","Data Science","Project Management","Marketing","Business"]})}catch(i){A.error("Failed to load learning data")}finally{y(!1)}}),x=i=>j(null,null,function*(){try{A.success("Enrolled in course successfully!"),I()}catch(b){A.error("Failed to enroll in course")}}),n=i=>j(null,null,function*(){try{A.success("Learning path started successfully!"),I()}catch(b){A.error("Failed to start learning path")}}),o=i=>({Beginner:"success",Intermediate:"warning",Advanced:"danger"})[i]||"default",v=i=>i.filter(b=>{var J;const z=!f||b.title.toLowerCase().includes(f.toLowerCase())||((J=b.description)==null?void 0:J.toLowerCase().includes(f.toLowerCase())),K=u==="all"||b.category===u;return z&&K}),a=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(ve,{placeholder:"Search courses...",value:f,onChange:i=>T(i.target.value),startContent:e.jsx("span",{className:"text-default-400",children:"🔍"})}),e.jsxs(ae,{placeholder:"Category",selectedKeys:[u],onSelectionChange:i=>S(Array.from(i)[0]),children:[e.jsx(X,{children:"All Categories"},"all"),w.categories.map(i=>e.jsx(X,{value:i,children:i},i))]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v(w.recommendedCourses).map((i,b)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:b*.1},children:e.jsx(R,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(L,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsx("h4",{className:"font-semibold line-clamp-2",children:i.title}),e.jsx(E,{color:"primary",size:"sm",variant:"flat",children:i.provider})]}),e.jsx("p",{className:"text-sm text-default-600 mb-3 line-clamp-2",children:i.description}),e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"text-sm text-default-500",children:[i.duration," • ",i.instructor]}),e.jsx(E,{color:o(i.difficulty),size:"sm",variant:"flat",children:i.difficulty})]}),e.jsx(N,{color:"primary",variant:"flat",size:"sm",className:"w-full",onClick:()=>x(i.id),children:"Enroll Now"})]})})},i.id))})]}),h=()=>e.jsx("div",{className:"space-y-4",children:w.learningPaths.map((i,b)=>e.jsx(F.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:b*.1},children:e.jsx(R,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:i.title}),e.jsx("p",{className:"text-default-600 mb-3",children:i.description}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:i.skills.map(z=>e.jsx(E,{size:"sm",variant:"bordered",children:z},z))}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Courses:"}),e.jsx("span",{className:"ml-2 font-medium",children:i.courses})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Duration:"}),e.jsx("span",{className:"ml-2 font-medium",children:i.duration})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Level:"}),e.jsx("span",{className:"ml-2 font-medium",children:i.difficulty})]})]})]}),e.jsxs("div",{className:"text-center ml-6",children:[e.jsxs("div",{className:"text-2xl font-bold text-primary mb-1",children:[i.progress,"%"]}),e.jsx("div",{className:"text-xs text-default-500",children:"Complete"})]})]}),e.jsx(Z,{value:i.progress,color:"primary",size:"sm",className:"mb-4"}),e.jsxs("div",{className:"flex gap-2",children:[i.progress===0?e.jsx(N,{color:"primary",onClick:()=>n(i.id),children:"Start Learning Path"}):i.progress<100?e.jsx(N,{color:"success",onClick:()=>n(i.id),children:"Continue Learning"}):e.jsx(N,{color:"default",variant:"bordered",children:"View Certificate"}),e.jsx(N,{color:"default",variant:"flat",children:"View Details"})]})]})})},i.id))}),t=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold mb-4",children:["In Progress (",w.inProgressCourses.length,")"]}),e.jsx("div",{className:"space-y-3",children:w.inProgressCourses.map((i,b)=>e.jsx(F.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:b*.1},children:e.jsx(R,{children:e.jsxs(L,{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:i.title}),e.jsxs("div",{className:"text-sm text-default-600",children:[i.provider," • ",i.instructor]})]}),e.jsxs(E,{color:"warning",size:"sm",variant:"flat",children:[i.progress,"%"]})]}),e.jsx(Z,{value:i.progress,color:"warning",size:"sm",className:"mb-3"}),e.jsx(N,{color:"primary",variant:"flat",size:"sm",children:"Continue Course"})]})})},i.id))})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold mb-4",children:["Completed (",w.completedCourses.length,")"]}),e.jsx("div",{className:"space-y-3",children:w.completedCourses.map((i,b)=>e.jsx(F.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:b*.1},children:e.jsx(R,{children:e.jsx(L,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:i.title}),e.jsxs("div",{className:"text-sm text-default-600",children:["Completed on ",i.completedAt.toLocaleDateString()]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[i.certificate&&e.jsx(E,{color:"success",size:"sm",variant:"flat",children:"📜 Certificate"}),e.jsx(E,{color:"success",size:"sm",variant:"flat",children:"✓ Complete"})]})]})})})},i.id))})]})]});return g?e.jsx(G,{isOpen:l,onClose:r,size:"5xl",children:e.jsx(O,{children:e.jsx(H,{className:"py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading learning hub..."})]})})})}):e.jsx(G,{isOpen:l,onClose:r,size:"5xl",scrollBehavior:"inside",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(O,{children:[e.jsxs(te,{className:"flex flex-col gap-1",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Learning Hub"}),e.jsx("p",{className:"text-default-600 font-normal",children:"Advance your skills with curated learning paths and LinkedIn Learning integration"})]}),e.jsx(H,{children:e.jsxs(ne,{selectedKey:C,onSelectionChange:_,className:"mb-6",children:[e.jsx(V,{title:"Recommended",children:a()},"recommended"),e.jsx(V,{title:"Learning Paths",children:h()},"paths"),e.jsx(V,{title:"My Progress",children:t()},"progress")]})}),e.jsx(ie,{children:e.jsx(N,{color:"danger",variant:"flat",onPress:r,children:"Close"})})]})})},Ae=({isOpen:l,onClose:r,currentUser:s,userSkills:m,onUpdate:c})=>{const[g,y]=p.useState(!0),[C,_]=p.useState("submit"),[f,T]=p.useState({pendingReviews:[],completedReviews:[],reviewRequests:[],availableReviews:[]}),[u,S]=p.useState({skillId:"",projectDescription:"",portfolioLinks:"",additionalNotes:""});p.useEffect(()=>{l&&w()},[l]);const w=()=>j(null,null,function*(){try{y(!0),T({pendingReviews:[{id:"review_1",skill:"React",requester:{name:"Sarah Johnson",avatar:null,level:1},projectDescription:"Built a comprehensive e-commerce platform using React, Redux, and TypeScript. Implemented advanced features like real-time inventory management and payment processing.",portfolioLinks:"https://github.com/sarah/ecommerce-app",submittedAt:new Date("2025-01-15"),deadline:new Date("2025-01-20"),status:"pending"},{id:"review_2",skill:"Node.js",requester:{name:"Mike Chen",avatar:null,level:2},projectDescription:"Developed a scalable REST API with Node.js, Express, and MongoDB. Includes authentication, rate limiting, and comprehensive testing.",portfolioLinks:"https://github.com/mike/api-server",submittedAt:new Date("2025-01-14"),deadline:new Date("2025-01-19"),status:"pending"}],completedReviews:[{id:"review_3",skill:"TypeScript",requester:{name:"Alex Rivera",avatar:null,level:1},projectDescription:"Created a type-safe React application with advanced TypeScript patterns and custom hooks.",reviewScore:4.5,feedback:"Excellent use of TypeScript generics and type guards. Code is well-structured and maintainable.",completedAt:new Date("2025-01-12"),status:"approved"}],reviewRequests:[{id:"request_1",skill:"React",projectDescription:"Built a real-time chat application with React, Socket.io, and Firebase. Features include message encryption and file sharing.",portfolioLinks:"https://github.com/user/chat-app",submittedAt:new Date("2025-01-16"),status:"pending",reviewersAssigned:2,reviewersNeeded:3}],availableReviews:[{id:"available_1",skill:"JavaScript",requester:{name:"Emma Davis",avatar:null,level:0},projectDescription:"Interactive web application showcasing advanced JavaScript concepts including closures, prototypes, and async programming.",estimatedTime:"30 minutes",difficulty:"Intermediate"},{id:"available_2",skill:"CSS",requester:{name:"Tom Wilson",avatar:null,level:1},projectDescription:"Responsive design portfolio with advanced CSS animations and grid layouts.",estimatedTime:"20 minutes",difficulty:"Beginner"}]})}catch(t){A.error("Failed to load peer review data")}finally{y(!1)}}),q=()=>j(null,null,function*(){try{if(!u.skillId||!u.projectDescription){A.error("Please fill in all required fields");return}A.success("Review request submitted successfully!"),S({skillId:"",projectDescription:"",portfolioLinks:"",additionalNotes:""}),w(),c()}catch(t){A.error("Failed to submit review request")}}),I=t=>j(null,null,function*(){try{A.success("Review accepted! You can now start the review process."),w()}catch(i){A.error("Failed to accept review")}}),x=t=>({pending:"warning",approved:"success",rejected:"danger",in_review:"primary"})[t]||"default",n=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),o=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Submit for Peer Review"}),e.jsx("p",{className:"text-default-600",children:"Get your skills validated by the community to advance to Level 2"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(ae,{label:"Skill to Review",placeholder:"Select a skill",selectedKeys:u.skillId?[u.skillId]:[],onSelectionChange:t=>S(i=>M(P({},i),{skillId:Array.from(t)[0]})),isRequired:!0,children:m.filter(t=>t.verification_level<2).map(t=>e.jsxs(X,{value:t.id,children:[t.name," (Level ",t.verification_level||0,")"]},t.id))}),e.jsx(Q,{label:"Project Description",placeholder:"Describe the project that demonstrates your skill proficiency...",value:u.projectDescription,onChange:t=>S(i=>M(P({},i),{projectDescription:t.target.value})),isRequired:!0,minRows:4}),e.jsx(Q,{label:"Portfolio Links",placeholder:"GitHub repository, live demo, or other relevant links...",value:u.portfolioLinks,onChange:t=>S(i=>M(P({},i),{portfolioLinks:t.target.value})),minRows:2}),e.jsx(Q,{label:"Additional Notes",placeholder:"Any additional context or specific areas you'd like reviewers to focus on...",value:u.additionalNotes,onChange:t=>S(i=>M(P({},i),{additionalNotes:t.target.value})),minRows:3}),e.jsx(N,{color:"primary",size:"lg",className:"w-full",onClick:q,children:"Submit for Review"})]}),e.jsx(R,{className:"bg-blue-50 dark:bg-blue-900/20",children:e.jsxs(L,{className:"p-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Review Process"}),e.jsxs("div",{className:"text-sm text-default-600 space-y-1",children:[e.jsx("div",{children:"• Your submission will be reviewed by 3 qualified peers"}),e.jsx("div",{children:"• Reviews typically take 3-5 business days"}),e.jsx("div",{children:"• You'll receive detailed feedback and a verification score"}),e.jsx("div",{children:"• Passing score (4.0+) advances you to Level 2"})]})]})})]}),v=()=>e.jsxs("div",{className:"space-y-4",children:[f.reviewRequests.map((t,i)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:i*.1},children:e.jsx(R,{children:e.jsxs(L,{className:"p-6",children:[e.jsx("div",{className:"flex items-start justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold",children:t.skill}),e.jsx(E,{color:x(t.status),size:"sm",variant:"flat",children:t.status})]}),e.jsx("p",{className:"text-default-600 mb-3",children:t.projectDescription}),e.jsxs("div",{className:"text-sm text-default-500",children:["Submitted on ",n(t.submittedAt)]})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-default-500",children:"Reviewers:"}),e.jsxs("span",{className:"ml-2 font-medium",children:[t.reviewersAssigned,"/",t.reviewersNeeded," assigned"]})]}),e.jsx(N,{color:"default",variant:"flat",size:"sm",children:"View Details"})]})]})})},t.id)),f.reviewRequests.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📝"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Review Requests"}),e.jsx("p",{className:"text-default-600",children:"Submit your first project for peer review to get started"})]})]}),a=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Available Reviews"}),e.jsx("div",{className:"space-y-4",children:f.availableReviews.map((t,i)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:i*.1},children:e.jsx(R,{className:"hover:shadow-lg transition-shadow",children:e.jsxs(L,{className:"p-6",children:[e.jsx("div",{className:"flex items-start justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(W,{src:t.requester.avatar,name:t.requester.name,size:"sm"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:t.requester.name}),e.jsxs("div",{className:"text-xs text-default-500",children:["Level ",t.requester.level]})]})]}),e.jsxs("h3",{className:"text-lg font-semibold mb-2",children:[t.skill," Review"]}),e.jsx("p",{className:"text-default-600 mb-3",children:t.projectDescription}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-default-500",children:[e.jsxs("span",{children:["⏱️ ",t.estimatedTime]}),e.jsxs("span",{children:["📊 ",t.difficulty]})]})]})}),e.jsx(N,{color:"primary",onClick:()=>I(t.id),children:"Accept Review"})]})})},t.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"My Pending Reviews"}),e.jsx("div",{className:"space-y-4",children:f.pendingReviews.map((t,i)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:i*.1},children:e.jsx(R,{className:"border-warning",children:e.jsxs(L,{className:"p-6",children:[e.jsx("div",{className:"flex items-start justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(W,{src:t.requester.avatar,name:t.requester.name,size:"sm"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:t.requester.name}),e.jsxs("div",{className:"text-xs text-default-500",children:["Level ",t.requester.level]})]})]}),e.jsxs("h3",{className:"text-lg font-semibold mb-2",children:[t.skill," Review"]}),e.jsx("p",{className:"text-default-600 mb-3",children:t.projectDescription}),e.jsxs("div",{className:"text-sm text-default-500",children:["Deadline: ",n(t.deadline)]})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{color:"success",onClick:()=>{A.info("Review interface coming soon")},children:"Start Review"}),e.jsx(N,{color:"default",variant:"flat",onClick:()=>{window.open(t.portfolioLinks,"_blank")},children:"View Project"})]})]})})},t.id))})]})]}),h=()=>e.jsxs("div",{className:"space-y-4",children:[f.completedReviews.map((t,i)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:i*.1},children:e.jsx(R,{children:e.jsx(L,{className:"p-6",children:e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(W,{src:t.requester.avatar,name:t.requester.name,size:"sm"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:t.requester.name}),e.jsxs("div",{className:"text-xs text-default-500",children:["Level ",t.requester.level]})]})]}),e.jsxs("h3",{className:"text-lg font-semibold mb-2",children:[t.skill," Review"]}),e.jsx("p",{className:"text-default-600 mb-3",children:t.projectDescription}),e.jsxs("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg mb-3",children:[e.jsx("div",{className:"font-medium text-green-700 dark:text-green-300 mb-1",children:"Your Feedback:"}),e.jsx("div",{className:"text-sm text-green-600 dark:text-green-400",children:t.feedback})]}),e.jsxs("div",{className:"text-sm text-default-500",children:["Completed on ",n(t.completedAt)]})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-green-600 mb-1",children:[t.reviewScore,"/5"]}),e.jsx(E,{color:"success",size:"sm",variant:"flat",children:t.status})]})]})})})},t.id)),f.completedReviews.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-4xl mb-4",children:"✅"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Completed Reviews"}),e.jsx("p",{className:"text-default-600",children:"Complete your first peer review to see it here"})]})]});return g?e.jsx(G,{isOpen:l,onClose:r,size:"4xl",children:e.jsx(O,{children:e.jsx(H,{className:"py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading peer review system..."})]})})})}):e.jsx(G,{isOpen:l,onClose:r,size:"4xl",scrollBehavior:"inside",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(O,{children:[e.jsxs(te,{className:"flex flex-col gap-1",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Peer Review System"}),e.jsx("p",{className:"text-default-600 font-normal",children:"Get validated by the community and help others advance their skills"})]}),e.jsx(H,{children:e.jsxs(ne,{selectedKey:C,onSelectionChange:_,className:"mb-6",children:[e.jsx(V,{title:"Submit Review",children:o()},"submit"),e.jsx(V,{title:`My Requests (${f.reviewRequests.length})`,children:v()},"requests"),e.jsx(V,{title:"Review Others",children:a()},"review"),e.jsx(V,{title:`Completed (${f.completedReviews.length})`,children:h()},"completed")]})}),e.jsx(ie,{children:e.jsx(N,{color:"danger",variant:"flat",onPress:r,children:"Close"})})]})})},Re=({isOpen:l,onClose:r,currentUser:s,userSkills:m,onUpdate:c})=>{const[g,y]=p.useState(!0),[C,_]=p.useState("apply"),[f,T]=p.useState({availableExperts:[],pendingAssessments:[],completedAssessments:[],expertApplications:[]}),[u,S]=p.useState({skillId:"",portfolioUrl:"",experience:"",achievements:"",motivation:""});p.useEffect(()=>{l&&w()},[l]);const w=()=>j(null,null,function*(){try{y(!0);const a=[{id:"expert_1",name:"Dr. Sarah Mitchell",title:"Senior Software Architect",company:"Google",avatar:null,specialties:["React","TypeScript","System Design"],experience:"15+ years",rating:4.9,reviewsCompleted:127,bio:"Leading expert in frontend architecture and scalable React applications. Former Facebook engineer with extensive experience in large-scale systems."},{id:"expert_2",name:"Michael Rodriguez",title:"Principal Engineer",company:"Microsoft",avatar:null,specialties:["Node.js","Cloud Architecture","DevOps"],experience:"12+ years",rating:4.8,reviewsCompleted:89,bio:"Expert in backend systems and cloud infrastructure. Specializes in Node.js performance optimization and Azure cloud solutions."},{id:"expert_3",name:"Lisa Chen",title:"Design Director",company:"Adobe",avatar:null,specialties:["UI/UX Design","Design Systems","User Research"],experience:"10+ years",rating:4.9,reviewsCompleted:156,bio:"Award-winning designer with expertise in creating intuitive user experiences and scalable design systems for enterprise applications."}],h=[{id:"assessment_1",skill:"React",expert:a[0],submittedAt:new Date("2025-01-14"),scheduledDate:new Date("2025-01-20"),status:"scheduled",assessmentType:"technical_interview"}];T({availableExperts:a,pendingAssessments:h,completedAssessments:[{id:"assessment_2",skill:"JavaScript",expert:{name:"John Smith",title:"Senior Developer",company:"Netflix"},completedAt:new Date("2025-01-10"),score:4.7,feedback:"Excellent understanding of advanced JavaScript concepts. Strong problem-solving skills and clean code practices.",status:"passed",certificateUrl:"https://certificates.royaltea.com/js-expert-123"}],expertApplications:[{id:"app_1",skill:"TypeScript",submittedAt:new Date("2025-01-15"),status:"under_review",expertAssigned:null,estimatedReviewTime:"3-5 business days"}]})}catch(a){A.error("Failed to load expert panel data")}finally{y(!1)}}),q=()=>j(null,null,function*(){try{if(!u.skillId||!u.portfolioUrl||!u.experience){A.error("Please fill in all required fields");return}A.success("Expert assessment application submitted successfully!"),S({skillId:"",portfolioUrl:"",experience:"",achievements:"",motivation:""}),w(),c()}catch(a){A.error("Failed to submit expert assessment application")}}),I=a=>({under_review:"warning",scheduled:"primary",passed:"success",failed:"danger",pending:"default"})[a]||"default",x=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),n=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Apply for Expert Assessment"}),e.jsx("p",{className:"text-default-600",children:"Get validated by industry experts to advance to Level 4"})]}),e.jsx(R,{className:"bg-purple-50 dark:bg-purple-900/20 mb-6",children:e.jsxs(L,{className:"p-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Expert Assessment Requirements"}),e.jsxs("div",{className:"text-sm text-default-600 space-y-1",children:[e.jsx("div",{children:"• Must have completed Level 2 (Peer Verified) or higher"}),e.jsx("div",{children:"• Demonstrate significant project experience and achievements"}),e.jsx("div",{children:"• Portfolio showcasing advanced skill proficiency"}),e.jsx("div",{children:"• Pass comprehensive technical interview with industry expert"})]})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(ae,{label:"Skill for Assessment",placeholder:"Select a skill",selectedKeys:u.skillId?[u.skillId]:[],onSelectionChange:a=>S(h=>M(P({},h),{skillId:Array.from(a)[0]})),isRequired:!0,children:m.filter(a=>(a.verification_level||0)>=2).map(a=>e.jsxs(X,{value:a.id,children:[a.name," (Level ",a.verification_level||0,")"]},a.id))}),e.jsx(Q,{label:"Portfolio URL",placeholder:"Link to your portfolio, GitHub, or project showcase...",value:u.portfolioUrl,onChange:a=>S(h=>M(P({},h),{portfolioUrl:a.target.value})),isRequired:!0,minRows:2}),e.jsx(Q,{label:"Professional Experience",placeholder:"Describe your professional experience and significant projects in this skill area...",value:u.experience,onChange:a=>S(h=>M(P({},h),{experience:a.target.value})),isRequired:!0,minRows:4}),e.jsx(Q,{label:"Key Achievements",placeholder:"Highlight your most significant achievements, certifications, or recognition in this field...",value:u.achievements,onChange:a=>S(h=>M(P({},h),{achievements:a.target.value})),minRows:3}),e.jsx(Q,{label:"Motivation",placeholder:"Why are you seeking expert validation? How will this certification benefit your career?",value:u.motivation,onChange:a=>S(h=>M(P({},h),{motivation:a.target.value})),minRows:3}),e.jsx(N,{color:"primary",size:"lg",className:"w-full",onClick:q,children:"Submit Expert Assessment Application"})]})]}),o=()=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Available Experts"}),e.jsx("p",{className:"text-default-600",children:"Industry leaders ready to validate your expertise"})]}),f.availableExperts.map((a,h)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:h*.1},children:e.jsx(R,{className:"hover:shadow-lg transition-shadow",children:e.jsx(L,{className:"p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(W,{src:a.avatar,name:a.name,size:"lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold",children:a.name}),e.jsx("div",{className:"text-default-600",children:a.title}),e.jsx("div",{className:"text-sm text-default-500",children:a.company})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center gap-1 mb-1",children:[e.jsx("span",{className:"text-yellow-500",children:"⭐"}),e.jsx("span",{className:"font-semibold",children:a.rating})]}),e.jsxs("div",{className:"text-xs text-default-500",children:[a.reviewsCompleted," reviews"]})]})]}),e.jsx("p",{className:"text-sm text-default-600 mb-4",children:a.bio}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-500 mb-2",children:"Specialties:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:a.specialties.map(t=>e.jsx(E,{size:"sm",variant:"bordered",children:t},t))})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-default-500 mb-2",children:"Experience:"}),e.jsx("div",{className:"font-medium",children:a.experience})]})]})]})]})})})},a.id))]}),v=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Pending Assessments"}),e.jsx("div",{className:"space-y-4",children:f.pendingAssessments.map((a,h)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:h*.1},children:e.jsx(R,{className:"border-primary",children:e.jsxs(L,{className:"p-6",children:[e.jsx("div",{className:"flex items-start justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsxs("h4",{className:"text-lg font-semibold",children:[a.skill," Assessment"]}),e.jsx(E,{color:I(a.status),size:"sm",variant:"flat",children:a.status})]}),e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx(W,{src:a.expert.avatar,name:a.expert.name,size:"sm"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:a.expert.name}),e.jsxs("div",{className:"text-sm text-default-600",children:[a.expert.title," at ",a.expert.company]})]})]}),e.jsxs("div",{className:"text-sm text-default-500",children:["Scheduled: ",x(a.scheduledDate)]})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{color:"primary",onClick:()=>{A.info("Assessment interface coming soon")},children:"Join Assessment"}),e.jsx(N,{color:"default",variant:"flat",onClick:()=>{A.info("Reschedule feature coming soon")},children:"Reschedule"})]})]})})},a.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Applications Under Review"}),e.jsx("div",{className:"space-y-4",children:f.expertApplications.map((a,h)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:h*.1},children:e.jsx(R,{children:e.jsx(L,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-semibold",children:[a.skill," Expert Assessment"]}),e.jsxs("div",{className:"text-sm text-default-600",children:["Submitted on ",x(a.submittedAt)]}),e.jsxs("div",{className:"text-sm text-default-500 mt-1",children:["ETA: ",a.estimatedReviewTime]})]}),e.jsx(E,{color:I(a.status),variant:"flat",children:a.status.replace("_"," ")})]})})})},a.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Completed Assessments"}),e.jsx("div",{className:"space-y-4",children:f.completedAssessments.map((a,h)=>e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:h*.1},children:e.jsx(R,{className:"border-success",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsxs("h4",{className:"text-lg font-semibold",children:[a.skill," Assessment"]}),e.jsx(E,{color:I(a.status),size:"sm",variant:"flat",children:a.status})]}),e.jsxs("div",{className:"text-sm text-default-600 mb-3",children:["Expert: ",a.expert.name," (",a.expert.company,")"]}),e.jsxs("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg mb-3",children:[e.jsx("div",{className:"font-medium text-green-700 dark:text-green-300 mb-1",children:"Expert Feedback:"}),e.jsx("div",{className:"text-sm text-green-600 dark:text-green-400",children:a.feedback})]}),e.jsxs("div",{className:"text-sm text-default-500",children:["Completed on ",x(a.completedAt)]})]}),e.jsxs("div",{className:"text-center ml-6",children:[e.jsxs("div",{className:"text-3xl font-bold text-green-600 mb-1",children:[a.score,"/5"]}),e.jsx("div",{className:"text-xs text-default-500",children:"Expert Score"})]})]}),a.certificateUrl&&e.jsx(N,{color:"success",variant:"flat",onClick:()=>window.open(a.certificateUrl,"_blank"),children:"Download Certificate"})]})})},a.id))})]}),f.pendingAssessments.length===0&&f.expertApplications.length===0&&f.completedAssessments.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-4xl mb-4",children:"⭐"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Expert Assessments"}),e.jsx("p",{className:"text-default-600",children:"Apply for expert validation to advance to Level 4"})]})]});return g?e.jsx(G,{isOpen:l,onClose:r,size:"4xl",children:e.jsx(O,{children:e.jsx(H,{className:"py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading expert panel..."})]})})})}):e.jsx(G,{isOpen:l,onClose:r,size:"4xl",scrollBehavior:"inside",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(O,{children:[e.jsxs(te,{className:"flex flex-col gap-1",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Expert Panel"}),e.jsx("p",{className:"text-default-600 font-normal",children:"Get validated by industry experts to reach Level 4 verification"})]}),e.jsx(H,{children:e.jsxs(ne,{selectedKey:C,onSelectionChange:_,className:"mb-6",children:[e.jsx(V,{title:"Apply",children:n()},"apply"),e.jsx(V,{title:"Experts",children:o()},"experts"),e.jsx(V,{title:"My Assessments",children:v()},"assessments")]})}),e.jsx(ie,{children:e.jsx(N,{color:"danger",variant:"flat",onPress:r,children:"Close"})})]})})},Le=({isOpen:l,onClose:r,currentUser:s,availableSkills:m,onUpdate:c})=>{const[g,y]=p.useState(!1),[C,_]=p.useState("selection"),[f,T]=p.useState(""),[u,S]=p.useState(0),[w,q]=p.useState({}),[I,x]=p.useState(0),[n,o]=p.useState(null),v=d=>{const k={React:[{id:1,type:"multiple_choice",question:"What is the purpose of the useEffect hook in React?",options:["To manage component state","To perform side effects in functional components","To handle user events","To render JSX elements"],correctAnswer:1,difficulty:"intermediate",timeLimit:60},{id:2,type:"multiple_choice",question:"Which of the following is the correct way to update state in a functional component?",options:["this.setState({value: newValue})","setState(newValue)","const [state, setState] = useState(); setState(newValue)","state = newValue"],correctAnswer:2,difficulty:"beginner",timeLimit:45},{id:3,type:"coding",question:"Write a custom React hook that manages a counter with increment and decrement functions.",placeholder:`function useCounter(initialValue = 0) {
  // Your implementation here
}`,difficulty:"advanced",timeLimit:300},{id:4,type:"practical",question:"Explain the difference between controlled and uncontrolled components in React. Provide examples.",difficulty:"intermediate",timeLimit:180}],JavaScript:[{id:1,type:"multiple_choice",question:"What is the output of: console.log(typeof null)?",options:["null","undefined","object","boolean"],correctAnswer:2,difficulty:"intermediate",timeLimit:30},{id:2,type:"coding",question:"Write a function that debounces another function.",placeholder:`function debounce(func, delay) {
  // Your implementation here
}`,difficulty:"advanced",timeLimit:240}]};return k[d]||k.JavaScript},a=()=>j(null,null,function*(){var d,k;if(!f){A.error("Please select a skill to assess");return}y(!0);try{const D=m.find(Y=>Y.id===f),U=v(D.name);o({skill:D,questions:U,totalQuestions:U.length,startTime:new Date,timeLimit:U.reduce((Y,me)=>Y+me.timeLimit,0)}),S(0),q({}),x(((d=U[0])==null?void 0:d.timeLimit)||60),_("assessment"),h(((k=U[0])==null?void 0:k.timeLimit)||60)}catch(D){A.error("Failed to start assessment")}finally{y(!1)}}),h=d=>{x(d);const k=setInterval(()=>{x(D=>D<=1?(clearInterval(k),i(),0):D-1)},1e3)},t=(d,k)=>{q(D=>M(P({},D),{[d]:k}))},i=()=>{const d=u+1;if(d<n.questions.length){S(d);const k=n.questions[d];h(k.timeLimit)}else z()},b=()=>{if(u>0){S(u-1);const d=n.questions[u-1];h(d.timeLimit)}},z=()=>j(null,null,function*(){try{y(!0);const d=K(),k={skillId:n.skill.id,score:d,answers:w,completedAt:new Date,timeSpent:Math.floor((new Date-n.startTime)/1e3)};yield new Promise(D=>setTimeout(D,1e3)),_("results"),A.success("Assessment completed successfully!")}catch(d){A.error("Failed to complete assessment")}finally{y(!1)}}),K=()=>{let d=0,k=0;return n.questions.forEach(D=>{k+=D.type==="multiple_choice"?10:20;const U=w[D.id];U&&(D.type==="multiple_choice"?d+=U===D.correctAnswer?10:0:d+=U.length>50?15:10)}),Math.round(d/k*100)},J=d=>{const k=Math.floor(d/60),D=d%60;return`${k}:${D.toString().padStart(2,"0")}`},ee=d=>({beginner:"success",intermediate:"warning",advanced:"danger"})[d]||"default",$=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Select Skill Assessment"}),e.jsx("p",{className:"text-default-600",children:"Choose a skill to test your proficiency and advance your verification level"})]}),e.jsx(ae,{label:"Skill to Assess",placeholder:"Select a skill",selectedKeys:f?[f]:[],onSelectionChange:d=>T(Array.from(d)[0]),size:"lg",children:m.map(d=>e.jsx(X,{value:d.id,children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:d.name}),e.jsx("div",{className:"text-sm text-default-500",children:d.category})]})},d.id))}),f&&e.jsx(R,{className:"bg-blue-50 dark:bg-blue-900/20",children:e.jsxs(L,{className:"p-4",children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Assessment Details"}),e.jsxs("div",{className:"text-sm text-default-600 space-y-1",children:[e.jsx("div",{children:"• Multiple question types: Multiple choice, coding, and practical"}),e.jsx("div",{children:"• Estimated time: 15-20 minutes"}),e.jsx("div",{children:"• Passing score: 70% or higher"}),e.jsx("div",{children:"• You can retake the assessment after 24 hours if needed"})]})]})}),e.jsx(N,{color:"primary",size:"lg",className:"w-full",onClick:a,disabled:!f,isLoading:g,children:"Start Assessment"})]}),re=()=>{var D;const d=n.questions[u],k=(u+1)/n.totalQuestions*100;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-xl font-semibold",children:[n.skill.name," Assessment"]}),e.jsxs("p",{className:"text-default-600",children:["Question ",u+1," of ",n.totalQuestions]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-primary",children:J(I)}),e.jsx("div",{className:"text-sm text-default-500",children:"Time remaining"})]})]}),e.jsx(Z,{value:k,color:"primary",size:"sm",className:"mb-6"}),e.jsx(R,{children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(E,{color:ee(d.difficulty),variant:"flat",size:"sm",children:d.difficulty}),e.jsx(E,{color:"primary",variant:"flat",size:"sm",children:d.type.replace("_"," ")})]}),e.jsx("h4",{className:"text-lg font-semibold mb-4",children:d.question}),d.type==="multiple_choice"&&e.jsx(ge,{value:(D=w[d.id])==null?void 0:D.toString(),onValueChange:U=>t(d.id,parseInt(U)),children:d.options.map((U,Y)=>e.jsx(je,{value:Y.toString(),children:U},Y))}),d.type==="coding"&&e.jsx(Q,{placeholder:d.placeholder,value:w[d.id]||"",onChange:U=>t(d.id,U.target.value),minRows:8,className:"font-mono"}),d.type==="practical"&&e.jsx(Q,{placeholder:"Provide a detailed explanation with examples...",value:w[d.id]||"",onChange:U=>t(d.id,U.target.value),minRows:6})]})}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(N,{color:"default",variant:"bordered",onClick:b,disabled:u===0,children:"Previous"}),e.jsx(N,{color:"primary",onClick:i,disabled:!w[d.id],children:u===n.totalQuestions-1?"Complete Assessment":"Next Question"})]})]})},de=()=>{const d=K(),k=d>=70;return e.jsxs("div",{className:"space-y-6 text-center",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:`text-6xl font-bold mb-4 ${k?"text-green-600":"text-red-600"}`,children:[d,"%"]}),e.jsx("h3",{className:"text-2xl font-semibold mb-2",children:k?"Congratulations!":"Assessment Complete"}),e.jsx("p",{className:"text-default-600",children:k?"You have successfully passed the assessment and advanced your skill verification!":"You can retake this assessment after 24 hours to improve your score."})]}),e.jsx(R,{className:k?"border-success":"border-warning",children:e.jsxs(L,{className:"p-6",children:[e.jsx("h4",{className:"font-semibold mb-4",children:"Assessment Summary"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Skill:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.skill.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Score:"}),e.jsxs("span",{className:"ml-2 font-medium",children:[d,"%"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Questions:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.totalQuestions})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-default-500",children:"Status:"}),e.jsx("span",{className:`ml-2 font-medium ${k?"text-green-600":"text-red-600"}`,children:k?"Passed":"Failed"})]})]})]})}),e.jsxs("div",{className:"flex gap-4 justify-center",children:[e.jsx(N,{color:"primary",onClick:()=>{c(),r()},children:"View Dashboard"}),e.jsx(N,{color:"default",variant:"bordered",onClick:()=>{_("selection"),T(""),o(null)},children:"Take Another Assessment"})]})]})};return e.jsx(G,{isOpen:l,onClose:r,size:"3xl",isDismissable:C==="selection",hideCloseButton:C==="assessment",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(O,{children:[e.jsxs(te,{className:"flex flex-col gap-1",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Skill Assessment"}),e.jsx("p",{className:"text-default-600 font-normal",children:"Test your skills and advance your verification level"})]}),e.jsxs(H,{children:[C==="selection"&&$(),C==="assessment"&&re(),C==="results"&&de()]}),C==="selection"&&e.jsx(ie,{children:e.jsx(N,{color:"danger",variant:"flat",onPress:r,children:"Cancel"})})]})})},Fe=({className:l=""})=>{const{currentUser:r}=p.useContext(ye);_e();const[s,m]=p.useState({userSkills:[],availableSkills:[],verificationLevel:0,overallProgress:0,recentAchievements:[],nextMilestones:[]}),[c,g]=p.useState(!0),[y,C]=p.useState("overview"),[_,f]=p.useState(!1),[T,u]=p.useState(!1),[S,w]=p.useState(!1),[q,I]=p.useState(!1),x=[{level:0,name:"Unverified",description:"New to the platform",color:"default",icon:"👤",requirements:"Complete profile setup"},{level:1,name:"Learning",description:"Completed learning path",color:"primary",icon:"📚",requirements:"Complete technology learning path"},{level:2,name:"Peer Verified",description:"Validated by community",color:"success",icon:"🤝",requirements:"Pass peer review process"},{level:3,name:"Project Verified",description:"Proven through client work",color:"warning",icon:"🏗️",requirements:"Complete successful client projects"},{level:4,name:"Expert Verified",description:"Validated by industry experts",color:"secondary",icon:"⭐",requirements:"Pass expert assessment panel"},{level:5,name:"Industry Certified",description:"Industry-recognized expertise",color:"danger",icon:"👑",requirements:"Industry certifications and recognition"}],n=()=>j(null,null,function*(){try{g(!0);const[t,i]=yield Promise.all([be(r==null?void 0:r.id),we()]),b=t.map($=>Ne($)),z=b.length>0?b.reduce(($,re)=>$+re,0)/b.length:0,K=Math.max(...t.map($=>$.verification_level||0),0),J=[{id:1,title:"React Fundamentals",type:"learning_completion",date:new Date("2025-01-15"),level:1},{id:2,title:"Peer Review Passed",type:"peer_validation",date:new Date("2025-01-14"),level:2}],ee=[{id:1,title:"Complete TypeScript Assessment",type:"assessment",progress:60,estimatedCompletion:"2 days"},{id:2,title:"Expert Panel Review",type:"expert_review",progress:0,estimatedCompletion:"1 week"}];m({userSkills:t,availableSkills:i,verificationLevel:K,overallProgress:Math.round(z),recentAchievements:J,nextMilestones:ee})}catch(t){A.error("Failed to load skill verification data")}finally{g(!1)}}),o=()=>x.find(t=>t.level===s.verificationLevel)||x[0],v=()=>{const t=s.verificationLevel+1;return x.find(i=>i.level===t)};if(p.useEffect(()=>{r&&n()},[r]),c)return e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading skill verification dashboard..."})]})});const a=o(),h=v();return e.jsxs("div",{className:`skill-verification-dashboard ${l}`,children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2",children:"🎓 Skill Verification"}),e.jsx("p",{className:"text-default-600",children:"Progress through our 6-level verification system"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{color:"primary",variant:"flat",onClick:()=>f(!0),startContent:e.jsx("span",{children:"📚"}),children:"Learning Hub"}),e.jsx(N,{color:"secondary",variant:"flat",onClick:()=>I(!0),startContent:e.jsx("span",{children:"📝"}),children:"Take Assessment"})]})]})}),e.jsxs("div",{className:"grid grid-cols-12 gap-6",children:[e.jsxs("div",{className:"col-span-12 lg:col-span-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(R,{className:"bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-800/20 hover:shadow-lg transition-shadow",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("span",{className:"text-2xl",children:a.icon}),e.jsxs(E,{color:a.color,variant:"flat",size:"sm",children:["Level ",a.level]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Current Level"}),e.jsx("div",{className:"text-lg font-bold text-blue-600",children:a.name})]}),e.jsx("div",{children:e.jsx("div",{className:"text-sm text-default-600",children:a.description})})]})]})})}),e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},children:e.jsx(R,{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20 hover:shadow-lg transition-shadow",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("span",{className:"text-2xl",children:"📊"}),e.jsx(E,{color:"success",variant:"flat",size:"sm",children:"Progress"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Overall Score"}),e.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[s.overallProgress,"%"]})]}),e.jsx(Z,{value:s.overallProgress,color:"success",size:"sm",className:"mt-2"})]})]})})}),e.jsx(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},children:e.jsx(R,{className:"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20 hover:shadow-lg transition-shadow",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("span",{className:"text-2xl",children:(h==null?void 0:h.icon)||"🎯"}),e.jsx(E,{color:"warning",variant:"flat",size:"sm",children:"Next Level"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Target"}),e.jsx("div",{className:"text-lg font-bold text-orange-600",children:(h==null?void 0:h.name)||"Max Level Reached"})]}),e.jsx("div",{children:e.jsx("div",{className:"text-sm text-default-600",children:(h==null?void 0:h.requirements)||"Congratulations!"})})]})]})})})]}),e.jsxs(R,{className:"mb-6",children:[e.jsx(oe,{className:"pb-3",children:e.jsx("h3",{className:"text-lg font-semibold",children:"Verification Level Progression"})}),e.jsx(L,{className:"pt-0",children:e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:x.map(t=>e.jsxs(F.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3,delay:t.level*.1},className:`text-center p-4 rounded-lg border-2 transition-all ${t.level<=s.verificationLevel?"border-primary bg-primary-50 dark:bg-primary-900/20":t.level===s.verificationLevel+1?"border-warning bg-warning-50 dark:bg-warning-900/20":"border-default-200 bg-default-50 dark:bg-default-900/20"}`,children:[e.jsx("div",{className:"text-2xl mb-2",children:t.icon}),e.jsx("div",{className:"font-semibold text-sm",children:t.name}),e.jsxs("div",{className:"text-xs text-default-600 mt-1",children:["Level ",t.level]}),t.level<=s.verificationLevel&&e.jsx(E,{color:"success",size:"sm",variant:"flat",className:"mt-2",children:"✓ Achieved"}),t.level===s.verificationLevel+1&&e.jsx(E,{color:"warning",size:"sm",variant:"flat",className:"mt-2",children:"In Progress"})]},t.level))})})]})]}),e.jsx("div",{className:"col-span-12 lg:col-span-4",children:e.jsxs(R,{className:"h-full",children:[e.jsx(oe,{className:"pb-3",children:e.jsx("h3",{className:"text-lg font-semibold",children:"Next Steps"})}),e.jsxs(L,{className:"space-y-4",children:[e.jsx("div",{className:"space-y-3",children:s.nextMilestones.map(t=>e.jsxs("div",{className:"p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium text-sm",children:t.title}),e.jsxs(E,{color:"primary",size:"sm",variant:"flat",children:[t.progress,"%"]})]}),e.jsx(Z,{value:t.progress,color:"primary",size:"sm",className:"mb-2"}),e.jsxs("div",{className:"text-xs text-default-500",children:["ETA: ",t.estimatedCompletion]})]},t.id))}),e.jsxs("div",{className:"border-t pt-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-sm",children:"Quick Actions"}),e.jsx(N,{color:"primary",variant:"flat",className:"w-full justify-start",startContent:e.jsx("span",{children:"📚"}),onClick:()=>f(!0),children:"Learning Hub"}),e.jsx(N,{color:"success",variant:"flat",className:"w-full justify-start",startContent:e.jsx("span",{children:"🤝"}),onClick:()=>u(!0),children:"Peer Review"}),e.jsx(N,{color:"secondary",variant:"flat",className:"w-full justify-start",startContent:e.jsx("span",{children:"⭐"}),onClick:()=>w(!0),children:"Expert Panel"}),e.jsx(N,{color:"warning",variant:"flat",className:"w-full justify-start",startContent:e.jsx("span",{children:"📝"}),onClick:()=>I(!0),children:"Take Assessment"})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"font-semibold text-sm mb-3",children:"Verification Tips"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-xs text-default-600 p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:"💡 Complete learning paths to advance to Level 1"}),e.jsx("div",{className:"text-xs text-default-600 p-2 bg-green-50 dark:bg-green-900/20 rounded",children:"🤝 Get peer reviews to reach Level 2"}),e.jsx("div",{className:"text-xs text-default-600 p-2 bg-purple-50 dark:bg-purple-900/20 rounded",children:"⭐ Expert validation unlocks Level 4"})]})]})]})]})})]}),_&&e.jsx(Ce,{isOpen:_,onClose:()=>f(!1),currentUser:r,userSkills:s.userSkills,onUpdate:()=>n()}),T&&e.jsx(Ae,{isOpen:T,onClose:()=>u(!1),currentUser:r,userSkills:s.userSkills,onUpdate:()=>n()}),S&&e.jsx(Re,{isOpen:S,onClose:()=>w(!1),currentUser:r,userSkills:s.userSkills,onUpdate:()=>n()}),q&&e.jsx(Le,{isOpen:q,onClose:()=>I(!1),currentUser:r,availableSkills:s.availableSkills,onUpdate:()=>n()})]})};export{Fe as default};
