var gh=Math.pow;import{r as q,d as Q,a as A}from"./chunk-DX4Z_LyS.js";import{g as le}from"./chunk-Cai8ouo_.js";import{b as re}from"../assets/main-CGUKzV0x.js";var Za,bh;function Re(){if(bh)return Za;bh=1;var e=Array.isArray;return Za=e,Za}var Ja,xh;function nb(){if(xh)return Ja;xh=1;var e=typeof globalThis=="object"&&globalThis&&globalThis.Object===Object&&globalThis;return Ja=e,Ja}var Qa,wh;function ct(){if(wh)return Qa;wh=1;var e=nb(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Qa=r,Qa}var eo,Oh;function Zn(){if(Oh)return eo;Oh=1;var e=ct(),t=e.Symbol;return eo=t,eo}var to,_h;function yw(){if(_h)return to;_h=1;var e=Zn(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),c=o[i];try{o[i]=void 0;var s=!0}catch(l){}var f=n.call(o);return s&&(u?o[i]=c:delete o[i]),f}return to=a,to}var ro,Sh;function mw(){if(Sh)return ro;Sh=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return ro=r,ro}var no,Ah;function wt(){if(Ah)return no;Ah=1;var e=Zn(),t=yw(),r=mw(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return no=o,no}var io,Ph;function Ot(){if(Ph)return io;Ph=1;function e(t){return t!=null&&typeof t=="object"}return io=e,io}var ao,Eh;function Wr(){if(Eh)return ao;Eh=1;var e=wt(),t=Ot(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return ao=n,ao}var oo,Th;function pf(){if(Th)return oo;Th=1;var e=Re(),t=Wr(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return oo=i,oo}var uo,jh;function $t(){if(jh)return uo;jh=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return uo=e,uo}var co,Mh;function df(){if(Mh)return co;Mh=1;var e=wt(),t=$t(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var c=e(u);return c==n||c==i||c==r||c==a}return co=o,co}var so,$h;function gw(){if($h)return so;$h=1;var e=ct(),t=e["__core-js_shared__"];return so=t,so}var lo,Ch;function bw(){if(Ch)return lo;Ch=1;var e=gw(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return lo=r,lo}var fo,Ih;function ib(){if(Ih)return fo;Ih=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch(i){}try{return n+""}catch(i){}}return""}return fo=r,fo}var ho,Dh;function xw(){if(Dh)return ho;Dh=1;var e=df(),t=bw(),r=$t(),n=ib(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,c=o.toString,s=u.hasOwnProperty,f=RegExp("^"+c.call(s).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var p=e(h)?f:a;return p.test(n(h))}return ho=l,ho}var po,kh;function ww(){if(kh)return po;kh=1;function e(t,r){return t==null?void 0:t[r]}return po=e,po}var vo,Rh;function er(){if(Rh)return vo;Rh=1;var e=xw(),t=ww();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return vo=r,vo}var yo,Nh;function ma(){if(Nh)return yo;Nh=1;var e=er(),t=e(Object,"create");return yo=t,yo}var mo,qh;function Ow(){if(qh)return mo;qh=1;var e=ma();function t(){this.__data__=e?e(null):{},this.size=0}return mo=t,mo}var go,Lh;function _w(){if(Lh)return go;Lh=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return go=e,go}var bo,Bh;function Sw(){if(Bh)return bo;Bh=1;var e=ma(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return bo=i,bo}var xo,Fh;function Aw(){if(Fh)return xo;Fh=1;var e=ma(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return xo=n,xo}var wo,zh;function Pw(){if(zh)return wo;zh=1;var e=ma(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return wo=r,wo}var Oo,Wh;function Ew(){if(Wh)return Oo;Wh=1;var e=Ow(),t=_w(),r=Sw(),n=Aw(),i=Pw();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Oo=a,Oo}var _o,Uh;function Tw(){if(Uh)return _o;Uh=1;function e(){this.__data__=[],this.size=0}return _o=e,_o}var So,Hh;function vf(){if(Hh)return So;Hh=1;function e(t,r){return t===r||t!==t&&r!==r}return So=e,So}var Ao,Gh;function ga(){if(Gh)return Ao;Gh=1;var e=vf();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return Ao=t,Ao}var Po,Kh;function jw(){if(Kh)return Po;Kh=1;var e=ga(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return Po=n,Po}var Eo,Vh;function Mw(){if(Vh)return Eo;Vh=1;var e=ga();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return Eo=t,Eo}var To,Xh;function $w(){if(Xh)return To;Xh=1;var e=ga();function t(r){return e(this.__data__,r)>-1}return To=t,To}var jo,Yh;function Cw(){if(Yh)return jo;Yh=1;var e=ga();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return jo=t,jo}var Mo,Zh;function ba(){if(Zh)return Mo;Zh=1;var e=Tw(),t=jw(),r=Mw(),n=$w(),i=Cw();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Mo=a,Mo}var $o,Jh;function yf(){if(Jh)return $o;Jh=1;var e=er(),t=ct(),r=e(t,"Map");return $o=r,$o}var Co,Qh;function Iw(){if(Qh)return Co;Qh=1;var e=Ew(),t=ba(),r=yf();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return Co=n,Co}var Io,ep;function Dw(){if(ep)return Io;ep=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return Io=e,Io}var Do,tp;function xa(){if(tp)return Do;tp=1;var e=Dw();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return Do=t,Do}var ko,rp;function kw(){if(rp)return ko;rp=1;var e=xa();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return ko=t,ko}var Ro,np;function Rw(){if(np)return Ro;np=1;var e=xa();function t(r){return e(this,r).get(r)}return Ro=t,Ro}var No,ip;function Nw(){if(ip)return No;ip=1;var e=xa();function t(r){return e(this,r).has(r)}return No=t,No}var qo,ap;function qw(){if(ap)return qo;ap=1;var e=xa();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return qo=t,qo}var Lo,op;function mf(){if(op)return Lo;op=1;var e=Iw(),t=kw(),r=Rw(),n=Nw(),i=qw();function a(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var s=o[u];this.set(s[0],s[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,Lo=a,Lo}var Bo,up;function ab(){if(up)return Bo;up=1;var e=mf(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],c=a.cache;if(c.has(u))return c.get(u);var s=n.apply(this,o);return a.cache=c.set(u,s)||c,s};return a.cache=new(r.Cache||e),a}return r.Cache=e,Bo=r,Bo}var Fo,cp;function Lw(){if(cp)return Fo;cp=1;var e=ab(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return Fo=r,Fo}var zo,sp;function Bw(){if(sp)return zo;sp=1;var e=Lw(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,c,s){a.push(c?s.replace(r,"$1"):u||o)}),a});return zo=n,zo}var Wo,lp;function gf(){if(lp)return Wo;lp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return Wo=e,Wo}var Uo,fp;function Fw(){if(fp)return Uo;fp=1;var e=Zn(),t=gf(),r=Re(),n=Wr(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var c=u+"";return c=="0"&&1/u==-1/0?"-0":c}return Uo=o,Uo}var Ho,hp;function ob(){if(hp)return Ho;hp=1;var e=Fw();function t(r){return r==null?"":e(r)}return Ho=t,Ho}var Go,pp;function ub(){if(pp)return Go;pp=1;var e=Re(),t=pf(),r=Bw(),n=ob();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return Go=i,Go}var Ko,dp;function wa(){if(dp)return Ko;dp=1;var e=Wr();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return Ko=t,Ko}var Vo,vp;function bf(){if(vp)return Vo;vp=1;var e=ub(),t=wa();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return Vo=r,Vo}var Xo,yp;function cb(){if(yp)return Xo;yp=1;var e=bf();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return Xo=t,Xo}var zw=cb();const Ge=le(zw);var Yo,mp;function Ww(){if(mp)return Yo;mp=1;function e(t){return t==null}return Yo=e,Yo}var Uw=Ww();const J=le(Uw);var Zo,gp;function Hw(){if(gp)return Zo;gp=1;var e=wt(),t=Re(),r=Ot(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return Zo=i,Zo}var Gw=Hw();const Xt=le(Gw);var Kw=df();const V=le(Kw);var Vw=$t();const Ur=le(Vw);var Jo={exports:{}},ne={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bp;function Xw(){if(bp)return ne;bp=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(d){if(typeof d=="object"&&d!==null){var b=d.$$typeof;switch(b){case e:switch(d=d.type,d){case r:case i:case n:case s:case f:return d;default:switch(d=d&&d.$$typeof,d){case u:case o:case c:case h:case l:case a:return d;default:return b}}case t:return b}}}return ne.ContextConsumer=o,ne.ContextProvider=a,ne.Element=e,ne.ForwardRef=c,ne.Fragment=r,ne.Lazy=h,ne.Memo=l,ne.Portal=t,ne.Profiler=i,ne.StrictMode=n,ne.Suspense=s,ne.SuspenseList=f,ne.isAsyncMode=function(){return!1},ne.isConcurrentMode=function(){return!1},ne.isContextConsumer=function(d){return v(d)===o},ne.isContextProvider=function(d){return v(d)===a},ne.isElement=function(d){return typeof d=="object"&&d!==null&&d.$$typeof===e},ne.isForwardRef=function(d){return v(d)===c},ne.isFragment=function(d){return v(d)===r},ne.isLazy=function(d){return v(d)===h},ne.isMemo=function(d){return v(d)===l},ne.isPortal=function(d){return v(d)===t},ne.isProfiler=function(d){return v(d)===i},ne.isStrictMode=function(d){return v(d)===n},ne.isSuspense=function(d){return v(d)===s},ne.isSuspenseList=function(d){return v(d)===f},ne.isValidElementType=function(d){return typeof d=="string"||typeof d=="function"||d===r||d===i||d===n||d===s||d===f||d===p||typeof d=="object"&&d!==null&&(d.$$typeof===h||d.$$typeof===l||d.$$typeof===a||d.$$typeof===o||d.$$typeof===c||d.$$typeof===y||d.getModuleId!==void 0)},ne.typeOf=v,ne}var xp;function Yw(){return xp||(xp=1,Jo.exports=Xw()),Jo.exports}var Zw=Yw(),Qo,wp;function sb(){if(wp)return Qo;wp=1;var e=wt(),t=Ot(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return Qo=n,Qo}var eu,Op;function Jw(){if(Op)return eu;Op=1;var e=sb();function t(r){return e(r)&&r!=+r}return eu=t,eu}var Qw=Jw();const Hr=le(Qw);var eO=sb();const tO=le(eO);var et=function(t){return t===0?0:t>0?1:-1},zt=function(t){return Xt(t)&&t.indexOf("%")===t.length-1},N=function(t){return tO(t)&&!Hr(t)},xe=function(t){return N(t)||Xt(t)},rO=0,Gr=function(t){var r=++rO;return"".concat(t||"").concat(r)},Yt=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!N(t)&&!Xt(t))return n;var a;if(zt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Hr(a)&&(a=n),i&&a>r&&(a=r),a},Et=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},nO=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},Ae=function(t,r){return N(t)&&N(r)?function(n){return t+n*(r-t)}:function(){return r}};function bi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ge(n,t))===r})}var y2=function(t){if(!t||!t.length)return null;for(var r=t.length,n=0,i=0,a=0,o=0,u=1/0,c=-1/0,s=0,f=0,l=0;l<r;l++)s=t[l].cx||0,f=t[l].cy||0,n+=s,i+=f,a+=s*f,o+=s*s,u=Math.min(u,s),c=Math.max(c,s);var h=r*o!==n*n?(r*a-n*i)/(r*o-n*n):0;return{xmin:u,xmax:c,a:h,b:(i-h*n)/r}},iO=function(t,r){return N(t)&&N(r)?t-r:Xt(t)&&Xt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function dr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Vs(e){"@babel/helpers - typeof";return Vs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vs(e)}var aO=["viewBox","children"],oO=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],_p=["points","pathLength"],tu={svg:aO,polygon:_p,polyline:_p},xf=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],xi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!Ur(n))return null;var i={};return Object.keys(n).forEach(function(a){xf.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},uO=function(t,r,n){return function(i){return t(r,n,i),null}},wi=function(t,r,n){if(!Ur(t)||Vs(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];xf.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=uO(o,r,n))}),i},cO=["children"],sO=["children"];function Sp(e,t){if(e==null)return{};var r=lO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Xs(e){"@babel/helpers - typeof";return Xs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xs(e)}var Ap={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},dt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Pp=null,ru=null,wf=function e(t){if(t===Pp&&Array.isArray(ru))return ru;var r=[];return q.Children.forEach(t,function(n){J(n)||(Zw.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),ru=r,Pp=t,r};function Ke(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return dt(i)}):n=[dt(t)],wf(e).forEach(function(i){var a=Ge(i,"type.displayName")||Ge(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Be(e,t){var r=Ke(e,t);return r&&r[0]}var Ep=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!N(n)||n<=0||!N(i)||i<=0)},fO=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],hO=function(t){return t&&t.type&&Xt(t.type)&&fO.indexOf(t.type)>=0},lb=function(t){return t&&Xs(t)==="object"&&"clipDot"in t},pO=function(t,r,n,i){var a,o=(a=tu==null?void 0:tu[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!V(t)&&(i&&o.includes(r)||oO.includes(r))||n&&xf.includes(r)},Y=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!Ur(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;pO((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Ys=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Tp(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Tp(a,o))return!1}return!0},Tp=function(t,r){if(J(t)&&J(r))return!0;if(!J(t)&&!J(r)){var n=t.props||{},i=n.children,a=Sp(n,cO),o=r.props||{},u=o.children,c=Sp(o,sO);return i&&u?dr(a,c)&&Ys(i,u):!i&&!u?dr(a,c):!1}return!1},jp=function(t,r){var n=[],i={};return wf(t).forEach(function(a,o){if(hO(a))n.push(a);else if(a){var u=dt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},dO=function(t){var r=t&&t.type;return r&&Ap[r]?Ap[r]:null},vO=function(t,r){return wf(r).indexOf(t)},yO=["children","width","height","viewBox","className","style","title","desc"];function Zs(){return Zs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zs.apply(this,arguments)}function mO(e,t){if(e==null)return{};var r=gO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function gO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Js(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=mO(e,yO),f=i||{width:r,height:n,x:0,y:0},l=Q("recharts-surface",a);return A.createElement("svg",Zs({},Y(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),A.createElement("title",null,u),A.createElement("desc",null,c),t)}var bO=["children","className"];function Qs(){return Qs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qs.apply(this,arguments)}function xO(e,t){if(e==null)return{};var r=wO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function wO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var ue=A.forwardRef(function(e,t){var r=e.children,n=e.className,i=xO(e,bO),a=Q("recharts-layer",n);return A.createElement("g",Qs({className:a},Y(i,!0),{ref:t}),r)}),vt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},nu,Mp;function OO(){if(Mp)return nu;Mp=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return nu=e,nu}var iu,$p;function _O(){if($p)return iu;$p=1;var e=OO();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return iu=t,iu}var au,Cp;function fb(){if(Cp)return au;Cp=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function c(s){return u.test(s)}return au=c,au}var ou,Ip;function SO(){if(Ip)return ou;Ip=1;function e(t){return t.split("")}return ou=e,ou}var uu,Dp;function AO(){if(Dp)return uu;Dp=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",c="\\ud83c[\\udffb-\\udfff]",s="(?:"+u+"|"+c+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",p="\\u200d",y=s+"?",v="["+a+"]?",d="(?:"+p+"(?:"+[f,l,h].join("|")+")"+v+y+")*",b=v+y+d,x="(?:"+[f+u+"?",u,l,h,o].join("|")+")",w=RegExp(c+"(?="+c+")|"+x+b,"g");function O(m){return m.match(w)||[]}return uu=O,uu}var cu,kp;function PO(){if(kp)return cu;kp=1;var e=SO(),t=fb(),r=AO();function n(i){return t(i)?r(i):e(i)}return cu=n,cu}var su,Rp;function EO(){if(Rp)return su;Rp=1;var e=_O(),t=fb(),r=PO(),n=ob();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,c=u?u[0]:o.charAt(0),s=u?e(u,1).join(""):o.slice(1);return c[a]()+s}}return su=i,su}var lu,Np;function TO(){if(Np)return lu;Np=1;var e=EO(),t=e("toUpperCase");return lu=t,lu}var jO=TO();const Oa=le(jO);function oe(e){return function(){return e}}const hb=Math.cos,Oi=Math.sin,tt=Math.sqrt,_i=Math.PI,_a=2*_i,el=Math.PI,tl=2*el,Bt=1e-6,MO=tl-Bt;function pb(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function $O(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return pb;const r=gh(10,t);return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class CO{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?pb:$O(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Bt)if(!(Math.abs(l*c-s*f)>Bt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let p=n-o,y=i-u,v=c*c+s*s,d=p*p+y*y,b=Math.sqrt(v),x=Math.sqrt(h),w=a*Math.tan((el-Math.acos((v+h-d)/(2*b*x)))/2),O=w/x,m=w/b;Math.abs(O-1)>Bt&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*p>f*y)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Bt||Math.abs(this._y1-f)>Bt)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%tl+tl),h>MO?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>Bt&&this._append`A${n},${n},0,${+(h>=el)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Of(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new CO(t)}function _f(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function db(e){this._context=e}db.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Sa(e){return new db(e)}function vb(e){return e[0]}function yb(e){return e[1]}function mb(e,t){var r=oe(!0),n=null,i=Sa,a=null,o=Of(u);e=typeof e=="function"?e:e===void 0?vb:oe(e),t=typeof t=="function"?t:t===void 0?yb:oe(t);function u(c){var s,f=(c=_f(c)).length,l,h=!1,p;for(n==null&&(a=i(p=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,s,c),+t(l,s,c));if(p)return a=null,p+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:oe(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:oe(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:oe(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function oi(e,t,r){var n=null,i=oe(!0),a=null,o=Sa,u=null,c=Of(s);e=typeof e=="function"?e:e===void 0?vb:oe(+e),t=typeof t=="function"?t:oe(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?yb:oe(+r);function s(l){var h,p,y,v=(l=_f(l)).length,d,b=!1,x,w=new Array(v),O=new Array(v);for(a==null&&(u=o(x=c())),h=0;h<=v;++h){if(!(h<v&&i(d=l[h],h,l))===b)if(b=!b)p=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=p;--y)u.point(w[y],O[y]);u.lineEnd(),u.areaEnd()}b&&(w[h]=+e(d,h,l),O[h]=+t(d,h,l),u.point(n?+n(d,h,l):w[h],r?+r(d,h,l):O[h]))}if(x)return u=null,x+""||null}function f(){return mb().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:oe(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:oe(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:oe(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class gb{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function IO(e){return new gb(e,!0)}function DO(e){return new gb(e,!1)}const Sf={draw(e,t){const r=tt(t/_i);e.moveTo(r,0),e.arc(0,0,r,0,_a)}},kO={draw(e,t){const r=tt(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},bb=tt(1/3),RO=bb*2,NO={draw(e,t){const r=tt(t/RO),n=r*bb;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},qO={draw(e,t){const r=tt(t),n=-r/2;e.rect(n,n,r,r)}},LO=.8908130915292852,xb=Oi(_i/10)/Oi(7*_i/10),BO=Oi(_a/10)*xb,FO=-hb(_a/10)*xb,zO={draw(e,t){const r=tt(t*LO),n=BO*r,i=FO*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=_a*a/5,u=hb(o),c=Oi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},fu=tt(3),WO={draw(e,t){const r=-tt(t/(fu*3));e.moveTo(0,r*2),e.lineTo(-fu*r,-r),e.lineTo(fu*r,-r),e.closePath()}},ze=-.5,We=tt(3)/2,rl=1/tt(12),UO=(rl/2+1)*3,HO={draw(e,t){const r=tt(t/UO),n=r/2,i=r*rl,a=n,o=r*rl+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(ze*n-We*i,We*n+ze*i),e.lineTo(ze*a-We*o,We*a+ze*o),e.lineTo(ze*u-We*c,We*u+ze*c),e.lineTo(ze*n+We*i,ze*i-We*n),e.lineTo(ze*a+We*o,ze*o-We*a),e.lineTo(ze*u+We*c,ze*c-We*u),e.closePath()}};function GO(e,t){let r=null,n=Of(i);e=typeof e=="function"?e:oe(e||Sf),t=typeof t=="function"?t:oe(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:oe(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:oe(+a),i):t},i.context=function(a){return arguments.length?(r=a==null?null:a,i):r},i}function Si(){}function Ai(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function wb(e){this._context=e}wb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ai(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ai(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function KO(e){return new wb(e)}function Ob(e){this._context=e}Ob.prototype={areaStart:Si,areaEnd:Si,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ai(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function VO(e){return new Ob(e)}function _b(e){this._context=e}_b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ai(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function XO(e){return new _b(e)}function Sb(e){this._context=e}Sb.prototype={areaStart:Si,areaEnd:Si,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function YO(e){return new Sb(e)}function qp(e){return e<0?-1:1}function Lp(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(qp(a)+qp(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Bp(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function hu(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Pi(e){this._context=e}Pi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:hu(this,this._t0,Bp(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,hu(this,Bp(this,r=Lp(this,e,t)),r);break;default:hu(this,this._t0,r=Lp(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Ab(e){this._context=new Pb(e)}(Ab.prototype=Object.create(Pi.prototype)).point=function(e,t){Pi.prototype.point.call(this,t,e)};function Pb(e){this._context=e}Pb.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function ZO(e){return new Pi(e)}function JO(e){return new Ab(e)}function Eb(e){this._context=e}Eb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Fp(e),i=Fp(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Fp(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function QO(e){return new Eb(e)}function Aa(e,t){this._context=e,this._t=t}Aa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function e1(e){return new Aa(e,.5)}function t1(e){return new Aa(e,0)}function r1(e){return new Aa(e,1)}function br(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function nl(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function n1(e,t){return e[t]}function i1(e){const t=[];return t.key=e,t}function a1(){var e=oe([]),t=nl,r=br,n=n1;function i(a){var o=Array.from(e.apply(this,arguments),i1),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=_f(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:oe(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:oe(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?nl:typeof a=="function"?a:oe(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a==null?br:a,i):r},i}function o1(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}br(e,t)}}function u1(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}br(e,t)}}function c1(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,p=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],d=v[n][1]||0,b=v[n-1][1]||0;p+=d-b}c+=l,s+=p*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,br(e,t)}}function yn(e){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yn(e)}var s1=["type","size","sizeType"];function il(){return il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},il.apply(this,arguments)}function zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zp(Object(r),!0).forEach(function(n){l1(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function l1(e,t,r){return t=f1(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f1(e){var t=h1(e,"string");return yn(t)=="symbol"?t:t+""}function h1(e,t){if(yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function p1(e,t){if(e==null)return{};var r=d1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function d1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Tb={symbolCircle:Sf,symbolCross:kO,symbolDiamond:NO,symbolSquare:qO,symbolStar:zO,symbolTriangle:WO,symbolWye:HO},v1=Math.PI/180,y1=function(t){var r="symbol".concat(Oa(t));return Tb[r]||Sf},m1=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*v1;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},g1=function(t,r){Tb["symbol".concat(Oa(t))]=r},Af=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=p1(t,s1),s=Wp(Wp({},c),{},{type:n,size:a,sizeType:u}),f=function(){var d=y1(n),b=GO().type(d).size(m1(a,u,n));return b()},l=s.className,h=s.cx,p=s.cy,y=Y(s,!0);return h===+h&&p===+p&&a===+a?A.createElement("path",il({},y,{className:Q("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(p,")"),d:f()})):null};Af.registerSymbol=g1;function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function al(){return al=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},al.apply(this,arguments)}function Up(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function b1(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Up(Object(r),!0).forEach(function(n){mn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Up(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function x1(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function w1(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Mb(n.key),n)}}function O1(e,t,r){return t&&w1(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function _1(e,t,r){return t=Ei(t),S1(e,jb()?Reflect.construct(t,r||[],Ei(e).constructor):t.apply(e,r))}function S1(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return A1(e)}function A1(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function jb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(jb=function(){return!!e})()}function Ei(e){return Ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ei(e)}function P1(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ol(e,t)}function ol(e,t){return ol=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ol(e,t)}function mn(e,t,r){return t=Mb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Mb(e){var t=E1(e,"string");return xr(t)=="symbol"?t:t+""}function E1(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ue=32,Pf=function(e){function t(){return x1(this,t),_1(this,t,arguments)}return P1(t,e),O1(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ue/2,o=Ue/6,u=Ue/3,c=n.inactive?i:n.color;if(n.type==="plainline")return A.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ue,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return A.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ue,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return A.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ue/8,"h").concat(Ue,"v").concat(Ue*3/4,"h").concat(-Ue,"z"),className:"recharts-legend-icon"});if(A.isValidElement(n.legendIcon)){var s=b1({},n);return delete s.legendIcon,A.cloneElement(n.legendIcon,s)}return A.createElement(Af,{fill:c,cx:a,cy:a,size:Ue,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Ue,height:Ue},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(p,y){var v=p.formatter||c,d=Q(mn(mn({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",p.inactive));if(p.type==="none")return null;var b=V(p.value)?null:p.value;vt(!V(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var x=p.inactive?s:p.color;return A.createElement("li",al({className:d,style:l,key:"legend-item-".concat(y)},wi(n.props,p,y)),A.createElement(Js,{width:o,height:o,viewBox:f,style:h},n.renderIcon(p)),A.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},v?v(b,p,y):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return A.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);mn(Pf,"displayName","Legend");mn(Pf,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var pu,Hp;function T1(){if(Hp)return pu;Hp=1;var e=ba();function t(){this.__data__=new e,this.size=0}return pu=t,pu}var du,Gp;function j1(){if(Gp)return du;Gp=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return du=e,du}var vu,Kp;function M1(){if(Kp)return vu;Kp=1;function e(t){return this.__data__.get(t)}return vu=e,vu}var yu,Vp;function $1(){if(Vp)return yu;Vp=1;function e(t){return this.__data__.has(t)}return yu=e,yu}var mu,Xp;function C1(){if(Xp)return mu;Xp=1;var e=ba(),t=yf(),r=mf(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var c=u.__data__;if(!t||c.length<n-1)return c.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(c)}return u.set(a,o),this.size=u.size,this}return mu=i,mu}var gu,Yp;function $b(){if(Yp)return gu;Yp=1;var e=ba(),t=T1(),r=j1(),n=M1(),i=$1(),a=C1();function o(u){var c=this.__data__=new e(u);this.size=c.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,gu=o,gu}var bu,Zp;function I1(){if(Zp)return bu;Zp=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return bu=t,bu}var xu,Jp;function D1(){if(Jp)return xu;Jp=1;function e(t){return this.__data__.has(t)}return xu=e,xu}var wu,Qp;function Cb(){if(Qp)return wu;Qp=1;var e=mf(),t=I1(),r=D1();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,wu=n,wu}var Ou,ed;function Ib(){if(ed)return Ou;ed=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return Ou=e,Ou}var _u,td;function Db(){if(td)return _u;td=1;function e(t,r){return t.has(r)}return _u=e,_u}var Su,rd;function kb(){if(rd)return Su;rd=1;var e=Cb(),t=Ib(),r=Db(),n=1,i=2;function a(o,u,c,s,f,l){var h=c&n,p=o.length,y=u.length;if(p!=y&&!(h&&y>p))return!1;var v=l.get(o),d=l.get(u);if(v&&d)return v==u&&d==o;var b=-1,x=!0,w=c&i?new e:void 0;for(l.set(o,u),l.set(u,o);++b<p;){var O=o[b],m=u[b];if(s)var g=h?s(m,O,b,u,o,l):s(O,m,b,o,u,l);if(g!==void 0){if(g)continue;x=!1;break}if(w){if(!t(u,function(_,S){if(!r(w,S)&&(O===_||f(O,_,c,s,l)))return w.push(S)})){x=!1;break}}else if(!(O===m||f(O,m,c,s,l))){x=!1;break}}return l.delete(o),l.delete(u),x}return Su=a,Su}var Au,nd;function k1(){if(nd)return Au;nd=1;var e=ct(),t=e.Uint8Array;return Au=t,Au}var Pu,id;function R1(){if(id)return Pu;id=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return Pu=e,Pu}var Eu,ad;function Ef(){if(ad)return Eu;ad=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return Eu=e,Eu}var Tu,od;function N1(){if(od)return Tu;od=1;var e=Zn(),t=k1(),r=vf(),n=kb(),i=R1(),a=Ef(),o=1,u=2,c="[object Boolean]",s="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",p="[object RegExp]",y="[object Set]",v="[object String]",d="[object Symbol]",b="[object ArrayBuffer]",x="[object DataView]",w=e?e.prototype:void 0,O=w?w.valueOf:void 0;function m(g,_,S,E,M,P,T){switch(S){case x:if(g.byteLength!=_.byteLength||g.byteOffset!=_.byteOffset)return!1;g=g.buffer,_=_.buffer;case b:return!(g.byteLength!=_.byteLength||!P(new t(g),new t(_)));case c:case s:case h:return r(+g,+_);case f:return g.name==_.name&&g.message==_.message;case p:case v:return g==_+"";case l:var j=i;case y:var C=E&o;if(j||(j=a),g.size!=_.size&&!C)return!1;var $=T.get(g);if($)return $==_;E|=u,T.set(g,_);var D=n(j(g),j(_),E,M,P,T);return T.delete(g),D;case d:if(O)return O.call(g)==O.call(_)}return!1}return Tu=m,Tu}var ju,ud;function Rb(){if(ud)return ju;ud=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return ju=e,ju}var Mu,cd;function q1(){if(cd)return Mu;cd=1;var e=Rb(),t=Re();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return Mu=r,Mu}var $u,sd;function L1(){if(sd)return $u;sd=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return $u=e,$u}var Cu,ld;function B1(){if(ld)return Cu;ld=1;function e(){return[]}return Cu=e,Cu}var Iu,fd;function F1(){if(fd)return Iu;fd=1;var e=L1(),t=B1(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return Iu=a,Iu}var Du,hd;function z1(){if(hd)return Du;hd=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return Du=e,Du}var ku,pd;function W1(){if(pd)return ku;pd=1;var e=wt(),t=Ot(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return ku=n,ku}var Ru,dd;function Tf(){if(dd)return Ru;dd=1;var e=W1(),t=Ot(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return Ru=a,Ru}var cn={exports:{}},Nu,vd;function U1(){if(vd)return Nu;vd=1;function e(){return!1}return Nu=e,Nu}cn.exports;var yd;function Nb(){return yd||(yd=1,function(e,t){var r=ct(),n=U1(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s}(cn,cn.exports)),cn.exports}var qu,md;function jf(){if(md)return qu;md=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i==null?e:i,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return qu=r,qu}var Lu,gd;function Mf(){if(gd)return Lu;gd=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return Lu=t,Lu}var Bu,bd;function H1(){if(bd)return Bu;bd=1;var e=wt(),t=Mf(),r=Ot(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",c="[object Function]",s="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",p="[object Set]",y="[object String]",v="[object WeakMap]",d="[object ArrayBuffer]",b="[object DataView]",x="[object Float32Array]",w="[object Float64Array]",O="[object Int8Array]",m="[object Int16Array]",g="[object Int32Array]",_="[object Uint8Array]",S="[object Uint8ClampedArray]",E="[object Uint16Array]",M="[object Uint32Array]",P={};P[x]=P[w]=P[O]=P[m]=P[g]=P[_]=P[S]=P[E]=P[M]=!0,P[n]=P[i]=P[d]=P[a]=P[b]=P[o]=P[u]=P[c]=P[s]=P[f]=P[l]=P[h]=P[p]=P[y]=P[v]=!1;function T(j){return r(j)&&t(j.length)&&!!P[e(j)]}return Bu=T,Bu}var Fu,xd;function qb(){if(xd)return Fu;xd=1;function e(t){return function(r){return t(r)}}return Fu=e,Fu}var sn={exports:{}};sn.exports;var wd;function G1(){return wd||(wd=1,function(e,t){var r=nb(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch(s){}}();e.exports=u}(sn,sn.exports)),sn.exports}var zu,Od;function Lb(){if(Od)return zu;Od=1;var e=H1(),t=qb(),r=G1(),n=r&&r.isTypedArray,i=n?t(n):e;return zu=i,zu}var Wu,_d;function K1(){if(_d)return Wu;_d=1;var e=z1(),t=Tf(),r=Re(),n=Nb(),i=jf(),a=Lb(),o=Object.prototype,u=o.hasOwnProperty;function c(s,f){var l=r(s),h=!l&&t(s),p=!l&&!h&&n(s),y=!l&&!h&&!p&&a(s),v=l||h||p||y,d=v?e(s.length,String):[],b=d.length;for(var x in s)(f||u.call(s,x))&&!(v&&(x=="length"||p&&(x=="offset"||x=="parent")||y&&(x=="buffer"||x=="byteLength"||x=="byteOffset")||i(x,b)))&&d.push(x);return d}return Wu=c,Wu}var Uu,Sd;function V1(){if(Sd)return Uu;Sd=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return Uu=t,Uu}var Hu,Ad;function Bb(){if(Ad)return Hu;Ad=1;function e(t,r){return function(n){return t(r(n))}}return Hu=e,Hu}var Gu,Pd;function X1(){if(Pd)return Gu;Pd=1;var e=Bb(),t=e(Object.keys,Object);return Gu=t,Gu}var Ku,Ed;function Y1(){if(Ed)return Ku;Ed=1;var e=V1(),t=X1(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return Ku=i,Ku}var Vu,Td;function Jn(){if(Td)return Vu;Td=1;var e=df(),t=Mf();function r(n){return n!=null&&t(n.length)&&!e(n)}return Vu=r,Vu}var Xu,jd;function Pa(){if(jd)return Xu;jd=1;var e=K1(),t=Y1(),r=Jn();function n(i){return r(i)?e(i):t(i)}return Xu=n,Xu}var Yu,Md;function Z1(){if(Md)return Yu;Md=1;var e=q1(),t=F1(),r=Pa();function n(i){return e(i,r,t)}return Yu=n,Yu}var Zu,$d;function J1(){if($d)return Zu;$d=1;var e=Z1(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,c,s,f){var l=u&t,h=e(a),p=h.length,y=e(o),v=y.length;if(p!=v&&!l)return!1;for(var d=p;d--;){var b=h[d];if(!(l?b in o:n.call(o,b)))return!1}var x=f.get(a),w=f.get(o);if(x&&w)return x==o&&w==a;var O=!0;f.set(a,o),f.set(o,a);for(var m=l;++d<p;){b=h[d];var g=a[b],_=o[b];if(c)var S=l?c(_,g,b,o,a,f):c(g,_,b,a,o,f);if(!(S===void 0?g===_||s(g,_,u,c,f):S)){O=!1;break}m||(m=b=="constructor")}if(O&&!m){var E=a.constructor,M=o.constructor;E!=M&&"constructor"in a&&"constructor"in o&&!(typeof E=="function"&&E instanceof E&&typeof M=="function"&&M instanceof M)&&(O=!1)}return f.delete(a),f.delete(o),O}return Zu=i,Zu}var Ju,Cd;function Q1(){if(Cd)return Ju;Cd=1;var e=er(),t=ct(),r=e(t,"DataView");return Ju=r,Ju}var Qu,Id;function e_(){if(Id)return Qu;Id=1;var e=er(),t=ct(),r=e(t,"Promise");return Qu=r,Qu}var ec,Dd;function Fb(){if(Dd)return ec;Dd=1;var e=er(),t=ct(),r=e(t,"Set");return ec=r,ec}var tc,kd;function t_(){if(kd)return tc;kd=1;var e=er(),t=ct(),r=e(t,"WeakMap");return tc=r,tc}var rc,Rd;function r_(){if(Rd)return rc;Rd=1;var e=Q1(),t=yf(),r=e_(),n=Fb(),i=t_(),a=wt(),o=ib(),u="[object Map]",c="[object Object]",s="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",p=o(e),y=o(t),v=o(r),d=o(n),b=o(i),x=a;return(e&&x(new e(new ArrayBuffer(1)))!=h||t&&x(new t)!=u||r&&x(r.resolve())!=s||n&&x(new n)!=f||i&&x(new i)!=l)&&(x=function(w){var O=a(w),m=O==c?w.constructor:void 0,g=m?o(m):"";if(g)switch(g){case p:return h;case y:return u;case v:return s;case d:return f;case b:return l}return O}),rc=x,rc}var nc,Nd;function n_(){if(Nd)return nc;Nd=1;var e=$b(),t=kb(),r=N1(),n=J1(),i=r_(),a=Re(),o=Nb(),u=Lb(),c=1,s="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,p=h.hasOwnProperty;function y(v,d,b,x,w,O){var m=a(v),g=a(d),_=m?f:i(v),S=g?f:i(d);_=_==s?l:_,S=S==s?l:S;var E=_==l,M=S==l,P=_==S;if(P&&o(v)){if(!o(d))return!1;m=!0,E=!1}if(P&&!E)return O||(O=new e),m||u(v)?t(v,d,b,x,w,O):r(v,d,_,b,x,w,O);if(!(b&c)){var T=E&&p.call(v,"__wrapped__"),j=M&&p.call(d,"__wrapped__");if(T||j){var C=T?v.value():v,$=j?d.value():d;return O||(O=new e),w(C,$,b,x,O)}}return P?(O||(O=new e),n(v,d,b,x,w,O)):!1}return nc=y,nc}var ic,qd;function $f(){if(qd)return ic;qd=1;var e=n_(),t=Ot();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return ic=r,ic}var ac,Ld;function i_(){if(Ld)return ac;Ld=1;var e=$b(),t=$f(),r=1,n=2;function i(a,o,u,c){var s=u.length,f=s,l=!c;if(a==null)return!f;for(a=Object(a);s--;){var h=u[s];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++s<f;){h=u[s];var p=h[0],y=a[p],v=h[1];if(l&&h[2]){if(y===void 0&&!(p in a))return!1}else{var d=new e;if(c)var b=c(y,v,p,a,o,d);if(!(b===void 0?t(v,y,r|n,c,d):b))return!1}}return!0}return ac=i,ac}var oc,Bd;function zb(){if(Bd)return oc;Bd=1;var e=$t();function t(r){return r===r&&!e(r)}return oc=t,oc}var uc,Fd;function a_(){if(Fd)return uc;Fd=1;var e=zb(),t=Pa();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return uc=r,uc}var cc,zd;function Wb(){if(zd)return cc;zd=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return cc=e,cc}var sc,Wd;function o_(){if(Wd)return sc;Wd=1;var e=i_(),t=a_(),r=Wb();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return sc=n,sc}var lc,Ud;function u_(){if(Ud)return lc;Ud=1;function e(t,r){return t!=null&&r in Object(t)}return lc=e,lc}var fc,Hd;function c_(){if(Hd)return fc;Hd=1;var e=ub(),t=Tf(),r=Re(),n=jf(),i=Mf(),a=wa();function o(u,c,s){c=e(c,u);for(var f=-1,l=c.length,h=!1;++f<l;){var p=a(c[f]);if(!(h=u!=null&&s(u,p)))break;u=u[p]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(p,l)&&(r(u)||t(u)))}return fc=o,fc}var hc,Gd;function s_(){if(Gd)return hc;Gd=1;var e=u_(),t=c_();function r(n,i){return n!=null&&t(n,i,e)}return hc=r,hc}var pc,Kd;function l_(){if(Kd)return pc;Kd=1;var e=$f(),t=cb(),r=s_(),n=pf(),i=zb(),a=Wb(),o=wa(),u=1,c=2;function s(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var p=t(h,f);return p===void 0&&p===l?r(h,f):e(l,p,u|c)}}return pc=s,pc}var dc,Vd;function Kr(){if(Vd)return dc;Vd=1;function e(t){return t}return dc=e,dc}var vc,Xd;function f_(){if(Xd)return vc;Xd=1;function e(t){return function(r){return r==null?void 0:r[t]}}return vc=e,vc}var yc,Yd;function h_(){if(Yd)return yc;Yd=1;var e=bf();function t(r){return function(n){return e(n,r)}}return yc=t,yc}var mc,Zd;function p_(){if(Zd)return mc;Zd=1;var e=f_(),t=h_(),r=pf(),n=wa();function i(a){return r(a)?e(n(a)):t(a)}return mc=i,mc}var gc,Jd;function Ct(){if(Jd)return gc;Jd=1;var e=o_(),t=l_(),r=Kr(),n=Re(),i=p_();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return gc=a,gc}var bc,Qd;function Ub(){if(Qd)return bc;Qd=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return bc=e,bc}var xc,ev;function d_(){if(ev)return xc;ev=1;function e(t){return t!==t}return xc=e,xc}var wc,tv;function v_(){if(tv)return wc;tv=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return wc=e,wc}var Oc,rv;function y_(){if(rv)return Oc;rv=1;var e=Ub(),t=d_(),r=v_();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return Oc=n,Oc}var _c,nv;function m_(){if(nv)return _c;nv=1;var e=y_();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return _c=t,_c}var Sc,iv;function g_(){if(iv)return Sc;iv=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return Sc=e,Sc}var Ac,av;function b_(){if(av)return Ac;av=1;function e(){}return Ac=e,Ac}var Pc,ov;function x_(){if(ov)return Pc;ov=1;var e=Fb(),t=b_(),r=Ef(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return Pc=i,Pc}var Ec,uv;function w_(){if(uv)return Ec;uv=1;var e=Cb(),t=m_(),r=g_(),n=Db(),i=x_(),a=Ef(),o=200;function u(c,s,f){var l=-1,h=t,p=c.length,y=!0,v=[],d=v;if(f)y=!1,h=r;else if(p>=o){var b=s?null:i(c);if(b)return a(b);y=!1,h=n,d=new e}else d=s?[]:v;e:for(;++l<p;){var x=c[l],w=s?s(x):x;if(x=f||x!==0?x:0,y&&w===w){for(var O=d.length;O--;)if(d[O]===w)continue e;s&&d.push(w),v.push(x)}else h(d,w,f)||(d!==v&&d.push(w),v.push(x))}return v}return Ec=u,Ec}var Tc,cv;function O_(){if(cv)return Tc;cv=1;var e=Ct(),t=w_();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return Tc=r,Tc}var __=O_();const sv=le(__);function Hb(e,t,r){return t===!0?sv(e,r):V(t)?sv(e,t):e}function wr(e){"@babel/helpers - typeof";return wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wr(e)}var S_=["ref"];function lv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function st(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lv(Object(r),!0).forEach(function(n){Ea(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function A_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Kb(n.key),n)}}function P_(e,t,r){return t&&fv(e.prototype,t),r&&fv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function E_(e,t,r){return t=Ti(t),T_(e,Gb()?Reflect.construct(t,r||[],Ti(e).constructor):t.apply(e,r))}function T_(e,t){if(t&&(wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return j_(e)}function j_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Gb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Gb=function(){return!!e})()}function Ti(e){return Ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ti(e)}function M_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ul(e,t)}function ul(e,t){return ul=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ul(e,t)}function Ea(e,t,r){return t=Kb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kb(e){var t=$_(e,"string");return wr(t)=="symbol"?t:t+""}function $_(e,t){if(wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function C_(e,t){if(e==null)return{};var r=I_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function I_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function D_(e){return e.value}function k_(e,t){if(A.isValidElement(e))return A.cloneElement(e,t);if(typeof e=="function")return A.createElement(e,t);t.ref;var r=C_(t,S_);return A.createElement(Pf,r)}var hv=1,vr=function(e){function t(){var r;A_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=E_(this,t,[].concat(i)),Ea(r,"lastBoundingBox",{width:-1,height:-1}),r}return M_(t,e),P_(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>hv||Math.abs(i.height-this.lastBoundingBox.height)>hv)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?st({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var p=this.getBBoxSnapshot();l={left:((s||0)-p.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return st(st({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=st(st({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return A.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(p){n.wrapperNode=p}},k_(a,st(st({},this.props),{},{payload:Hb(f,s,D_)})))}}],[{key:"getWithHeight",value:function(n,i){var a=st(st({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&N(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);Ea(vr,"displayName","Legend");Ea(vr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var jc,pv;function R_(){if(pv)return jc;pv=1;var e=Zn(),t=Tf(),r=Re(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return jc=i,jc}var Mc,dv;function Vb(){if(dv)return Mc;dv=1;var e=Rb(),t=R_();function r(n,i,a,o,u){var c=-1,s=n.length;for(a||(a=t),u||(u=[]);++c<s;){var f=n[c];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return Mc=r,Mc}var $c,vv;function N_(){if(vv)return $c;vv=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),c=u.length;c--;){var s=u[t?c:++a];if(n(o[s],s,o)===!1)break}return r}}return $c=e,$c}var Cc,yv;function q_(){if(yv)return Cc;yv=1;var e=N_(),t=e();return Cc=t,Cc}var Ic,mv;function Xb(){if(mv)return Ic;mv=1;var e=q_(),t=Pa();function r(n,i){return n&&e(n,i,t)}return Ic=r,Ic}var Dc,gv;function L_(){if(gv)return Dc;gv=1;var e=Jn();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,c=Object(i);(n?u--:++u<o)&&a(c[u],u,c)!==!1;);return i}}return Dc=t,Dc}var kc,bv;function Cf(){if(bv)return kc;bv=1;var e=Xb(),t=L_(),r=t(e);return kc=r,kc}var Rc,xv;function Yb(){if(xv)return Rc;xv=1;var e=Cf(),t=Jn();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,c,s){o[++a]=i(u,c,s)}),o}return Rc=r,Rc}var Nc,wv;function B_(){if(wv)return Nc;wv=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return Nc=e,Nc}var qc,Ov;function F_(){if(Ov)return qc;Ov=1;var e=Wr();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),c=n!==void 0,s=n===null,f=n===n,l=e(n);if(!s&&!l&&!u&&r>n||u&&c&&f&&!s&&!l||a&&c&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||s&&i&&o||!c&&o||!f)return-1}return 0}return qc=t,qc}var Lc,_v;function z_(){if(_v)return Lc;_v=1;var e=F_();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,c=o.length,s=i.length;++a<c;){var f=e(o[a],u[a]);if(f){if(a>=s)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return Lc=t,Lc}var Bc,Sv;function W_(){if(Sv)return Bc;Sv=1;var e=gf(),t=bf(),r=Ct(),n=Yb(),i=B_(),a=qb(),o=z_(),u=Kr(),c=Re();function s(f,l,h){l.length?l=e(l,function(v){return c(v)?function(d){return t(d,v.length===1?v[0]:v)}:v}):l=[u];var p=-1;l=e(l,a(r));var y=n(f,function(v,d,b){var x=e(l,function(w){return w(v)});return{criteria:x,index:++p,value:v}});return i(y,function(v,d){return o(v,d,h)})}return Bc=s,Bc}var Fc,Av;function U_(){if(Av)return Fc;Av=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return Fc=e,Fc}var zc,Pv;function H_(){if(Pv)return zc;Pv=1;var e=U_(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,c=t(o.length-i,0),s=Array(c);++u<c;)s[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(s),e(n,this,f)}}return zc=r,zc}var Wc,Ev;function G_(){if(Ev)return Wc;Ev=1;function e(t){return function(){return t}}return Wc=e,Wc}var Uc,Tv;function Zb(){if(Tv)return Uc;Tv=1;var e=er(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch(n){}}();return Uc=t,Uc}var Hc,jv;function K_(){if(jv)return Hc;jv=1;var e=G_(),t=Zb(),r=Kr(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return Hc=n,Hc}var Gc,Mv;function V_(){if(Mv)return Gc;Mv=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),c=t-(u-o);if(o=u,c>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return Gc=n,Gc}var Kc,$v;function X_(){if($v)return Kc;$v=1;var e=K_(),t=V_(),r=t(e);return Kc=r,Kc}var Vc,Cv;function Y_(){if(Cv)return Vc;Cv=1;var e=Kr(),t=H_(),r=X_();function n(i,a){return r(t(i,a,e),i+"")}return Vc=n,Vc}var Xc,Iv;function Ta(){if(Iv)return Xc;Iv=1;var e=vf(),t=Jn(),r=jf(),n=$t();function i(a,o,u){if(!n(u))return!1;var c=typeof o;return(c=="number"?t(u)&&r(o,u.length):c=="string"&&o in u)?e(u[o],a):!1}return Xc=i,Xc}var Yc,Dv;function Z_(){if(Dv)return Yc;Dv=1;var e=Vb(),t=W_(),r=Y_(),n=Ta(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return Yc=i,Yc}var J_=Z_();const If=le(J_);function gn(e){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gn(e)}function cl(){return cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},cl.apply(this,arguments)}function Q_(e,t){return nS(e)||rS(e,t)||tS(e,t)||eS()}function eS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function tS(e,t){if(e){if(typeof e=="string")return kv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kv(e,t)}}function kv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function rS(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function nS(e){if(Array.isArray(e))return e}function Rv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rv(Object(r),!0).forEach(function(n){iS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function iS(e,t,r){return t=aS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aS(e){var t=oS(e,"string");return gn(t)=="symbol"?t:t+""}function oS(e,t){if(gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uS(e){return Array.isArray(e)&&xe(e[0])&&xe(e[1])?e.join(" ~ "):e}var cS=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,p=t.wrapperClassName,y=t.labelClassName,v=t.label,d=t.labelFormatter,b=t.accessibilityLayer,x=b===void 0?!1:b,w=function(){if(f&&f.length){var T={padding:0,margin:0},j=(h?If(f,h):f).map(function(C,$){if(C.type==="none")return null;var D=Zc({display:"block",paddingTop:4,paddingBottom:4,color:C.color||"#000"},u),k=C.formatter||l||uS,L=C.value,B=C.name,U=L,G=B;if(k&&U!=null&&G!=null){var z=k(L,B,C,$,f);if(Array.isArray(z)){var K=Q_(z,2);U=K[0],G=K[1]}else U=z}return A.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat($),style:D},xe(G)?A.createElement("span",{className:"recharts-tooltip-item-name"},G):null,xe(G)?A.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,A.createElement("span",{className:"recharts-tooltip-item-value"},U),A.createElement("span",{className:"recharts-tooltip-item-unit"},C.unit||""))});return A.createElement("ul",{className:"recharts-tooltip-item-list",style:T},j)}return null},O=Zc({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=Zc({margin:0},s),g=!J(v),_=g?v:"",S=Q("recharts-default-tooltip",p),E=Q("recharts-tooltip-label",y);g&&d&&f!==void 0&&f!==null&&(_=d(v,f));var M=x?{role:"status","aria-live":"assertive"}:{};return A.createElement("div",cl({className:S,style:O},M),A.createElement("p",{className:E,style:m},A.isValidElement(_)?_:"".concat(_)),w())};function bn(e){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bn(e)}function ui(e,t,r){return t=sS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sS(e){var t=lS(e,"string");return bn(t)=="symbol"?t:t+""}function lS(e,t){if(bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jr="recharts-tooltip-wrapper",fS={visibility:"hidden"};function hS(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return Q(Jr,ui(ui(ui(ui({},"".concat(Jr,"-right"),N(r)&&t&&N(t.x)&&r>=t.x),"".concat(Jr,"-left"),N(r)&&t&&N(t.x)&&r<t.x),"".concat(Jr,"-bottom"),N(n)&&t&&N(t.y)&&n>=t.y),"".concat(Jr,"-top"),N(n)&&t&&N(t.y)&&n<t.y))}function Nv(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&N(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,p=c[n];return h<p?Math.max(l,c[n]):Math.max(f,c[n])}var y=l+u,v=c[n]+s;return y>v?Math.max(f,c[n]):Math.max(l,c[n])}function pS(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function dS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=Nv({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=Nv({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=pS({translateX:f,translateY:l,useTranslate3d:u})):s=fS,{cssProperties:s,cssClasses:hS({translateX:f,translateY:l,coordinate:r})}}function Or(e){"@babel/helpers - typeof";return Or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Or(e)}function qv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qv(Object(r),!0).forEach(function(n){ll(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function yS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qb(n.key),n)}}function mS(e,t,r){return t&&yS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function gS(e,t,r){return t=ji(t),bS(e,Jb()?Reflect.construct(t,r||[],ji(e).constructor):t.apply(e,r))}function bS(e,t){if(t&&(Or(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xS(e)}function xS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Jb=function(){return!!e})()}function ji(e){return ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ji(e)}function wS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sl(e,t)}function sl(e,t){return sl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},sl(e,t)}function ll(e,t,r){return t=Qb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qb(e){var t=OS(e,"string");return Or(t)=="symbol"?t:t+""}function OS(e,t){if(Or(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Or(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Bv=1,_S=function(e){function t(){var r;vS(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=gS(this,t,[].concat(i)),ll(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ll(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return wS(t,e),mS(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Bv||Math.abs(n.height-this.state.lastBoundingBox.height)>Bv)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,p=i.offset,y=i.position,v=i.reverseDirection,d=i.useTranslate3d,b=i.viewBox,x=i.wrapperStyle,w=dS({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:p,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:b}),O=w.cssClasses,m=w.cssProperties,g=Lv(Lv({transition:h&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},x);return A.createElement("div",{tabIndex:-1,className:O,style:g,ref:function(S){n.wrapperNode=S}},s)}}])}(q.PureComponent),SS=function(){return!(typeof window!="undefined"&&window.document&&window.document.createElement&&window.setTimeout)},tr={isSsr:SS()};function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}function Fv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fv(Object(r),!0).forEach(function(n){Df(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function AS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function PS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,t0(n.key),n)}}function ES(e,t,r){return t&&PS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function TS(e,t,r){return t=Mi(t),jS(e,e0()?Reflect.construct(t,r||[],Mi(e).constructor):t.apply(e,r))}function jS(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return MS(e)}function MS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e0=function(){return!!e})()}function Mi(e){return Mi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Mi(e)}function $S(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fl(e,t)}function fl(e,t){return fl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},fl(e,t)}function Df(e,t,r){return t=t0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function t0(e){var t=CS(e,"string");return _r(t)=="symbol"?t:t+""}function CS(e,t){if(_r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function IS(e){return e.dataKey}function DS(e,t){return A.isValidElement(e)?A.cloneElement(e,t):typeof e=="function"?A.createElement(e,t):A.createElement(cS,t)}var lt=function(e){function t(){return AS(this,t),TS(this,t,arguments)}return $S(t,e),ES(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,p=i.offset,y=i.payload,v=i.payloadUniqBy,d=i.position,b=i.reverseDirection,x=i.useTranslate3d,w=i.viewBox,O=i.wrapperStyle,m=y!=null?y:[];l&&m.length&&(m=Hb(y.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),v,IS));var g=m.length>0;return A.createElement(_S,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:a,coordinate:f,hasPayload:g,offset:p,position:d,reverseDirection:b,useTranslate3d:x,viewBox:w,wrapperStyle:O},DS(s,zv(zv({},this.props),{},{payload:m})))}}])}(q.PureComponent);Df(lt,"displayName","Tooltip");Df(lt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!tr.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Jc,Wv;function kS(){if(Wv)return Jc;Wv=1;var e=ct(),t=function(){return e.Date.now()};return Jc=t,Jc}var Qc,Uv;function RS(){if(Uv)return Qc;Uv=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return Qc=t,Qc}var es,Hv;function NS(){if(Hv)return es;Hv=1;var e=RS(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return es=r,es}var ts,Gv;function r0(){if(Gv)return ts;Gv=1;var e=NS(),t=$t(),r=Wr(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function c(s){if(typeof s=="number")return s;if(r(s))return n;if(t(s)){var f=typeof s.valueOf=="function"?s.valueOf():s;s=t(f)?f+"":f}if(typeof s!="string")return s===0?s:+s;s=e(s);var l=a.test(s);return l||o.test(s)?u(s.slice(2),l?2:8):i.test(s)?n:+s}return ts=c,ts}var rs,Kv;function qS(){if(Kv)return rs;Kv=1;var e=$t(),t=kS(),r=r0(),n="Expected a function",i=Math.max,a=Math.min;function o(u,c,s){var f,l,h,p,y,v,d=0,b=!1,x=!1,w=!0;if(typeof u!="function")throw new TypeError(n);c=r(c)||0,e(s)&&(b=!!s.leading,x="maxWait"in s,h=x?i(r(s.maxWait)||0,c):h,w="trailing"in s?!!s.trailing:w);function O(j){var C=f,$=l;return f=l=void 0,d=j,p=u.apply($,C),p}function m(j){return d=j,y=setTimeout(S,c),b?O(j):p}function g(j){var C=j-v,$=j-d,D=c-C;return x?a(D,h-$):D}function _(j){var C=j-v,$=j-d;return v===void 0||C>=c||C<0||x&&$>=h}function S(){var j=t();if(_(j))return E(j);y=setTimeout(S,g(j))}function E(j){return y=void 0,w&&f?O(j):(f=l=void 0,p)}function M(){y!==void 0&&clearTimeout(y),d=0,f=v=l=y=void 0}function P(){return y===void 0?p:E(t())}function T(){var j=t(),C=_(j);if(f=arguments,l=this,v=j,C){if(y===void 0)return m(v);if(x)return clearTimeout(y),y=setTimeout(S,c),O(v)}return y===void 0&&(y=setTimeout(S,c)),p}return T.cancel=M,T.flush=P,T}return rs=o,rs}var ns,Vv;function LS(){if(Vv)return ns;Vv=1;var e=qS(),t=$t(),r="Expected a function";function n(i,a,o){var u=!0,c=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,c="trailing"in o?!!o.trailing:c),e(i,a,{leading:u,maxWait:a,trailing:c})}return ns=n,ns}var BS=LS();const n0=le(BS);function xn(e){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xn(e)}function Xv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ci(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xv(Object(r),!0).forEach(function(n){FS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function FS(e,t,r){return t=zS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zS(e){var t=WS(e,"string");return xn(t)=="symbol"?t:t+""}function WS(e,t){if(xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function US(e,t){return VS(e)||KS(e,t)||GS(e,t)||HS()}function HS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GS(e,t){if(e){if(typeof e=="string")return Yv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yv(e,t)}}function Yv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function KS(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function VS(e){if(Array.isArray(e))return e}var m2=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,p=e.children,y=e.debounce,v=y===void 0?0:y,d=e.id,b=e.className,x=e.onResize,w=e.style,O=w===void 0?{}:w,m=q.useRef(null),g=q.useRef();g.current=x,q.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return m.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),S=US(_,2),E=S[0],M=S[1],P=q.useCallback(function(j,C){M(function($){var D=Math.round(j),k=Math.round(C);return $.containerWidth===D&&$.containerHeight===k?$:{containerWidth:D,containerHeight:k}})},[]);q.useEffect(function(){var j=function(B){var U,G=B[0].contentRect,z=G.width,K=G.height;P(z,K),(U=g.current)===null||U===void 0||U.call(g,z,K)};v>0&&(j=n0(j,v,{trailing:!0,leading:!1}));var C=new ResizeObserver(j),$=m.current.getBoundingClientRect(),D=$.width,k=$.height;return P(D,k),C.observe(m.current),function(){C.disconnect()}},[P,v]);var T=q.useMemo(function(){var j=E.containerWidth,C=E.containerHeight;if(j<0||C<0)return null;vt(zt(o)||zt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),vt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var $=zt(o)?j:o,D=zt(c)?C:c;r&&r>0&&($?D=$/r:D&&($=D*r),h&&D>h&&(D=h)),vt($>0||D>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,$,D,o,c,f,l,r);var k=!Array.isArray(p)&&dt(p.type).endsWith("Chart");return A.Children.map(p,function(L){return A.isValidElement(L)?q.cloneElement(L,ci({width:$,height:D},k?{style:ci({height:"100%",width:"100%",maxHeight:D,maxWidth:$},L.props.style)}:{})):L})},[r,p,c,h,l,f,E,o]);return A.createElement("div",{id:d?"".concat(d):void 0,className:Q("recharts-responsive-container",b),style:ci(ci({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:m},T)}),i0=function(t){return null};i0.displayName="Cell";function wn(e){"@babel/helpers - typeof";return wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wn(e)}function Zv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zv(Object(r),!0).forEach(function(n){XS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XS(e,t,r){return t=YS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function YS(e){var t=ZS(e,"string");return wn(t)=="symbol"?t:t+""}function ZS(e,t){if(wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ur={widthCache:{},cacheCount:0},JS=2e3,QS={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Jv="recharts_measurement_span";function eA(e){var t=hl({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var fn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||tr.isSsr)return{width:0,height:0};var n=eA(r),i=JSON.stringify({text:t,copyStyle:n});if(ur.widthCache[i])return ur.widthCache[i];try{var a=document.getElementById(Jv);a||(a=document.createElement("span"),a.setAttribute("id",Jv),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=hl(hl({},QS),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return ur.widthCache[i]=c,++ur.cacheCount>JS&&(ur.cacheCount=0,ur.widthCache={}),c}catch(s){return{width:0,height:0}}},tA=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function On(e){"@babel/helpers - typeof";return On=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},On(e)}function $i(e,t){return aA(e)||iA(e,t)||nA(e,t)||rA()}function rA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nA(e,t){if(e){if(typeof e=="string")return Qv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qv(e,t)}}function Qv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function iA(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function aA(e){if(Array.isArray(e))return e}function oA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ey(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cA(n.key),n)}}function uA(e,t,r){return t&&ey(e.prototype,t),r&&ey(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function cA(e){var t=sA(e,"string");return On(t)=="symbol"?t:t+""}function sA(e,t){if(On(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(On(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ty=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,ry=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,lA=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,fA=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,a0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},hA=Object.keys(a0),fr="NaN";function pA(e,t){return e*a0[t]}var si=function(){function e(t,r){oA(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!lA.test(r)&&(this.num=NaN,this.unit=""),hA.includes(r)&&(this.num=pA(t,r),this.unit="px")}return uA(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=fA.exec(r))!==null&&n!==void 0?n:[],a=$i(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u!=null?u:"")}}])}();function o0(e){if(e.includes(fr))return fr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=ty.exec(t))!==null&&r!==void 0?r:[],i=$i(n,4),a=i[1],o=i[2],u=i[3],c=si.parse(a!=null?a:""),s=si.parse(u!=null?u:""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return fr;t=t.replace(ty,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=ry.exec(t))!==null&&l!==void 0?l:[],p=$i(h,4),y=p[1],v=p[2],d=p[3],b=si.parse(y!=null?y:""),x=si.parse(d!=null?d:""),w=v==="+"?b.add(x):b.subtract(x);if(w.isNaN())return fr;t=t.replace(ry,w.toString())}return t}var ny=/\(([^()]*)\)/;function dA(e){for(var t=e;t.includes("(");){var r=ny.exec(t),n=$i(r,2),i=n[1];t=t.replace(ny,o0(i))}return t}function vA(e){var t=e.replace(/\s+/g,"");return t=dA(t),t=o0(t),t}function yA(e){try{return vA(e)}catch(t){return fr}}function is(e){var t=yA(e.slice(5,-1));return t===fr?"":t}var mA=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],gA=["dx","dy","angle","className","breakAll"];function pl(){return pl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pl.apply(this,arguments)}function iy(e,t){if(e==null)return{};var r=bA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function bA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ay(e,t){return _A(e)||OA(e,t)||wA(e,t)||xA()}function xA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wA(e,t){if(e){if(typeof e=="string")return oy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oy(e,t)}}function oy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function OA(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function _A(e){if(Array.isArray(e))return e}var u0=/[ \f\n\r\t\v\u2028\u2029]+/,c0=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];J(r)||(n?a=r.toString().split(""):a=r.toString().split(u0));var o=a.map(function(c){return{word:c,width:fn(c,i).width}}),u=n?0:fn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch(c){return null}},SA=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=N(o),l=u,h=function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return $.reduce(function(D,k){var L=k.word,B=k.width,U=D[D.length-1];if(U&&(i==null||a||U.width+B+n<Number(i)))U.words.push(L),U.width+=B+n;else{var G={words:[L],width:B};D.push(G)}return D},[])},p=h(r),y=function($){return $.reduce(function(D,k){return D.width>k.width?D:k})};if(!f)return p;for(var v="…",d=function($){var D=l.slice(0,$),k=c0({breakAll:s,style:c,children:D+v}).wordsWithComputedWidth,L=h(k),B=L.length>o||y(L).width>Number(i);return[B,L]},b=0,x=l.length-1,w=0,O;b<=x&&w<=l.length-1;){var m=Math.floor((b+x)/2),g=m-1,_=d(g),S=ay(_,2),E=S[0],M=S[1],P=d(m),T=ay(P,1),j=T[0];if(!E&&!j&&(b=m+1),E&&j&&(x=m-1),!E&&j){O=M;break}w++}return O||p},uy=function(t){var r=J(t)?[]:t.toString().split(u0);return[{words:r}]},AA=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!tr.isSsr){var c,s,f=c0({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return uy(i);return SA({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return uy(i)},cy="#808080",Ci=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,p=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,d=t.fill,b=d===void 0?cy:d,x=iy(t,mA),w=q.useMemo(function(){return AA({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:l,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,l,x.style,x.width]),O=x.dx,m=x.dy,g=x.angle,_=x.className,S=x.breakAll,E=iy(x,gA);if(!xe(n)||!xe(a))return null;var M=n+(N(O)?O:0),P=a+(N(m)?m:0),T;switch(v){case"start":T=is("calc(".concat(s,")"));break;case"middle":T=is("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:T=is("calc(".concat(w.length-1," * -").concat(u,")"));break}var j=[];if(l){var C=w[0].width,$=x.width;j.push("scale(".concat((N($)?$/C:1)/C,")"))}return g&&j.push("rotate(".concat(g,", ").concat(M,", ").concat(P,")")),j.length&&(E.transform=j.join(" ")),A.createElement("text",pl({},Y(E,!0),{x:M,y:P,className:Q("recharts-text",_),textAnchor:p,fill:b.includes("url")?cy:b}),w.map(function(D,k){var L=D.words.join(S?"":" ");return A.createElement("tspan",{x:M,dy:k===0?T:u,key:"".concat(L,"-").concat(k)},L)}))};function Mt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function PA(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function kf(e){let t,r,n;e.length!==2?(t=Mt,r=(u,c)=>Mt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Mt||e===PA?e:EA,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function EA(){return 0}function s0(e){return e===null?NaN:+e}function*TA(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const jA=kf(Mt),Qn=jA.right;kf(s0).center;class sy extends Map{constructor(t,r=CA){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(ly(this,t))}has(t){return super.has(ly(this,t))}set(t,r){return super.set(MA(this,t),r)}delete(t){return super.delete($A(this,t))}}function ly({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function MA({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function $A({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function CA(e){return e!==null&&typeof e=="object"?e.valueOf():e}function IA(e=Mt){if(e===Mt)return l0;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function l0(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const DA=Math.sqrt(50),kA=Math.sqrt(10),RA=Math.sqrt(2);function Ii(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=DA?10:a>=kA?5:a>=RA?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Ii(e,t,r*2):[u,c,s]}function dl(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Ii(t,e,r):Ii(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function vl(e,t,r){return t=+t,e=+e,r=+r,Ii(e,t,r)[2]}function yl(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?vl(t,e,r):vl(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function fy(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function hy(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function f0(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?l0:IA(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),p=Math.max(r,Math.floor(t-s*l/c+h)),y=Math.min(n,Math.floor(t+(c-s)*l/c+h));f0(e,t,p,y,i)}const a=e[t];let o=r,u=n;for(Qr(e,r,t),i(e[n],a)>0&&Qr(e,r,n);o<u;){for(Qr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Qr(e,r,u):(++u,Qr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Qr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function NA(e,t,r){if(e=Float64Array.from(TA(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return hy(e);if(t>=1)return fy(e);var n,i=(n-1)*t,a=Math.floor(i),o=fy(f0(e,a).subarray(0,a+1)),u=hy(e.subarray(a+1));return o+(u-o)*(i-a)}}function qA(e,t,r=s0){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function LA(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Xe(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function _t(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const ml=Symbol("implicit");function Rf(){var e=new sy,t=[],r=[],n=ml;function i(a){let o=e.get(a);if(o===void 0){if(n!==ml)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new sy;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Rf(t,r).unknown(n)},Xe.apply(i,arguments),i}function _n(){var e=Rf().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,p=i<n,y=p?i:n,v=p?n:i;a=(v-y)/Math.max(1,h-c+s*2),u&&(a=Math.floor(a)),y+=(v-y-a*(h-c))*f,o=a*(1-c),u&&(y=Math.round(y),o=Math.round(o));var d=LA(h).map(function(b){return y+a*b});return r(p?d.reverse():d)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return _n(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Xe.apply(l(),arguments)}function h0(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return h0(t())},e}function hn(){return h0(_n.apply(null,arguments).paddingInner(1))}function Nf(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function p0(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function ei(){}var Sn=.7,Di=1/Sn,yr="\\s*([+-]?\\d+)\\s*",An="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",it="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",BA=/^#([0-9a-f]{3,8})$/,FA=new RegExp(`^rgb\\(${yr},${yr},${yr}\\)$`),zA=new RegExp(`^rgb\\(${it},${it},${it}\\)$`),WA=new RegExp(`^rgba\\(${yr},${yr},${yr},${An}\\)$`),UA=new RegExp(`^rgba\\(${it},${it},${it},${An}\\)$`),HA=new RegExp(`^hsl\\(${An},${it},${it}\\)$`),GA=new RegExp(`^hsla\\(${An},${it},${it},${An}\\)$`),py={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Nf(ei,Pn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:dy,formatHex:dy,formatHex8:KA,formatHsl:VA,formatRgb:vy,toString:vy});function dy(){return this.rgb().formatHex()}function KA(){return this.rgb().formatHex8()}function VA(){return d0(this).formatHsl()}function vy(){return this.rgb().formatRgb()}function Pn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=BA.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?yy(t):r===3?new ke(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?li(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?li(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=FA.exec(e))?new ke(t[1],t[2],t[3],1):(t=zA.exec(e))?new ke(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=WA.exec(e))?li(t[1],t[2],t[3],t[4]):(t=UA.exec(e))?li(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=HA.exec(e))?by(t[1],t[2]/100,t[3]/100,1):(t=GA.exec(e))?by(t[1],t[2]/100,t[3]/100,t[4]):py.hasOwnProperty(e)?yy(py[e]):e==="transparent"?new ke(NaN,NaN,NaN,0):null}function yy(e){return new ke(e>>16&255,e>>8&255,e&255,1)}function li(e,t,r,n){return n<=0&&(e=t=r=NaN),new ke(e,t,r,n)}function XA(e){return e instanceof ei||(e=Pn(e)),e?(e=e.rgb(),new ke(e.r,e.g,e.b,e.opacity)):new ke}function gl(e,t,r,n){return arguments.length===1?XA(e):new ke(e,t,r,n==null?1:n)}function ke(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Nf(ke,gl,p0(ei,{brighter(e){return e=e==null?Di:Math.pow(Di,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Sn:Math.pow(Sn,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ke(Kt(this.r),Kt(this.g),Kt(this.b),ki(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:my,formatHex:my,formatHex8:YA,formatRgb:gy,toString:gy}));function my(){return`#${Wt(this.r)}${Wt(this.g)}${Wt(this.b)}`}function YA(){return`#${Wt(this.r)}${Wt(this.g)}${Wt(this.b)}${Wt((isNaN(this.opacity)?1:this.opacity)*255)}`}function gy(){const e=ki(this.opacity);return`${e===1?"rgb(":"rgba("}${Kt(this.r)}, ${Kt(this.g)}, ${Kt(this.b)}${e===1?")":`, ${e})`}`}function ki(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Kt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Wt(e){return e=Kt(e),(e<16?"0":"")+e.toString(16)}function by(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Qe(e,t,r,n)}function d0(e){if(e instanceof Qe)return new Qe(e.h,e.s,e.l,e.opacity);if(e instanceof ei||(e=Pn(e)),!e)return new Qe;if(e instanceof Qe)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new Qe(o,u,c,e.opacity)}function ZA(e,t,r,n){return arguments.length===1?d0(e):new Qe(e,t,r,n==null?1:n)}function Qe(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Nf(Qe,ZA,p0(ei,{brighter(e){return e=e==null?Di:Math.pow(Di,e),new Qe(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Sn:Math.pow(Sn,e),new Qe(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new ke(as(e>=240?e-240:e+120,i,n),as(e,i,n),as(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Qe(xy(this.h),fi(this.s),fi(this.l),ki(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ki(this.opacity);return`${e===1?"hsl(":"hsla("}${xy(this.h)}, ${fi(this.s)*100}%, ${fi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function xy(e){return e=(e||0)%360,e<0?e+360:e}function fi(e){return Math.max(0,Math.min(1,e||0))}function as(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const qf=e=>()=>e;function JA(e,t){return function(r){return e+r*t}}function QA(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function eP(e){return(e=+e)==1?v0:function(t,r){return r-t?QA(t,r,e):qf(isNaN(t)?r:t)}}function v0(e,t){var r=t-e;return r?JA(e,r):qf(isNaN(e)?t:e)}const wy=function e(t){var r=eP(t);function n(i,a){var o=r((i=gl(i)).r,(a=gl(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=v0(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function tP(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function rP(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function nP(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Vr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function iP(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Ri(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function aP(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Vr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var bl=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,os=new RegExp(bl.source,"g");function oP(e){return function(){return e}}function uP(e){return function(t){return e(t)+""}}function cP(e,t){var r=bl.lastIndex=os.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=bl.exec(e))&&(i=os.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Ri(n,i)})),r=os.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?uP(c[0].x):oP(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function Vr(e,t){var r=typeof t,n;return t==null||r==="boolean"?qf(t):(r==="number"?Ri:r==="string"?(n=Pn(t))?(t=n,wy):cP:t instanceof Pn?wy:t instanceof Date?iP:rP(t)?tP:Array.isArray(t)?nP:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?aP:Ri)(e,t)}function Lf(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function sP(e,t){t===void 0&&(t=e,e=Vr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function lP(e){return function(){return e}}function Ni(e){return+e}var Oy=[0,1];function $e(e){return e}function xl(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:lP(isNaN(t)?NaN:.5)}function fP(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function hP(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=xl(i,n),a=r(o,a)):(n=xl(n,i),a=r(a,o)),function(u){return a(n(u))}}function pP(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=xl(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=Qn(e,u,1,n)-1;return a[c](i[c](u))}}function ti(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function ja(){var e=Oy,t=Oy,r=Vr,n,i,a,o=$e,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==$e&&(o=fP(e[0],e[h-1])),u=h>2?pP:hP,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?a:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((s||(s=u(t,e.map(n),Ri)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,Ni),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=Lf,f()},l.clamp=function(h){return arguments.length?(o=h?!0:$e,f()):o!==$e},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,p){return n=h,i=p,f()}}function Bf(){return ja()($e,$e)}function dP(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function qi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Sr(e){return e=qi(Math.abs(e)),e?e[1]:NaN}function vP(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function yP(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var mP=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function En(e){if(!(t=mP.exec(e)))throw new Error("invalid format: "+e);var t;return new Ff({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}En.prototype=Ff.prototype;function Ff(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Ff.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function gP(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var y0;function bP(e,t){var r=qi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(y0=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+qi(e,Math.max(0,t+a-1))[0]}function _y(e,t){var r=qi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Sy={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:dP,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>_y(e*100,t),r:_y,s:bP,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Ay(e){return e}var Py=Array.prototype.map,Ey=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function xP(e){var t=e.grouping===void 0||e.thousands===void 0?Ay:vP(Py.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Ay:yP(Py.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=En(l);var h=l.fill,p=l.align,y=l.sign,v=l.symbol,d=l.zero,b=l.width,x=l.comma,w=l.precision,O=l.trim,m=l.type;m==="n"?(x=!0,m="g"):Sy[m]||(w===void 0&&(w=12),O=!0,m="g"),(d||h==="0"&&p==="=")&&(d=!0,h="0",p="=");var g=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",_=v==="$"?n:/[%p]/.test(m)?o:"",S=Sy[m],E=/[defgprs%]/.test(m);w=w===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function M(P){var T=g,j=_,C,$,D;if(m==="c")j=S(P)+j,P="";else{P=+P;var k=P<0||1/P<0;if(P=isNaN(P)?c:S(Math.abs(P),w),O&&(P=gP(P)),k&&+P==0&&y!=="+"&&(k=!1),T=(k?y==="("?y:u:y==="-"||y==="("?"":y)+T,j=(m==="s"?Ey[8+y0/3]:"")+j+(k&&y==="("?")":""),E){for(C=-1,$=P.length;++C<$;)if(D=P.charCodeAt(C),48>D||D>57){j=(D===46?i+P.slice(C+1):P.slice(C))+j,P=P.slice(0,C);break}}}x&&!d&&(P=t(P,1/0));var L=T.length+P.length+j.length,B=L<b?new Array(b-L+1).join(h):"";switch(x&&d&&(P=t(B+P,B.length?b-j.length:1/0),B=""),p){case"<":P=T+P+j+B;break;case"=":P=T+B+P+j;break;case"^":P=B.slice(0,L=B.length>>1)+T+P+j+B.slice(L);break;default:P=B+T+P+j;break}return a(P)}return M.toString=function(){return l+""},M}function f(l,h){var p=s((l=En(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(Sr(h)/3)))*3,v=Math.pow(10,-y),d=Ey[8+y/3];return function(b){return p(v*b)+d}}return{format:s,formatPrefix:f}}var hi,zf,m0;wP({thousands:",",grouping:[3],currency:["$",""]});function wP(e){return hi=xP(e),zf=hi.format,m0=hi.formatPrefix,hi}function OP(e){return Math.max(0,-Sr(Math.abs(e)))}function _P(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Sr(t)/3)))*3-Sr(Math.abs(e)))}function SP(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Sr(t)-Sr(e))+1}function g0(e,t,r,n){var i=yl(e,t,r),a;switch(n=En(n==null?",f":n),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=_P(i,o))&&(n.precision=a),m0(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=SP(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=OP(i))&&(n.precision=a-(n.type==="%")*2);break}}return zf(n)}function It(e){var t=e.domain;return e.ticks=function(r){var n=t();return dl(n[0],n[n.length-1],r==null?10:r)},e.tickFormat=function(r,n){var i=t();return g0(i[0],i[i.length-1],r==null?10:r,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=vl(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Li(){var e=Bf();return e.copy=function(){return ti(e,Li())},Xe.apply(e,arguments),It(e)}function b0(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Ni),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return b0(e).unknown(t)},e=arguments.length?Array.from(e,Ni):[0,1],It(r)}function x0(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Ty(e){return Math.log(e)}function jy(e){return Math.exp(e)}function AP(e){return-Math.log(-e)}function PP(e){return-Math.exp(-e)}function EP(e){return isFinite(e)?+("1e"+e):e<0?0:e}function TP(e){return e===10?EP:e===Math.E?Math.exp:t=>Math.pow(e,t)}function jP(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function My(e){return(t,r)=>-e(-t,r)}function Wf(e){const t=e(Ty,jy),r=t.domain;let n=10,i,a;function o(){return i=jP(n),a=TP(n),r()[0]<0?(i=My(i),a=My(a),e(AP,PP)):e(Ty,jy),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=i(s),p=i(f),y,v;const d=u==null?10:+u;let b=[];if(!(n%1)&&p-h<d){if(h=Math.floor(h),p=Math.ceil(p),s>0){for(;h<=p;++h)for(y=1;y<n;++y)if(v=h<0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;b.push(v)}}else for(;h<=p;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/a(-h):y*a(h),!(v<s)){if(v>f)break;b.push(v)}b.length*2<d&&(b=dl(s,f,d))}else b=dl(h,p,Math.min(p-h,d)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=En(c)).precision==null&&(c.trim=!0),c=zf(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(x0(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function w0(){const e=Wf(ja()).domain([1,10]);return e.copy=()=>ti(e,w0()).base(e.base()),Xe.apply(e,arguments),e}function $y(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Cy(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Uf(e){var t=1,r=e($y(t),Cy(t));return r.constant=function(n){return arguments.length?e($y(t=+n),Cy(t)):t},It(r)}function O0(){var e=Uf(ja());return e.copy=function(){return ti(e,O0()).constant(e.constant())},Xe.apply(e,arguments)}function Iy(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function MP(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function $P(e){return e<0?-e*e:e*e}function Hf(e){var t=e($e,$e),r=1;function n(){return r===1?e($e,$e):r===.5?e(MP,$P):e(Iy(r),Iy(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},It(t)}function Gf(){var e=Hf(ja());return e.copy=function(){return ti(e,Gf()).exponent(e.exponent())},Xe.apply(e,arguments),e}function CP(){return Gf.apply(null,arguments).exponent(.5)}function Dy(e){return Math.sign(e)*e*e}function IP(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function _0(){var e=Bf(),t=[0,1],r=!1,n;function i(a){var o=IP(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(Dy(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Ni)).map(Dy)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return _0(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Xe.apply(i,arguments),It(i)}function S0(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=qA(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Qn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Mt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return S0().domain(e).range(t).unknown(n)},Xe.apply(a,arguments)}function A0(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[Qn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return A0().domain([e,t]).range(i).unknown(a)},Xe.apply(It(o),arguments)}function P0(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Qn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return P0().domain(e).range(t).unknown(r)},Xe.apply(i,arguments)}const us=new Date,cs=new Date;function we(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>we(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(us.setTime(+a),cs.setTime(+o),e(us),e(cs),Math.floor(r(us,cs))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Bi=we(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Bi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?we(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Bi);Bi.range;const ft=1e3,He=ft*60,ht=He*60,gt=ht*24,Kf=gt*7,ky=gt*30,ss=gt*365,Ut=we(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getUTCSeconds());Ut.range;const Vf=we(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ft)},(e,t)=>{e.setTime(+e+t*He)},(e,t)=>(t-e)/He,e=>e.getMinutes());Vf.range;const Xf=we(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*He)},(e,t)=>(t-e)/He,e=>e.getUTCMinutes());Xf.range;const Yf=we(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ft-e.getMinutes()*He)},(e,t)=>{e.setTime(+e+t*ht)},(e,t)=>(t-e)/ht,e=>e.getHours());Yf.range;const Zf=we(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ht)},(e,t)=>(t-e)/ht,e=>e.getUTCHours());Zf.range;const ri=we(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*He)/gt,e=>e.getDate()-1);ri.range;const Ma=we(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/gt,e=>e.getUTCDate()-1);Ma.range;const E0=we(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/gt,e=>Math.floor(e/gt));E0.range;function rr(e){return we(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*He)/Kf)}const $a=rr(0),Fi=rr(1),DP=rr(2),kP=rr(3),Ar=rr(4),RP=rr(5),NP=rr(6);$a.range;Fi.range;DP.range;kP.range;Ar.range;RP.range;NP.range;function nr(e){return we(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Kf)}const Ca=nr(0),zi=nr(1),qP=nr(2),LP=nr(3),Pr=nr(4),BP=nr(5),FP=nr(6);Ca.range;zi.range;qP.range;LP.range;Pr.range;BP.range;FP.range;const Jf=we(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Jf.range;const Qf=we(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Qf.range;const bt=we(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());bt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:we(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});bt.range;const xt=we(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());xt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:we(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});xt.range;function T0(e,t,r,n,i,a){const o=[[Ut,1,ft],[Ut,5,5*ft],[Ut,15,15*ft],[Ut,30,30*ft],[a,1,He],[a,5,5*He],[a,15,15*He],[a,30,30*He],[i,1,ht],[i,3,3*ht],[i,6,6*ht],[i,12,12*ht],[n,1,gt],[n,2,2*gt],[r,1,Kf],[t,1,ky],[t,3,3*ky],[e,1,ss]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const p=l&&typeof l.range=="function"?l:c(s,f,l),y=p?p.range(s,+f+1):[];return h?y.reverse():y}function c(s,f,l){const h=Math.abs(f-s)/l,p=kf(([,,d])=>d).right(o,h);if(p===o.length)return e.every(yl(s/ss,f/ss,l));if(p===0)return Bi.every(Math.max(yl(s,f,l),1));const[y,v]=o[h/o[p-1][2]<o[p][2]/h?p-1:p];return y.every(v)}return[u,c]}const[zP,WP]=T0(xt,Qf,Ca,E0,Zf,Xf),[UP,HP]=T0(bt,Jf,$a,ri,Yf,Vf);function ls(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function fs(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function en(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function GP(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=tn(i),f=rn(i),l=tn(a),h=rn(a),p=tn(o),y=rn(o),v=tn(u),d=rn(u),b=tn(c),x=rn(c),w={a:k,A:L,b:B,B:U,c:null,d:Fy,e:Fy,f:vE,g:AE,G:EE,H:hE,I:pE,j:dE,L:j0,m:yE,M:mE,p:G,q:z,Q:Uy,s:Hy,S:gE,u:bE,U:xE,V:wE,w:OE,W:_E,x:null,X:null,y:SE,Y:PE,Z:TE,"%":Wy},O={a:K,A:ce,b:de,B:Ne,c:null,d:zy,e:zy,f:CE,g:zE,G:UE,H:jE,I:ME,j:$E,L:$0,m:IE,M:DE,p:Rt,q:Ie,Q:Uy,s:Hy,S:kE,u:RE,U:NE,V:qE,w:LE,W:BE,x:null,X:null,y:FE,Y:WE,Z:HE,"%":Wy},m={a:M,A:P,b:T,B:j,c:C,d:Ly,e:Ly,f:cE,g:qy,G:Ny,H:By,I:By,j:iE,L:uE,m:nE,M:aE,p:E,q:rE,Q:lE,s:fE,S:oE,u:ZP,U:JP,V:QP,w:YP,W:eE,x:$,X:D,y:qy,Y:Ny,Z:tE,"%":sE};w.x=g(r,w),w.X=g(n,w),w.c=g(t,w),O.x=g(r,O),O.X=g(n,O),O.c=g(t,O);function g(F,X){return function(Z){var R=[],he=-1,ee=0,me=F.length,ge,De,St;for(Z instanceof Date||(Z=new Date(+Z));++he<me;)F.charCodeAt(he)===37&&(R.push(F.slice(ee,he)),(De=Ry[ge=F.charAt(++he)])!=null?ge=F.charAt(++he):De=ge==="e"?" ":"0",(St=X[ge])&&(ge=St(Z,De)),R.push(ge),ee=he+1);return R.push(F.slice(ee,he)),R.join("")}}function _(F,X){return function(Z){var R=en(1900,void 0,1),he=S(R,F,Z+="",0),ee,me;if(he!=Z.length)return null;if("Q"in R)return new Date(R.Q);if("s"in R)return new Date(R.s*1e3+("L"in R?R.L:0));if(X&&!("Z"in R)&&(R.Z=0),"p"in R&&(R.H=R.H%12+R.p*12),R.m===void 0&&(R.m="q"in R?R.q:0),"V"in R){if(R.V<1||R.V>53)return null;"w"in R||(R.w=1),"Z"in R?(ee=fs(en(R.y,0,1)),me=ee.getUTCDay(),ee=me>4||me===0?zi.ceil(ee):zi(ee),ee=Ma.offset(ee,(R.V-1)*7),R.y=ee.getUTCFullYear(),R.m=ee.getUTCMonth(),R.d=ee.getUTCDate()+(R.w+6)%7):(ee=ls(en(R.y,0,1)),me=ee.getDay(),ee=me>4||me===0?Fi.ceil(ee):Fi(ee),ee=ri.offset(ee,(R.V-1)*7),R.y=ee.getFullYear(),R.m=ee.getMonth(),R.d=ee.getDate()+(R.w+6)%7)}else("W"in R||"U"in R)&&("w"in R||(R.w="u"in R?R.u%7:"W"in R?1:0),me="Z"in R?fs(en(R.y,0,1)).getUTCDay():ls(en(R.y,0,1)).getDay(),R.m=0,R.d="W"in R?(R.w+6)%7+R.W*7-(me+5)%7:R.w+R.U*7-(me+6)%7);return"Z"in R?(R.H+=R.Z/100|0,R.M+=R.Z%100,fs(R)):ls(R)}}function S(F,X,Z,R){for(var he=0,ee=X.length,me=Z.length,ge,De;he<ee;){if(R>=me)return-1;if(ge=X.charCodeAt(he++),ge===37){if(ge=X.charAt(he++),De=m[ge in Ry?X.charAt(he++):ge],!De||(R=De(F,Z,R))<0)return-1}else if(ge!=Z.charCodeAt(R++))return-1}return R}function E(F,X,Z){var R=s.exec(X.slice(Z));return R?(F.p=f.get(R[0].toLowerCase()),Z+R[0].length):-1}function M(F,X,Z){var R=p.exec(X.slice(Z));return R?(F.w=y.get(R[0].toLowerCase()),Z+R[0].length):-1}function P(F,X,Z){var R=l.exec(X.slice(Z));return R?(F.w=h.get(R[0].toLowerCase()),Z+R[0].length):-1}function T(F,X,Z){var R=b.exec(X.slice(Z));return R?(F.m=x.get(R[0].toLowerCase()),Z+R[0].length):-1}function j(F,X,Z){var R=v.exec(X.slice(Z));return R?(F.m=d.get(R[0].toLowerCase()),Z+R[0].length):-1}function C(F,X,Z){return S(F,t,X,Z)}function $(F,X,Z){return S(F,r,X,Z)}function D(F,X,Z){return S(F,n,X,Z)}function k(F){return o[F.getDay()]}function L(F){return a[F.getDay()]}function B(F){return c[F.getMonth()]}function U(F){return u[F.getMonth()]}function G(F){return i[+(F.getHours()>=12)]}function z(F){return 1+~~(F.getMonth()/3)}function K(F){return o[F.getUTCDay()]}function ce(F){return a[F.getUTCDay()]}function de(F){return c[F.getUTCMonth()]}function Ne(F){return u[F.getUTCMonth()]}function Rt(F){return i[+(F.getUTCHours()>=12)]}function Ie(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var X=g(F+="",w);return X.toString=function(){return F},X},parse:function(F){var X=_(F+="",!1);return X.toString=function(){return F},X},utcFormat:function(F){var X=g(F+="",O);return X.toString=function(){return F},X},utcParse:function(F){var X=_(F+="",!0);return X.toString=function(){return F},X}}}var Ry={"-":"",_:" ",0:"0"},_e=/^\s*\d+/,KP=/^%/,VP=/[\\^$*+?|[\]().{}]/g;function te(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function XP(e){return e.replace(VP,"\\$&")}function tn(e){return new RegExp("^(?:"+e.map(XP).join("|")+")","i")}function rn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function YP(e,t,r){var n=_e.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function ZP(e,t,r){var n=_e.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function JP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function QP(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function eE(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Ny(e,t,r){var n=_e.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function qy(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function tE(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function rE(e,t,r){var n=_e.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function nE(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Ly(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function iE(e,t,r){var n=_e.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function By(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function aE(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function oE(e,t,r){var n=_e.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function uE(e,t,r){var n=_e.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function cE(e,t,r){var n=_e.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function sE(e,t,r){var n=KP.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function lE(e,t,r){var n=_e.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function fE(e,t,r){var n=_e.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Fy(e,t){return te(e.getDate(),t,2)}function hE(e,t){return te(e.getHours(),t,2)}function pE(e,t){return te(e.getHours()%12||12,t,2)}function dE(e,t){return te(1+ri.count(bt(e),e),t,3)}function j0(e,t){return te(e.getMilliseconds(),t,3)}function vE(e,t){return j0(e,t)+"000"}function yE(e,t){return te(e.getMonth()+1,t,2)}function mE(e,t){return te(e.getMinutes(),t,2)}function gE(e,t){return te(e.getSeconds(),t,2)}function bE(e){var t=e.getDay();return t===0?7:t}function xE(e,t){return te($a.count(bt(e)-1,e),t,2)}function M0(e){var t=e.getDay();return t>=4||t===0?Ar(e):Ar.ceil(e)}function wE(e,t){return e=M0(e),te(Ar.count(bt(e),e)+(bt(e).getDay()===4),t,2)}function OE(e){return e.getDay()}function _E(e,t){return te(Fi.count(bt(e)-1,e),t,2)}function SE(e,t){return te(e.getFullYear()%100,t,2)}function AE(e,t){return e=M0(e),te(e.getFullYear()%100,t,2)}function PE(e,t){return te(e.getFullYear()%1e4,t,4)}function EE(e,t){var r=e.getDay();return e=r>=4||r===0?Ar(e):Ar.ceil(e),te(e.getFullYear()%1e4,t,4)}function TE(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+te(t/60|0,"0",2)+te(t%60,"0",2)}function zy(e,t){return te(e.getUTCDate(),t,2)}function jE(e,t){return te(e.getUTCHours(),t,2)}function ME(e,t){return te(e.getUTCHours()%12||12,t,2)}function $E(e,t){return te(1+Ma.count(xt(e),e),t,3)}function $0(e,t){return te(e.getUTCMilliseconds(),t,3)}function CE(e,t){return $0(e,t)+"000"}function IE(e,t){return te(e.getUTCMonth()+1,t,2)}function DE(e,t){return te(e.getUTCMinutes(),t,2)}function kE(e,t){return te(e.getUTCSeconds(),t,2)}function RE(e){var t=e.getUTCDay();return t===0?7:t}function NE(e,t){return te(Ca.count(xt(e)-1,e),t,2)}function C0(e){var t=e.getUTCDay();return t>=4||t===0?Pr(e):Pr.ceil(e)}function qE(e,t){return e=C0(e),te(Pr.count(xt(e),e)+(xt(e).getUTCDay()===4),t,2)}function LE(e){return e.getUTCDay()}function BE(e,t){return te(zi.count(xt(e)-1,e),t,2)}function FE(e,t){return te(e.getUTCFullYear()%100,t,2)}function zE(e,t){return e=C0(e),te(e.getUTCFullYear()%100,t,2)}function WE(e,t){return te(e.getUTCFullYear()%1e4,t,4)}function UE(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Pr(e):Pr.ceil(e),te(e.getUTCFullYear()%1e4,t,4)}function HE(){return"+0000"}function Wy(){return"%"}function Uy(e){return+e}function Hy(e){return Math.floor(+e/1e3)}var cr,I0,D0;GE({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function GE(e){return cr=GP(e),I0=cr.format,cr.parse,D0=cr.utcFormat,cr.utcParse,cr}function KE(e){return new Date(e)}function VE(e){return e instanceof Date?+e:+new Date(+e)}function eh(e,t,r,n,i,a,o,u,c,s){var f=Bf(),l=f.invert,h=f.domain,p=s(".%L"),y=s(":%S"),v=s("%I:%M"),d=s("%I %p"),b=s("%a %d"),x=s("%b %d"),w=s("%B"),O=s("%Y");function m(g){return(c(g)<g?p:u(g)<g?y:o(g)<g?v:a(g)<g?d:n(g)<g?i(g)<g?b:x:r(g)<g?w:O)(g)}return f.invert=function(g){return new Date(l(g))},f.domain=function(g){return arguments.length?h(Array.from(g,VE)):h().map(KE)},f.ticks=function(g){var _=h();return e(_[0],_[_.length-1],g==null?10:g)},f.tickFormat=function(g,_){return _==null?m:s(_)},f.nice=function(g){var _=h();return(!g||typeof g.range!="function")&&(g=t(_[0],_[_.length-1],g==null?10:g)),g?h(x0(_,g)):f},f.copy=function(){return ti(f,eh(e,t,r,n,i,a,o,u,c,s))},f}function XE(){return Xe.apply(eh(UP,HP,bt,Jf,$a,ri,Yf,Vf,Ut,I0).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function YE(){return Xe.apply(eh(zP,WP,xt,Qf,Ca,Ma,Zf,Xf,Ut,D0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ia(){var e=0,t=1,r,n,i,a,o=$e,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var p,y;return arguments.length?([p,y]=h,o=l(p,y),s):[o(0),o(1)]}}return s.range=f(Vr),s.rangeRound=f(Lf),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function Dt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function k0(){var e=It(Ia()($e));return e.copy=function(){return Dt(e,k0())},_t.apply(e,arguments)}function R0(){var e=Wf(Ia()).domain([1,10]);return e.copy=function(){return Dt(e,R0()).base(e.base())},_t.apply(e,arguments)}function N0(){var e=Uf(Ia());return e.copy=function(){return Dt(e,N0()).constant(e.constant())},_t.apply(e,arguments)}function th(){var e=Hf(Ia());return e.copy=function(){return Dt(e,th()).exponent(e.exponent())},_t.apply(e,arguments)}function ZE(){return th.apply(null,arguments).exponent(.5)}function q0(){var e=[],t=$e;function r(n){if(n!=null&&!isNaN(n=+n))return t((Qn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Mt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>NA(e,a/n))},r.copy=function(){return q0(t).domain(e)},_t.apply(r,arguments)}function Da(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=$e,f,l=!1,h;function p(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}p.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,p):[e,t,r]},p.clamp=function(v){return arguments.length?(l=!!v,p):l},p.interpolator=function(v){return arguments.length?(s=v,p):s};function y(v){return function(d){var b,x,w;return arguments.length?([b,x,w]=d,s=sP(v,[b,x,w]),p):[s(0),s(.5),s(1)]}}return p.range=y(Vr),p.rangeRound=y(Lf),p.unknown=function(v){return arguments.length?(h=v,p):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,p}}function L0(){var e=It(Da()($e));return e.copy=function(){return Dt(e,L0())},_t.apply(e,arguments)}function B0(){var e=Wf(Da()).domain([.1,1,10]);return e.copy=function(){return Dt(e,B0()).base(e.base())},_t.apply(e,arguments)}function F0(){var e=Uf(Da());return e.copy=function(){return Dt(e,F0()).constant(e.constant())},_t.apply(e,arguments)}function rh(){var e=Hf(Da());return e.copy=function(){return Dt(e,rh()).exponent(e.exponent())},_t.apply(e,arguments)}function JE(){return rh.apply(null,arguments).exponent(.5)}const Gy=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:_n,scaleDiverging:L0,scaleDivergingLog:B0,scaleDivergingPow:rh,scaleDivergingSqrt:JE,scaleDivergingSymlog:F0,scaleIdentity:b0,scaleImplicit:ml,scaleLinear:Li,scaleLog:w0,scaleOrdinal:Rf,scalePoint:hn,scalePow:Gf,scaleQuantile:S0,scaleQuantize:A0,scaleRadial:_0,scaleSequential:k0,scaleSequentialLog:R0,scaleSequentialPow:th,scaleSequentialQuantile:q0,scaleSequentialSqrt:ZE,scaleSequentialSymlog:N0,scaleSqrt:CP,scaleSymlog:O0,scaleThreshold:P0,scaleTime:XE,scaleUtc:YE,tickFormat:g0},Symbol.toStringTag,{value:"Module"}));var hs,Ky;function z0(){if(Ky)return hs;Ky=1;var e=Wr();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],c=n(u);if(c!=null&&(s===void 0?c===c&&!e(c):i(c,s)))var s=c,f=u}return f}return hs=t,hs}var ps,Vy;function QE(){if(Vy)return ps;Vy=1;function e(t,r){return t>r}return ps=e,ps}var ds,Xy;function eT(){if(Xy)return ds;Xy=1;var e=z0(),t=QE(),r=Kr();function n(i){return i&&i.length?e(i,r,t):void 0}return ds=n,ds}var tT=eT();const Tt=le(tT);var vs,Yy;function rT(){if(Yy)return vs;Yy=1;function e(t,r){return t<r}return vs=e,vs}var ys,Zy;function nT(){if(Zy)return ys;Zy=1;var e=z0(),t=rT(),r=Kr();function n(i){return i&&i.length?e(i,r,t):void 0}return ys=n,ys}var iT=nT();const ka=le(iT);var ms,Jy;function aT(){if(Jy)return ms;Jy=1;var e=gf(),t=Ct(),r=Yb(),n=Re();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return ms=i,ms}var gs,Qy;function oT(){if(Qy)return gs;Qy=1;var e=Vb(),t=aT();function r(n,i){return e(t(n,i),1)}return gs=r,gs}var uT=oT();const cT=le(uT);var bs,em;function sT(){if(em)return bs;em=1;var e=$f();function t(r,n){return e(r,n)}return bs=t,bs}var lT=sT();const Er=le(lT);var Xr=1e9,fT={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},ih,fe=!0,Ve="[DecimalError] ",Vt=Ve+"Invalid argument: ",nh=Ve+"Exponent out of range: ",Yr=Math.floor,Ft=Math.pow,hT=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Fe,Oe=1e7,se=7,W0=9007199254740991,Wi=Yr(W0/se),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*se;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return yt(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return ae(yt(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return ye(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Fe))throw Error(Ve+"NaN");if(r.s<1)throw Error(Ve+(r.s?"NaN":"-Infinity"));return r.eq(Fe)?new n(0):(fe=!1,t=yt(Tn(r,a),Tn(e,a),a),fe=!0,ae(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?G0(t,e):U0(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ve+"NaN");return r.s?(fe=!1,t=yt(r,e,0,1).times(e),fe=!0,r.minus(t)):ae(new n(r),i)};W.naturalExponential=W.exp=function(){return H0(this)};W.naturalLogarithm=W.ln=function(){return Tn(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?U0(t,e):G0(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Vt+e);if(t=ye(i)+1,n=i.d.length-1,r=n*se+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ve+"NaN")}for(e=ye(u),fe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=rt(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Yr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(yt(u,a,o+2)).times(.5),rt(a.d).slice(0,o)===(t=rt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ae(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return fe=!0,ae(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,h=f.d,p=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=p.length,c<s&&(a=h,h=p,p=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+p[n]*h[i-n-1]+t,a[i--]=u%Oe|0,t=u/Oe|0;a[i]=(a[i]+t)%Oe|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,fe?ae(e,l.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ot(e,0,Xr),t===void 0?t=n.rounding:ot(t,0,8),ae(r,e+ye(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Zt(n,!0):(ot(e,0,Xr),t===void 0?t=i.rounding:ot(t,0,8),n=ae(new i(n),e+1,t),r=Zt(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Zt(i):(ot(e,0,Xr),t===void 0?t=a.rounding:ot(t,0,8),n=ae(new a(i),e+ye(i)+1,t),r=Zt(n.abs(),!1,e+ye(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return ae(new t(e),ye(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Fe);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ve+"Infinity");return u}if(u.eq(Fe))return u;if(n=c.precision,e.eq(Fe))return ae(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=W0){for(i=new c(Fe),t=Math.ceil(n/se+4),fe=!1;r%2&&(i=i.times(u),rm(i.d,t)),r=Yr(r/2),r!==0;)u=u.times(u),rm(u.d,t);return fe=!0,e.s<0?new c(Fe).div(i):ae(i,n)}}else if(a<0)throw Error(Ve+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,fe=!1,i=e.times(Tn(u,n+s)),fe=!0,i=H0(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ye(i),n=Zt(i,r<=a.toExpNeg||r>=a.toExpPos)):(ot(e,1,Xr),t===void 0?t=a.rounding:ot(t,0,8),i=ae(new a(i),e,t),r=ye(i),n=Zt(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ot(e,1,Xr),t===void 0?t=n.rounding:ot(t,0,8)),ae(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ye(e),r=e.constructor;return Zt(e,t<=r.toExpNeg||t>=r.toExpPos)};function U0(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),fe?ae(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/se),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Oe|0,c[a]%=Oe;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,fe?ae(t,l):t}function ot(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Vt+e)}function rt(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=se-n.length,r&&(a+=Pt(r)),a+=n;o=e[t],n=o+"",r=se-n.length,r&&(a+=Pt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var yt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Oe|0,o=a/Oe|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Oe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,h,p,y,v,d,b,x,w,O,m,g,_,S,E=n.constructor,M=n.s==i.s?1:-1,P=n.d,T=i.d;if(!n.s)return new E(n);if(!i.s)throw Error(Ve+"Division by zero");for(c=n.e-i.e,_=T.length,m=P.length,p=new E(M),y=p.d=[],s=0;T[s]==(P[s]||0);)++s;if(T[s]>(P[s]||0)&&--c,a==null?x=a=E.precision:o?x=a+(ye(n)-ye(i))+1:x=a,x<0)return new E(0);if(x=x/se+2|0,s=0,_==1)for(f=0,T=T[0],x++;(s<m||f)&&x--;s++)w=f*Oe+(P[s]||0),y[s]=w/T|0,f=w%T|0;else{for(f=Oe/(T[0]+1)|0,f>1&&(T=e(T,f),P=e(P,f),_=T.length,m=P.length),O=_,v=P.slice(0,_),d=v.length;d<_;)v[d++]=0;S=T.slice(),S.unshift(0),g=T[0],T[1]>=Oe/2&&++g;do f=0,u=t(T,v,_,d),u<0?(b=v[0],_!=d&&(b=b*Oe+(v[1]||0)),f=b/g|0,f>1?(f>=Oe&&(f=Oe-1),l=e(T,f),h=l.length,d=v.length,u=t(l,v,h,d),u==1&&(f--,r(l,_<h?S:T,h))):(f==0&&(u=f=1),l=T.slice()),h=l.length,h<d&&l.unshift(0),r(v,l,d),u==-1&&(d=v.length,u=t(T,v,_,d),u<1&&(f++,r(v,_<d?S:T,d))),d=v.length):u===0&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[d++]=P[O]||0:(v=[P[O]],d=1);while((O++<m||v[0]!==void 0)&&x--)}return y[0]||y.shift(),p.e=c,ae(p,o?a+ye(p)+1:a)}}();function H0(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(ye(e)>16)throw Error(nh+ye(e));if(!e.s)return new f(Fe);for(fe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Ft(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Fe),f.precision=u;;){if(i=ae(i.times(e),u),r=r.times(++c),o=a.plus(yt(i,r,u)),rt(o.d).slice(0,u)===rt(a.d).slice(0,u)){for(;s--;)a=ae(a.times(a),u);return f.precision=l,t==null?(fe=!0,ae(a,l)):a}a=o}}function ye(e){for(var t=e.e*se,r=e.d[0];r>=10;r/=10)t++;return t}function xs(e,t,r){if(t>e.LN10.sd())throw fe=!0,r&&(e.precision=r),Error(Ve+"LN10 precision limit exceeded");return ae(new e(e.LN10),t)}function Pt(e){for(var t="";e--;)t+="0";return t}function Tn(e,t){var r,n,i,a,o,u,c,s,f,l=1,h=10,p=e,y=p.d,v=p.constructor,d=v.precision;if(p.s<1)throw Error(Ve+(p.s?"NaN":"-Infinity"));if(p.eq(Fe))return new v(0);if(t==null?(fe=!1,s=d):s=t,p.eq(10))return t==null&&(fe=!0),xs(v,s);if(s+=h,v.precision=s,r=rt(y),n=r.charAt(0),a=ye(p),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)p=p.times(e),r=rt(p.d),n=r.charAt(0),l++;a=ye(p),n>1?(p=new v("0."+r),a++):p=new v(n+"."+r.slice(1))}else return c=xs(v,s+2,d).times(a+""),p=Tn(new v(n+"."+r.slice(1)),s-h).plus(c),v.precision=d,t==null?(fe=!0,ae(p,d)):p;for(u=o=p=yt(p.minus(Fe),p.plus(Fe),s),f=ae(p.times(p),s),i=3;;){if(o=ae(o.times(f),s),c=u.plus(yt(o,new v(i),s)),rt(c.d).slice(0,s)===rt(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(xs(v,s+2,d).times(a+""))),u=yt(u,new v(l),s),v.precision=d,t==null?(fe=!0,ae(u,d)):u;u=c,i+=2}}function tm(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Yr(r/se),e.d=[],n=(r+1)%se,r<0&&(n+=se),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=se;n<i;)e.d.push(+t.slice(n,n+=se));t=t.slice(n),n=se-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),fe&&(e.e>Wi||e.e<-Wi))throw Error(nh+r)}else e.s=0,e.e=0,e.d=[0];return e}function ae(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=se,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/se),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=se,i=n-se+o}if(r!==void 0&&(a=Ft(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Ft(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=ye(e),l.length=1,t=t-a-1,l[0]=Ft(10,(se-t%se)%se),e.e=Yr(-t/se)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Ft(10,se-n),l[f]=i>0?(s/Ft(10,o-i)%Ft(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Oe&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Oe)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(fe&&(e.e>Wi||e.e<-Wi))throw Error(nh+ye(e));return e}function G0(e,t){var r,n,i,a,o,u,c,s,f,l,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),fe?ae(t,p):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(p/se),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Oe-1;--c[a],c[i]+=Oe}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,fe?ae(t,p):t):new h(0)}function Zt(e,t,r){var n,i=ye(e),a=rt(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Pt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Pt(-i-1)+a,r&&(n=r-o)>0&&(a+=Pt(n))):i>=o?(a+=Pt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Pt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Pt(n))),e.s<0?"-"+a:a}function rm(e,t){if(e.length>t)return e.length=t,!0}function K0(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Vt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return tm(o,a.toString())}else if(typeof a!="string")throw Error(Vt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,hT.test(a))tm(o,a);else throw Error(Vt+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=K0,i.config=i.set=pT,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function pT(e){if(!e||typeof e!="object")throw Error(Ve+"Object expected");var t,r,n,i=["precision",1,Xr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Yr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Vt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Vt+r+": "+n);return this}var ih=K0(fT);Fe=new ih(1);const ie=ih;function dT(e){return gT(e)||mT(e)||yT(e)||vT()}function vT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yT(e,t){if(e){if(typeof e=="string")return wl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wl(e,t)}}function mT(e){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(e))return Array.from(e)}function gT(e){if(Array.isArray(e))return wl(e)}function wl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var bT=function(t){return t},V0={},X0=function(t){return t===V0},nm=function(t){return function r(){return arguments.length===0||arguments.length===1&&X0(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},xT=function e(t,r){return t===1?r:nm(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==V0}).length;return o>=t?r.apply(void 0,i):e(t-o,nm(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return X0(l)?c.shift():l});return r.apply(void 0,dT(f).concat(c))}))})},Ra=function(t){return xT(t.length,t)},Ol=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},wT=Ra(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),OT=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return bT;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},_l=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Y0=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function _T(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function ST(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var AT=Ra(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),PT=Ra(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),ET=Ra(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Na={rangeStep:ST,getDigitCount:_T,interpolateNumber:AT,uninterpolateNumber:PT,uninterpolateTruncation:ET};function Sl(e){return MT(e)||jT(e)||Z0(e)||TT()}function TT(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jT(e){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(e))return Array.from(e)}function MT(e){if(Array.isArray(e))return Al(e)}function jn(e,t){return IT(e)||CT(e,t)||Z0(e,t)||$T()}function $T(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Z0(e,t){if(e){if(typeof e=="string")return Al(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Al(e,t)}}function Al(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function CT(e,t){if(!(typeof Symbol=="undefined"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function IT(e){if(Array.isArray(e))return e}function J0(e){var t=jn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Q0(e,t,r){if(e.lte(0))return new ie(0);var n=Na.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function DT(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(Na.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=OT(wT(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),Ol);return u(0,t)}function ex(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=Q0(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?ex(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function kT(e){var t=jn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=J0([r,n]),c=jn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Sl(Ol(0,i-1).map(function(){return 1/0}))):[].concat(Sl(Ol(0,i-1).map(function(){return-1/0})),[f]);return r>n?_l(l):l}if(s===f)return DT(s,i,a);var h=ex(s,f,o,a),p=h.step,y=h.tickMin,v=h.tickMax,d=Na.rangeStep(y,v.add(new ie(.1).mul(p)),p);return r>n?_l(d):d}function RT(e,t){var r=jn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=J0([n,i]),u=jn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=Q0(new ie(s).sub(c).div(f-1),a,0),h=[].concat(Sl(Na.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?_l(h):h}var NT=Y0(kT),qT=Y0(RT),LT="Invariant failed";function Jt(e,t){throw new Error(LT)}var BT=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function Ui(){return Ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ui.apply(this,arguments)}function FT(e,t){return HT(e)||UT(e,t)||WT(e,t)||zT()}function zT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function WT(e,t){if(e){if(typeof e=="string")return im(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return im(e,t)}}function im(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function UT(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function HT(e){if(Array.isArray(e))return e}function GT(e,t){if(e==null)return{};var r=KT(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function KT(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function VT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function XT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nx(n.key),n)}}function YT(e,t,r){return t&&XT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ZT(e,t,r){return t=Hi(t),JT(e,tx()?Reflect.construct(t,r||[],Hi(e).constructor):t.apply(e,r))}function JT(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QT(e)}function QT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function tx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tx=function(){return!!e})()}function Hi(e){return Hi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Hi(e)}function ej(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pl(e,t)}function Pl(e,t){return Pl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pl(e,t)}function rx(e,t,r){return t=nx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nx(e){var t=tj(e,"string");return Tr(t)=="symbol"?t:t+""}function tj(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ni=function(e){function t(){return VT(this,t),ZT(this,t,arguments)}return ej(t,e),YT(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=GT(n,BT),p=Y(h,!1);this.props.direction==="x"&&f.type!=="number"&&Jt();var y=c.map(function(v){var d=s(v,u),b=d.x,x=d.y,w=d.value,O=d.errorVal;if(!O)return null;var m=[],g,_;if(Array.isArray(O)){var S=FT(O,2);g=S[0],_=S[1]}else g=_=O;if(a==="vertical"){var E=f.scale,M=x+i,P=M+o,T=M-o,j=E(w-g),C=E(w+_);m.push({x1:C,y1:P,x2:C,y2:T}),m.push({x1:j,y1:M,x2:C,y2:M}),m.push({x1:j,y1:P,x2:j,y2:T})}else if(a==="horizontal"){var $=l.scale,D=b+i,k=D-o,L=D+o,B=$(w-g),U=$(w+_);m.push({x1:k,y1:U,x2:L,y2:U}),m.push({x1:D,y1:B,x2:D,y2:U}),m.push({x1:k,y1:B,x2:L,y2:B})}return A.createElement(ue,Ui({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},p),m.map(function(G){return A.createElement("line",Ui({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return A.createElement(ue,{className:"recharts-errorBars"},y)}}])}(A.Component);rx(ni,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});rx(ni,"displayName","ErrorBar");function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}function am(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?am(Object(r),!0).forEach(function(n){rj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):am(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rj(e,t,r){return t=nj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nj(e){var t=ij(e,"string");return Mn(t)=="symbol"?t:t+""}function ij(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ix=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=Be(r,vr);if(!o)return null;var u=vr.defaultProps,c=u!==void 0?Lt(Lt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var h=l.item,p=l.props,y=p.sectors||p.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,p=h!==void 0?Lt(Lt({},h),l.props):{},y=p.dataKey,v=p.name,d=p.legendType,b=p.hide;return{inactive:b,dataKey:y,type:c.iconType||d||"square",color:ah(l),value:v||y,payload:p}}),Lt(Lt(Lt({},c),vr.getWithHeight(o,i)),{},{payload:s,item:o})};function $n(e){"@babel/helpers - typeof";return $n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$n(e)}function om(e){return cj(e)||uj(e)||oj(e)||aj()}function aj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function oj(e,t){if(e){if(typeof e=="string")return El(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return El(e,t)}}function uj(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function cj(e){if(Array.isArray(e))return El(e)}function El(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function um(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?um(Object(r),!0).forEach(function(n){mr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):um(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mr(e,t,r){return t=sj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sj(e){var t=lj(e,"string");return $n(t)=="symbol"?t:t+""}function lj(e,t){if($n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ce(e,t,r){return J(e)||J(t)?r:xe(t)?Ge(e,t,r):V(t)?t(e):r}function pn(e,t,r,n){var i=cT(e,function(u){return Ce(u,t)});if(r==="number"){var a=i.filter(function(u){return N(u)||parseFloat(u)});return a.length?[ka(a),Tt(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!J(u)}):i;return o.map(function(u){return xe(u)||u instanceof Date?u:""})}var fj=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,h=s>=u-1?i[0].coordinate:i[s+1].coordinate,p=void 0;if(et(l-f)!==et(h-l)){var y=[];if(et(h-l)===et(c[1]-c[0])){p=h;var v=l+c[1]-c[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{p=f;var d=h+c[1]-c[0];y[0]=Math.min(l,(d+l)/2),y[1]=Math.max(l,(d+l)/2)}var b=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>b[0]&&t<=b[1]||t>=y[0]&&t<=y[1]){o=i[s].index;break}}else{var x=Math.min(f,h),w=Math.max(f,h);if(t>(x+l)/2&&t<=(w+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},ah=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?pe(pe({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},hj=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),h=0,p=l.length;h<p;h++){var y=f[l[h]],v=y.items,d=y.cateAxisId,b=v.filter(function(_){return dt(_.type).indexOf("Bar")>=0});if(b&&b.length){var x=b[0].type.defaultProps,w=x!==void 0?pe(pe({},x),b[0].props):b[0].props,O=w.barSize,m=w[d];o[m]||(o[m]=[]);var g=J(O)?r:O;o[m].push({item:b[0],stackList:b.slice(1),barSize:J(g)?void 0:Yt(g,n,0)})}}return o},pj=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Yt(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,p=i/c,y=o.reduce(function(O,m){return O+m.barSize||0},0);y+=(c-1)*s,y>=i&&(y-=(c-1)*s,s=0),y>=i&&p>0&&(h=!0,p*=.9,y=c*p);var v=(i-y)/2>>0,d={offset:v-s,size:0};f=o.reduce(function(O,m){var g={item:m.item,position:{offset:d.offset+d.size+s,size:h?p:m.barSize}},_=[].concat(om(O),[g]);return d=_[_.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:d})}),_},l)}else{var b=Yt(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var x=(i-2*b-(c-1)*s)/c;x>1&&(x>>=0);var w=u===+u?Math.min(x,u):x;f=o.reduce(function(O,m,g){var _=[].concat(om(O),[{item:m.item,position:{offset:b+(x+s)*g+(x-w)/2,size:w}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(S){_.push({item:S,position:_[_.length-1].position})}),_},l)}return f},dj=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=ix({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,h=f.height,p=s.align,y=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&p!=="center"&&N(t[p]))return pe(pe({},t),{},mr({},p,t[p]+(l||0)));if((v==="horizontal"||v==="vertical"&&p==="center")&&y!=="middle"&&N(t[y]))return pe(pe({},t),{},mr({},y,t[y]+(h||0)))}return t},vj=function(t,r,n){return J(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},ax=function(t,r,n,i,a){var o=r.props.children,u=Ke(o,ni).filter(function(s){return vj(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=Ce(f,n);if(J(l))return s;var h=Array.isArray(l)?[ka(l),Tt(l)]:[l,l],p=c.reduce(function(y,v){var d=Ce(f,v,0),b=h[0]-Math.abs(Array.isArray(d)?d[0]:d),x=h[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(b,y[0]),Math.max(x,y[1])]},[1/0,-1/0]);return[Math.min(p[0],s[0]),Math.max(p[1],s[1])]},[1/0,-1/0])}return null},yj=function(t,r,n,i,a){var o=r.map(function(u){return ax(t,u,n,a,i)}).filter(function(u){return!J(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},ox=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&ax(t,c,s,i)||pn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},ux=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},cx=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},pt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?et(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+s,value:l,offset:s}});return f.filter(function(l){return!Hr(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+s,value:l,index:h,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,h){return{coordinate:i(l)+s,value:a?a[l]:l,index:h,offset:s}})},ws=new WeakMap,pi=function(t,r){if(typeof r!="function")return t;ws.has(t)||ws.set(t,new WeakMap);var n=ws.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},mj=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:_n(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Li(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:hn(),realScaleType:"point"}:a==="category"?{scale:_n(),realScaleType:"band"}:{scale:Li(),realScaleType:"linear"};if(Xt(i)){var c="scale".concat(Oa(i));return{scale:(Gy[c]||hn)(),realScaleType:Gy[c]?c:"point"}}return V(i)?{scale:i}:{scale:hn(),realScaleType:"point"}},cm=1e-4,gj=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-cm,o=Math.max(i[0],i[1])+cm,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},bj=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},xj=function(t,r){if(!r||r.length!==2||!N(r[0])||!N(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!N(t[0])||t[0]<n)&&(a[0]=n),(!N(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},wj=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=Hr(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},Oj=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Hr(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},_j={sign:wj,expand:o1,none:br,silhouette:u1,wiggle:c1,positive:Oj},Sj=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=_j[n],o=a1().keys(i).value(function(u,c){return+Ce(u,c,0)}).order(nl).offset(a);return o(t)},Aj=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var p,y=(p=h.type)!==null&&p!==void 0&&p.defaultProps?pe(pe({},h.type.defaultProps),h.props):h.props,v=y.stackId,d=y.hide;if(d)return l;var b=y[n],x=l[b]||{hasStack:!1,stackGroups:{}};if(xe(v)){var w=x.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};w.items.push(h),x.hasStack=!0,x.stackGroups[v]=w}else x.stackGroups[Gr("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return pe(pe({},l),{},mr({},b,x))},c),f={};return Object.keys(s).reduce(function(l,h){var p=s[h];if(p.hasStack){var y={};p.stackGroups=Object.keys(p.stackGroups).reduce(function(v,d){var b=p.stackGroups[d];return pe(pe({},v),{},mr({},d,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:Sj(t,b.items,a)}))},y)}return pe(pe({},l),{},mr({},h,p))},f)},Pj=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=NT(s,a,u);return t.domain([ka(f),Tt(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=qT(l,a,u);return{niceTicks:h}}return null};function Gi(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!J(i[t.dataKey])){var u=bi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Ce(i,J(o)?t.dataKey:o);return J(c)?null:t.scale(c)}var sm=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Ce(o,r.dataKey,r.domain[u]);return J(c)?null:r.scale(c)-a/2+i},Ej=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Tj=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?pe(pe({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(xe(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},jj=function(t){return t.reduce(function(r,n){return[ka(n.concat([r[0]]).filter(N)),Tt(n.concat([r[1]]).filter(N))]},[1/0,-1/0])},sx=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=jj(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},lm=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,fm=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Tl=function(t,r,n){if(V(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(N(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(lm.test(t[0])){var a=+lm.exec(t[0])[1];i[0]=r[0]-a}else V(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(N(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(fm.test(t[1])){var o=+fm.exec(t[1])[1];i[1]=r[1]+o}else V(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Ki=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=If(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},hm=function(t,r,n){return!t||!t.length||Er(t,Ge(n,"type.defaultProps.domain"))?r:t},lx=function(t,r){var n=t.type.defaultProps?pe(pe({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return pe(pe({},Y(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:ah(t),value:Ce(r,i),type:c,payload:r,chartType:s,hide:f})};function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function pm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pm(Object(r),!0).forEach(function(n){Mj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Mj(e,t,r){return t=$j(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $j(e){var t=Cj(e,"string");return Cn(t)=="symbol"?t:t+""}function Cj(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Vi=Math.PI/180,Ij=function(t){return t*180/Math.PI},Pe=function(t,r,n,i){return{x:t+Math.cos(-Vi*i)*n,y:r+Math.sin(-Vi*i)*n}},Dj=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},kj=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=Dj({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:Ij(s),angleInRadian:s}},Rj=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},Nj=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},vm=function(t,r){var n=t.x,i=t.y,a=kj({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=Rj(r),l=f.startAngle,h=f.endAngle,p=u,y;if(l<=h){for(;p>h;)p-=360;for(;p<l;)p+=360;y=p>=l&&p<=h}else{for(;p>l;)p-=360;for(;p<h;)p+=360;y=p>=h&&p<=l}return y?dm(dm({},r),{},{radius:o,angle:Nj(p,r)}):null};function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}var qj=["offset"];function Lj(e){return Wj(e)||zj(e)||Fj(e)||Bj()}function Bj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Fj(e,t){if(e){if(typeof e=="string")return jl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jl(e,t)}}function zj(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Wj(e){if(Array.isArray(e))return jl(e)}function jl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Uj(e,t){if(e==null)return{};var r=Hj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Hj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ym(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function be(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ym(Object(r),!0).forEach(function(n){Gj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ym(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gj(e,t,r){return t=Kj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kj(e){var t=Vj(e,"string");return In(t)=="symbol"?t:t+""}function Vj(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Dn(){return Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Dn.apply(this,arguments)}var Xj=function(t){var r=t.value,n=t.formatter,i=J(t.children)?r:t.children;return V(n)?n(i):i},Yj=function(t,r){var n=et(r-t),i=Math.min(Math.abs(r-t),360);return n*i},Zj=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,p=c.startAngle,y=c.endAngle,v=c.clockWise,d=(l+h)/2,b=Yj(p,y),x=b>=0?1:-1,w,O;i==="insideStart"?(w=p+x*o,O=v):i==="insideEnd"?(w=y-x*o,O=!v):i==="end"&&(w=y+x*o,O=v),O=b<=0?O:!O;var m=Pe(s,f,d,w),g=Pe(s,f,d,w+(O?1:-1)*359),_="M".concat(m.x,",").concat(m.y,`
    A`).concat(d,",").concat(d,",0,1,").concat(O?0:1,`,
    `).concat(g.x,",").concat(g.y),S=J(t.id)?Gr("recharts-radial-line-"):t.id;return A.createElement("text",Dn({},n,{dominantBaseline:"central",className:Q("recharts-radial-bar-label",u)}),A.createElement("defs",null,A.createElement("path",{id:S,d:_})),A.createElement("textPath",{xlinkHref:"#".concat(S)},r))},Jj=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var p=Pe(o,u,s+n,h),y=p.x,v=p.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var d=(c+s)/2,b=Pe(o,u,d,h),x=b.x,w=b.y;return{x,y:w,textAnchor:"middle",verticalAnchor:"middle"}},Qj=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*i,p=l>0?"end":"start",y=l>0?"start":"end",v=s>=0?1:-1,d=v*i,b=v>0?"end":"start",x=v>0?"start":"end";if(a==="top"){var w={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:p};return be(be({},w),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:y};return be(be({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-d,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return be(be({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var g={x:u+s+d,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return be(be({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:f}:{})}var _=n?{width:s,height:f}:{};return a==="insideLeft"?be({x:u+d,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},_):a==="insideRight"?be({x:u+s-d,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},_):a==="insideTop"?be({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:y},_):a==="insideBottom"?be({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:p},_):a==="insideTopLeft"?be({x:u+d,y:c+h,textAnchor:x,verticalAnchor:y},_):a==="insideTopRight"?be({x:u+s-d,y:c+h,textAnchor:b,verticalAnchor:y},_):a==="insideBottomLeft"?be({x:u+d,y:c+f-h,textAnchor:x,verticalAnchor:p},_):a==="insideBottomRight"?be({x:u+s-d,y:c+f-h,textAnchor:b,verticalAnchor:p},_):Ur(a)&&(N(a.x)||zt(a.x))&&(N(a.y)||zt(a.y))?be({x:u+Yt(a.x,s),y:c+Yt(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):be({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},eM=function(t){return"cx"in t&&N(t.cx)};function Te(e){var t=e.offset,r=t===void 0?5:t,n=Uj(e,qj),i=be({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||J(u)&&J(c)&&!q.isValidElement(s)&&!V(s))return null;if(q.isValidElement(s))return q.cloneElement(s,i);var p;if(V(s)){if(p=q.createElement(s,i),q.isValidElement(p))return p}else p=Xj(i);var y=eM(a),v=Y(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return Zj(i,p,v);var d=y?Jj(i):Qj(i);return A.createElement(Ci,Dn({className:Q("recharts-label",l)},v,d,{breakAll:h}),p)}Te.displayName="Label";var fx=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,p=t.top,y=t.left,v=t.width,d=t.height,b=t.clockWise,x=t.labelViewBox;if(x)return x;if(N(v)&&N(d)){if(N(l)&&N(h))return{x:l,y:h,width:v,height:d};if(N(p)&&N(y))return{x:p,y,width:v,height:d}}return N(l)&&N(h)?{x:l,y:h,width:0,height:0}:N(r)&&N(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},tM=function(t,r){return t?t===!0?A.createElement(Te,{key:"label-implicit",viewBox:r}):xe(t)?A.createElement(Te,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===Te?q.cloneElement(t,{key:"label-implicit",viewBox:r}):A.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):V(t)?A.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):Ur(t)?A.createElement(Te,Dn({viewBox:r},t,{key:"label-implicit"})):null:null},rM=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=fx(t),o=Ke(i,Te).map(function(c,s){return q.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=tM(t.label,r||a);return[u].concat(Lj(o))};Te.parseViewBox=fx;Te.renderCallByParent=rM;var Os,mm;function nM(){if(mm)return Os;mm=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return Os=e,Os}var iM=nM();const aM=le(iM);function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}var oM=["valueAccessor"],uM=["data","dataKey","clockWise","id","textBreakAll"];function cM(e){return hM(e)||fM(e)||lM(e)||sM()}function sM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lM(e,t){if(e){if(typeof e=="string")return Ml(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ml(e,t)}}function fM(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function hM(e){if(Array.isArray(e))return Ml(e)}function Ml(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xi.apply(this,arguments)}function gm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gm(Object(r),!0).forEach(function(n){pM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pM(e,t,r){return t=dM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dM(e){var t=vM(e,"string");return kn(t)=="symbol"?t:t+""}function vM(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function xm(e,t){if(e==null)return{};var r=yM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var mM=function(t){return Array.isArray(t.value)?aM(t.value):t.value};function mt(e){var t=e.valueAccessor,r=t===void 0?mM:t,n=xm(e,oM),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=xm(n,uM);return!i||!i.length?null:A.createElement(ue,{className:"recharts-label-list"},i.map(function(f,l){var h=J(a)?r(f,l):Ce(f&&f.payload,a),p=J(u)?{}:{id:"".concat(u,"-").concat(l)};return A.createElement(Te,Xi({},Y(f,!0),s,p,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:Te.parseViewBox(J(o)?f:bm(bm({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}mt.displayName="LabelList";function gM(e,t){return e?e===!0?A.createElement(mt,{key:"labelList-implicit",data:t}):A.isValidElement(e)||V(e)?A.createElement(mt,{key:"labelList-implicit",data:t,content:e}):Ur(e)?A.createElement(mt,Xi({data:t},e,{key:"labelList-implicit"})):null:null}function bM(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ke(n,mt).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=gM(e.label,t);return[a].concat(cM(i))}mt.renderCallByParent=bM;function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function $l(){return $l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$l.apply(this,arguments)}function wm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Om(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wm(Object(r),!0).forEach(function(n){xM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xM(e,t,r){return t=wM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wM(e){var t=OM(e,"string");return Rn(t)=="symbol"?t:t+""}function OM(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var _M=function(t,r){var n=et(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},di=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/Vi,h=s?a:a+o*l,p=Pe(r,n,f,h),y=Pe(r,n,i,h),v=s?a-o*l:a,d=Pe(r,n,f*Math.cos(l*Vi),v);return{center:p,circleTangency:y,lineTangency:d,theta:l}},hx=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=_M(o,u),s=o+c,f=Pe(r,n,a,o),l=Pe(r,n,a,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var p=Pe(r,n,i,o),y=Pe(r,n,i,s);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},SM=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=et(f-s),h=di({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),p=h.circleTangency,y=h.lineTangency,v=h.theta,d=di({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=d.circleTangency,x=d.lineTangency,w=d.theta,O=c?Math.abs(s-f):Math.abs(s-f)-v-w;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):hx({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
  `);if(i>0){var g=di({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),_=g.circleTangency,S=g.lineTangency,E=g.theta,M=di({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),P=M.circleTangency,T=M.lineTangency,j=M.theta,C=c?Math.abs(s-f):Math.abs(s-f)-E-j;if(C<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(P.x,",").concat(P.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(C>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},AM={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},px=function(t){var r=Om(Om({},AM),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var p=Q("recharts-sector",h),y=o-a,v=Yt(u,y,0,!0),d;return v>0&&Math.abs(f-l)<360?d=SM({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):d=hx({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),A.createElement("path",$l({},Y(r,!0),{className:p,d,role:"img"}))};function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Cl(){return Cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cl.apply(this,arguments)}function _m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_m(Object(r),!0).forEach(function(n){PM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_m(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function PM(e,t,r){return t=EM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function EM(e){var t=TM(e,"string");return Nn(t)=="symbol"?t:t+""}function TM(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Am={curveBasisClosed:VO,curveBasisOpen:XO,curveBasis:KO,curveBumpX:IO,curveBumpY:DO,curveLinearClosed:YO,curveLinear:Sa,curveMonotoneX:ZO,curveMonotoneY:JO,curveNatural:QO,curveStep:e1,curveStepAfter:r1,curveStepBefore:t1},vi=function(t){return t.x===+t.x&&t.y===+t.y},nn=function(t){return t.x},an=function(t){return t.y},jM=function(t,r){if(V(t))return t;var n="curve".concat(Oa(t));return(n==="curveMonotone"||n==="curveBump")&&r?Am["".concat(n).concat(r==="vertical"?"Y":"X")]:Am[n]||Sa},MM=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=jM(n,u),l=s?a.filter(function(v){return vi(v)}):a,h;if(Array.isArray(o)){var p=s?o.filter(function(v){return vi(v)}):o,y=l.map(function(v,d){return Sm(Sm({},v),{},{base:p[d]})});return u==="vertical"?h=oi().y(an).x1(nn).x0(function(v){return v.base.x}):h=oi().x(nn).y1(an).y0(function(v){return v.base.y}),h.defined(vi).curve(f),h(y)}return u==="vertical"&&N(o)?h=oi().y(an).x1(nn).x0(o):N(o)?h=oi().x(nn).y1(an).y0(o):h=mb().x(nn).y(an),h.defined(vi).curve(f),h(l)},gr=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?MM(t):i;return A.createElement("path",Cl({},Y(t,!1),xi(t),{className:Q("recharts-curve",r),d:o,ref:a}))},$M=Object.getOwnPropertyNames,CM=Object.getOwnPropertySymbols,IM=Object.prototype.hasOwnProperty;function Pm(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function yi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function Em(e){return $M(e).concat(CM(e))}var DM=Object.hasOwn||function(e,t){return IM.call(e,t)};function ir(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var kM="__v",RM="__o",NM="_owner",Tm=Object.getOwnPropertyDescriptor,jm=Object.keys;function qM(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function LM(e,t){return ir(e.getTime(),t.getTime())}function BM(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function FM(e,t){return e===t}function Mm(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,p=u.value;if(r.equals(h[0],p[0],c,l,e,t,r)&&r.equals(h[1],p[1],h[0],p[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var zM=ir;function WM(e,t,r){var n=jm(e),i=n.length;if(jm(t).length!==i)return!1;for(;i-- >0;)if(!dx(e,t,r,n[i]))return!1;return!0}function on(e,t,r){var n=Em(e),i=n.length;if(Em(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!dx(e,t,r,a)||(o=Tm(e,a),u=Tm(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function UM(e,t){return ir(e.valueOf(),t.valueOf())}function HM(e,t){return e.source===t.source&&e.flags===t.flags}function $m(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function GM(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function KM(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function dx(e,t,r,n){return(n===NM||n===RM||n===kM)&&(e.$$typeof||t.$$typeof)?!0:DM(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var VM="[object Arguments]",XM="[object Boolean]",YM="[object Date]",ZM="[object Error]",JM="[object Map]",QM="[object Number]",e$="[object Object]",t$="[object RegExp]",r$="[object Set]",n$="[object String]",i$="[object URL]",a$=Array.isArray,Cm=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Im=Object.assign,o$=Object.prototype.toString.call.bind(Object.prototype.toString);function u$(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,d){if(y===v)return!0;if(y==null||v==null)return!1;var b=typeof y;if(b!==typeof v)return!1;if(b!=="object")return b==="number"?o(y,v,d):b==="function"?i(y,v,d):!1;var x=y.constructor;if(x!==v.constructor)return!1;if(x===Object)return u(y,v,d);if(a$(y))return t(y,v,d);if(Cm!=null&&Cm(y))return l(y,v,d);if(x===Date)return r(y,v,d);if(x===RegExp)return s(y,v,d);if(x===Map)return a(y,v,d);if(x===Set)return f(y,v,d);var w=o$(y);return w===YM?r(y,v,d):w===t$?s(y,v,d):w===JM?a(y,v,d):w===r$?f(y,v,d):w===e$?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,d):w===i$?h(y,v,d):w===ZM?n(y,v,d):w===VM?u(y,v,d):w===XM||w===QM||w===n$?c(y,v,d):!1}}function c$(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?on:qM,areDatesEqual:LM,areErrorsEqual:BM,areFunctionsEqual:FM,areMapsEqual:n?Pm(Mm,on):Mm,areNumbersEqual:zM,areObjectsEqual:n?on:WM,arePrimitiveWrappersEqual:UM,areRegExpsEqual:HM,areSetsEqual:n?Pm($m,on):$m,areTypedArraysEqual:n?on:GM,areUrlsEqual:KM};if(r&&(i=Im({},i,r(i))),t){var a=yi(i.areArraysEqual),o=yi(i.areMapsEqual),u=yi(i.areObjectsEqual),c=yi(i.areSetsEqual);i=Im({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function s$(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function l$(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,p=f.meta;return r(c,s,{cache:h,equals:i,meta:p,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var f$=kt();kt({strict:!0});kt({circular:!0});kt({circular:!0,strict:!0});kt({createInternalComparator:function(){return ir}});kt({strict:!0,createInternalComparator:function(){return ir}});kt({circular:!0,createInternalComparator:function(){return ir}});kt({circular:!0,createInternalComparator:function(){return ir},strict:!0});function kt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=c$(e),c=u$(u),s=n?n(c):s$(c);return l$({circular:r,comparator:c,createState:i,equals:s,strict:o})}function h$(e){typeof requestAnimationFrame!="undefined"&&requestAnimationFrame(e)}function Dm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):h$(i)};requestAnimationFrame(n)}function Il(e){"@babel/helpers - typeof";return Il=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Il(e)}function p$(e){return m$(e)||y$(e)||v$(e)||d$()}function d$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function v$(e,t){if(e){if(typeof e=="string")return km(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return km(e,t)}}function km(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function y$(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function m$(e){if(Array.isArray(e))return e}function g$(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=p$(o),c=u[0],s=u.slice(1);if(typeof c=="number"){Dm(i.bind(null,s),c);return}i(c),Dm(i.bind(null,s));return}Il(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function Rm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Nm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rm(Object(r),!0).forEach(function(n){vx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vx(e,t,r){return t=b$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b$(e){var t=x$(e,"string");return qn(t)==="symbol"?t:String(t)}function x$(e,t){if(qn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var w$=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},O$=function(t){return t},_$=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},dn=function(t,r){return Object.keys(r).reduce(function(n,i){return Nm(Nm({},n),{},vx({},i,t(i,r[i])))},{})},qm=function(t,r,n){return t.map(function(i){return"".concat(_$(i)," ").concat(r,"ms ").concat(n)}).join(",")};function S$(e,t){return E$(e)||P$(e,t)||yx(e,t)||A$()}function A$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function P$(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function E$(e){if(Array.isArray(e))return e}function T$(e){return $$(e)||M$(e)||yx(e)||j$()}function j$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yx(e,t){if(e){if(typeof e=="string")return Dl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dl(e,t)}}function M$(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function $$(e){if(Array.isArray(e))return Dl(e)}function Dl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Yi=1e-4,mx=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},gx=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Lm=function(t,r){return function(n){var i=mx(t,r);return gx(i,n)}},C$=function(t,r){return function(n){var i=mx(t,r),a=[].concat(T$(i.map(function(o,u){return o*u}).slice(1)),[0]);return gx(a,n)}},Bm=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(d){return parseFloat(d)}),f=S$(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=Lm(i,o),h=Lm(a,u),p=C$(i,o),y=function(b){return b>1?1:b<0?0:b},v=function(b){for(var x=b>1?1:b,w=x,O=0;O<8;++O){var m=l(w)-x,g=p(w);if(Math.abs(m-x)<Yi||g<Yi)return h(w);w=y(w-m/g)}return h(w)};return v.isStepper=!1,v},I$=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var p=-(f-l)*n,y=h*a,v=h+(p-y)*u/1e3,d=h*u/1e3+f;return Math.abs(d-l)<Yi&&Math.abs(v)<Yi?[l,0]:[d,v]};return c.isStepper=!0,c.dt=u,c},D$=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Bm(i);case"spring":return I$();default:if(i.split("(")[0]==="cubic-bezier")return Bm(i)}return typeof i=="function"?i:null};function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function Fm(e){return N$(e)||R$(e)||bx(e)||k$()}function k$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function R$(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function N$(e){if(Array.isArray(e))return Rl(e)}function zm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zm(Object(r),!0).forEach(function(n){kl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function kl(e,t,r){return t=q$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function q$(e){var t=L$(e,"string");return Ln(t)==="symbol"?t:String(t)}function L$(e,t){if(Ln(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function B$(e,t){return W$(e)||z$(e,t)||bx(e,t)||F$()}function F$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bx(e,t){if(e){if(typeof e=="string")return Rl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rl(e,t)}}function Rl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function z$(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function W$(e){if(Array.isArray(e))return e}var Zi=function(t,r,n){return t+(r-t)*n},Nl=function(t){var r=t.from,n=t.to;return r!==n},U$=function e(t,r,n){var i=dn(function(a,o){if(Nl(o)){var u=t(o.from,o.to,o.velocity),c=B$(u,2),s=c[0],f=c[1];return Se(Se({},o),{},{from:s,velocity:f})}return o},r);return n<1?dn(function(a,o){return Nl(o)?Se(Se({},o),{},{velocity:Zi(o.velocity,i[a].velocity,n),from:Zi(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const H$=function(e,t,r,n,i){var a=w$(e,t),o=a.reduce(function(d,b){return Se(Se({},d),{},kl({},b,[e[b],t[b]]))},{}),u=a.reduce(function(d,b){return Se(Se({},d),{},kl({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return dn(function(b,x){return x.from},u)},p=function(){return!Object.values(u).filter(Nl).length},y=function(b){s||(s=b);var x=b-s,w=x/r.dt;u=U$(r,u,w),i(Se(Se(Se({},e),t),h())),s=b,p()||(c=requestAnimationFrame(l))},v=function(b){f||(f=b);var x=(b-f)/n,w=dn(function(m,g){return Zi.apply(void 0,Fm(g).concat([r(x)]))},o);if(i(Se(Se(Se({},e),t),w)),x<1)c=requestAnimationFrame(l);else{var O=dn(function(m,g){return Zi.apply(void 0,Fm(g).concat([r(1)]))},o);i(Se(Se(Se({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}var G$=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function K$(e,t){if(e==null)return{};var r=V$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function V$(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function _s(e){return J$(e)||Z$(e)||Y$(e)||X$()}function X$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Y$(e,t){if(e){if(typeof e=="string")return ql(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ql(e,t)}}function Z$(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function J$(e){if(Array.isArray(e))return ql(e)}function ql(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Wm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wm(Object(r),!0).forEach(function(n){ln(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ln(e,t,r){return t=xx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Q$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function eC(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xx(n.key),n)}}function tC(e,t,r){return t&&eC(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function xx(e){var t=rC(e,"string");return jr(t)==="symbol"?t:String(t)}function rC(e,t){if(jr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function nC(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ll(e,t)}function Ll(e,t){return Ll=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ll(e,t)}function iC(e){var t=aC();return function(){var n=Ji(e),i;if(t){var a=Ji(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Bl(this,i)}}function Bl(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Fl(e)}function Fl(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function aC(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function Ji(e){return Ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ji(e)}var ut=function(e){nC(r,e);var t=iC(r);function r(n,i){var a;Q$(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,p=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Fl(a)),a.changeStyle=a.changeStyle.bind(Fl(a)),!u||p<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),Bl(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof h=="function")return a.state={style:s},Bl(a);a.state={style:c?ln({},c,s):s}}else a.state={style:{}};return a}return tC(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var p={style:c?ln({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(p);return}if(!(f$(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||s?l:i.to;if(this.state&&h){var d={style:c?ln({},c,v):v};(c&&h[c]!==v||!c&&h!==v)&&this.setState(d)}this.runAnimation(Ye(Ye({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,p=H$(o,u,D$(s),c,this.changeStyle),y=function(){a.stopJSAnimation=p()};this.manager.start([h,f,y,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,p=function(v,d,b){if(b===0)return v;var x=d.duration,w=d.easing,O=w===void 0?"ease":w,m=d.style,g=d.properties,_=d.onAnimationEnd,S=b>0?o[b-1]:d,E=g||Object.keys(m);if(typeof O=="function"||O==="spring")return[].concat(_s(v),[a.runJSAnimation.bind(a,{from:S.style,to:m,duration:x,easing:O}),x]);var M=qm(E,x,O),P=Ye(Ye(Ye({},S.style),m),{},{transition:M});return[].concat(_s(v),[P,x,_]).filter(O$)};return this.manager.start([c].concat(_s(o.reduce(p,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=g$());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,p=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof s=="function"||typeof p=="function"||s==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?ln({},u,c):c,d=qm(Object.keys(v),o,s);y.start([f,a,Ye(Ye({},v),{},{transition:d}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=K$(i,G$),s=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(p){var y=p.props,v=y.style,d=v===void 0?{}:v,b=y.className,x=q.cloneElement(p,Ye(Ye({},c),{},{style:Ye(Ye({},d),f),className:b}));return x};return s===1?l(q.Children.only(a)):A.createElement("div",null,q.Children.map(a,function(h){return l(h)}))}}]),r}(q.PureComponent);ut.displayName="Animate";ut.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};ut.propTypes={from:re.oneOfType([re.object,re.string]),to:re.oneOfType([re.object,re.string]),attributeName:re.string,duration:re.number,begin:re.number,easing:re.oneOfType([re.string,re.func]),steps:re.arrayOf(re.shape({duration:re.number.isRequired,style:re.object.isRequired,easing:re.oneOfType([re.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),re.func]),properties:re.arrayOf("string"),onAnimationEnd:re.func})),children:re.oneOfType([re.node,re.func]),isActive:re.bool,canBegin:re.bool,onAnimationEnd:re.func,shouldReAnimate:re.bool,onAnimationStart:re.func,onAnimationReStart:re.func};function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function Qi(){return Qi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qi.apply(this,arguments)}function oC(e,t){return lC(e)||sC(e,t)||cC(e,t)||uC()}function uC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cC(e,t){if(e){if(typeof e=="string")return Um(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Um(e,t)}}function Um(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function sC(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function lC(e){if(Array.isArray(e))return e}function Hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hm(Object(r),!0).forEach(function(n){fC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fC(e,t,r){return t=hC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hC(e){var t=pC(e,"string");return Bn(t)=="symbol"?t:t+""}function pC(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Km=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,p=4;h<p;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+c*y,",").concat(r,`
            L `).concat(t+n-c*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+n-c*y,",").concat(r+i,`
            L `).concat(t+c*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},dC=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=h}return!1},vC={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},oh=function(t){var r=Gm(Gm({},vC),t),n=q.useRef(),i=q.useState(-1),a=oC(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch(m){}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,b=r.isAnimationActive,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var w=Q("recharts-rectangle",p);return x?A.createElement(ut,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:y,isActive:x},function(O){var m=O.width,g=O.height,_=O.x,S=O.y;return A.createElement(ut,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,isActive:b,easing:y},A.createElement("path",Qi({},Y(r,!0),{className:w,d:Km(_,S,m,g,h),ref:n})))}):A.createElement("path",Qi({},Y(r,!0),{className:w,d:Km(c,s,f,l,h)}))};function zl(){return zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zl.apply(this,arguments)}var qa=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=Q("recharts-dot",a);return r===+r&&n===+n&&i===+i?A.createElement("circle",zl({},Y(t,!1),xi(t),{className:o,cx:r,cy:n,r:i})):null};function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}var yC=["x","y","top","left","width","height","className"];function Wl(){return Wl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wl.apply(this,arguments)}function Vm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mC(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vm(Object(r),!0).forEach(function(n){gC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gC(e,t,r){return t=bC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bC(e){var t=xC(e,"string");return Fn(t)=="symbol"?t:t+""}function xC(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wC(e,t){if(e==null)return{};var r=OC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function OC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var _C=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},SC=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,p=h===void 0?0:h,y=t.className,v=wC(t,yC),d=mC({x:n,y:a,top:u,left:s,width:l,height:p},v);return!N(n)||!N(a)||!N(l)||!N(p)||!N(u)||!N(s)?null:A.createElement("path",Wl({},Y(d,!0),{className:Q("recharts-cross",y),d:_C(n,a,l,p,u,s)}))},Ss,Xm;function AC(){if(Xm)return Ss;Xm=1;var e=Bb(),t=e(Object.getPrototypeOf,Object);return Ss=t,Ss}var As,Ym;function PC(){if(Ym)return As;Ym=1;var e=wt(),t=AC(),r=Ot(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,c=o.call(Object);function s(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==c}return As=s,As}var EC=PC();const TC=le(EC);var Ps,Zm;function jC(){if(Zm)return Ps;Zm=1;var e=wt(),t=Ot(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return Ps=n,Ps}var MC=jC();const $C=le(MC);function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function ea(){return ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ea.apply(this,arguments)}function CC(e,t){return RC(e)||kC(e,t)||DC(e,t)||IC()}function IC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DC(e,t){if(e){if(typeof e=="string")return Jm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jm(e,t)}}function Jm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function kC(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function RC(e){if(Array.isArray(e))return e}function Qm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function eg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qm(Object(r),!0).forEach(function(n){NC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function NC(e,t,r){return t=qC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qC(e){var t=LC(e,"string");return zn(t)=="symbol"?t:t+""}function LC(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var tg=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},BC={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},FC=function(t){var r=eg(eg({},BC),t),n=q.useRef(),i=q.useState(-1),a=CC(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch(O){}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var x=Q("recharts-trapezoid",p);return b?A.createElement(ut,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:v,animationEasing:y,isActive:b},function(w){var O=w.upperWidth,m=w.lowerWidth,g=w.height,_=w.x,S=w.y;return A.createElement(ut,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,easing:y},A.createElement("path",ea({},Y(r,!0),{className:x,d:tg(_,S,O,m,g),ref:n})))}):A.createElement("g",null,A.createElement("path",ea({},Y(r,!0),{className:x,d:tg(c,s,f,l,h)})))},zC=["option","shapeType","propTransformer","activeClassName","isActive"];function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function WC(e,t){if(e==null)return{};var r=UC(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function UC(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function rg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ta(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rg(Object(r),!0).forEach(function(n){HC(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HC(e,t,r){return t=GC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GC(e){var t=KC(e,"string");return Wn(t)=="symbol"?t:t+""}function KC(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function VC(e,t){return ta(ta({},t),e)}function XC(e,t){return e==="symbols"}function ng(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return A.createElement(oh,r);case"trapezoid":return A.createElement(FC,r);case"sector":return A.createElement(px,r);case"symbols":if(XC(t))return A.createElement(Af,r);break;default:return null}}function YC(e){return q.isValidElement(e)?e.props:e}function ZC(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?VC:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=WC(e,zC),s;if(q.isValidElement(t))s=q.cloneElement(t,ta(ta({},c),YC(t)));else if(V(t))s=t(c);else if(TC(t)&&!$C(t)){var f=i(t,c);s=A.createElement(ng,{shapeType:r,elementProps:f})}else{var l=c;s=A.createElement(ng,{shapeType:r,elementProps:l})}return u?A.createElement(ue,{className:o},s):s}function La(e,t){return t!=null&&"trapezoids"in e.props}function Ba(e,t){return t!=null&&"sectors"in e.props}function Un(e,t){return t!=null&&"points"in e.props}function JC(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function QC(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function eI(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function tI(e,t){var r;return La(e,t)?r=JC:Ba(e,t)?r=QC:Un(e,t)&&(r=eI),r}function rI(e,t){var r;return La(e,t)?r="trapezoids":Ba(e,t)?r="sectors":Un(e,t)&&(r="points"),r}function nI(e,t){if(La(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Ba(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Un(e,t)?t.payload:{}}function iI(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=rI(r,t),a=nI(r,t),o=n.filter(function(c,s){var f=Er(a,c),l=r.props[i].filter(function(y){var v=tI(r,t);return v(y,t)}),h=r.props[i].indexOf(l[l.length-1]),p=s===h;return f&&p}),u=n.indexOf(o[o.length-1]);return u}var Es,ig;function aI(){if(ig)return Es;ig=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,c=t(e((i-n)/(a||1)),0),s=Array(c);c--;)s[o?c:++u]=n,n+=a;return s}return Es=r,Es}var Ts,ag;function wx(){if(ag)return Ts;ag=1;var e=r0(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-t){var a=i<0?-1:1;return a*r}return i===i?i:0}return Ts=n,Ts}var js,og;function oI(){if(og)return js;og=1;var e=aI(),t=Ta(),r=wx();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return js=n,js}var Ms,ug;function uI(){if(ug)return Ms;ug=1;var e=oI(),t=e();return Ms=t,Ms}var cI=uI();const ra=le(cI);function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function cg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function sg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cg(Object(r),!0).forEach(function(n){Ox(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ox(e,t,r){return t=sI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sI(e){var t=lI(e,"string");return Hn(t)=="symbol"?t:t+""}function lI(e,t){if(Hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fI=["Webkit","Moz","O","ms"],hI=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=fI.reduce(function(a,o){return sg(sg({},a),{},Ox({},o+n,r))},{});return i[t]=r,i};function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function na(){return na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},na.apply(this,arguments)}function lg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $s(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lg(Object(r),!0).forEach(function(n){Le(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Sx(n.key),n)}}function dI(e,t,r){return t&&fg(e.prototype,t),r&&fg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function vI(e,t,r){return t=ia(t),yI(e,_x()?Reflect.construct(t,r||[],ia(e).constructor):t.apply(e,r))}function yI(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mI(e)}function mI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _x(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_x=function(){return!!e})()}function ia(e){return ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ia(e)}function gI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ul(e,t)}function Ul(e,t){return Ul=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ul(e,t)}function Le(e,t,r){return t=Sx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sx(e){var t=bI(e,"string");return Mr(t)=="symbol"?t:t+""}function bI(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var xI=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=hn().domain(ra(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},hg=function(t){return t.changedTouches&&!!t.changedTouches.length},$r=function(e){function t(r){var n;return pI(this,t),n=vI(this,t,[r]),Le(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Le(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Le(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Le(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Le(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Le(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Le(n,"handleSlideDragStart",function(i){var a=hg(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return gI(t,e),dI(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),h=Math.max(i,a),p=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:p-p%c,endIndex:y===f?f:y-y%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=Ce(a[n],u,n);return V(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,h=c.startIndex,p=c.endIndex,y=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var d=this.getIndex({startX:o+v,endX:u+v});(d.startIndex!==h||d.endIndex!==p)&&y&&y(d),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=hg(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,h=f.width,p=f.travellerWidth,y=f.onChange,v=f.gap,d=f.data,b={startX:this.state.startX,endX:this.state.endX},x=n.pageX-a;x>0?x=Math.min(x,l+h-p-s):x<0&&(x=Math.max(x,l-s)),b[o]=s+x;var w=this.getIndex(b),O=w.startIndex,m=w.endIndex,g=function(){var S=d.length-1;return o==="startX"&&(u>c?O%v===0:m%v===0)||u<c&&m===S||o==="endX"&&(u>c?m%v===0:O%v===0)||u>c&&m===S};this.setState(Le(Le({},o,s+x),"brushMoveStartX",n.pageX),function(){y&&g()&&y(w)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var p=u[h];i==="startX"&&p>=s||i==="endX"&&p<=c||this.setState(Le({},i,p),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return A.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=q.Children.only(s);return l?A.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,h=c.traveller,p=c.ariaLabel,y=c.data,v=c.startIndex,d=c.endIndex,b=Math.max(n,this.props.x),x=$s($s({},Y(this.props,!1)),{},{x:b,y:s,width:f,height:l}),w=p||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[d])===null||o===void 0?void 0:o.name);return A.createElement(ue,{tabIndex:0,role:"slider","aria-label":w,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,x))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return A.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,h=f.endX,p=5,y={pointerEvents:"none",fill:s};return A.createElement(ue,{className:"recharts-brush-texts"},A.createElement(Ci,na({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-p,y:o+u/2},y),this.getTextOfTick(i)),A.createElement(Ci,na({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+c+p,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,h=this.state,p=h.startX,y=h.endX,v=h.isTextActive,d=h.isSlideMoving,b=h.isTravellerMoving,x=h.isTravellerFocused;if(!i||!i.length||!N(u)||!N(c)||!N(s)||!N(f)||s<=0||f<=0)return null;var w=Q("recharts-brush",a),O=A.Children.count(o)===1,m=hI("userSelect","none");return A.createElement(ue,{className:w,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(p,y),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(y,"endX"),(v||d||b||x||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return A.createElement(A.Fragment,null,A.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),A.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),A.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return A.isValidElement(n)?a=A.cloneElement(n,i):V(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return $s({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?xI({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var h=i.scale.domain().map(function(p){return i.scale(p)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(q.PureComponent);Le($r,"displayName","Brush");Le($r,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Cs,pg;function wI(){if(pg)return Cs;pg=1;var e=Cf();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return Cs=t,Cs}var Is,dg;function OI(){if(dg)return Is;dg=1;var e=Ib(),t=Ct(),r=wI(),n=Re(),i=Ta();function a(o,u,c){var s=n(o)?e:r;return c&&i(o,u,c)&&(u=void 0),s(o,t(u,3))}return Is=a,Is}var _I=OI();const SI=le(_I);var at=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Ds,vg;function AI(){if(vg)return Ds;vg=1;var e=Zb();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return Ds=t,Ds}var ks,yg;function PI(){if(yg)return ks;yg=1;var e=AI(),t=Xb(),r=Ct();function n(i,a){var o={};return a=r(a,3),t(i,function(u,c,s){e(o,c,a(u,c,s))}),o}return ks=n,ks}var EI=PI();const TI=le(EI);var Rs,mg;function jI(){if(mg)return Rs;mg=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return Rs=e,Rs}var Ns,gg;function MI(){if(gg)return Ns;gg=1;var e=Cf();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return Ns=t,Ns}var qs,bg;function $I(){if(bg)return qs;bg=1;var e=jI(),t=MI(),r=Ct(),n=Re(),i=Ta();function a(o,u,c){var s=n(o)?e:t;return c&&i(o,u,c)&&(u=void 0),s(o,r(u,3))}return qs=a,qs}var CI=$I();const Ax=le(CI);var II=["x","y"];function Gn(e){"@babel/helpers - typeof";return Gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gn(e)}function Hl(){return Hl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hl.apply(this,arguments)}function xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function un(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xg(Object(r),!0).forEach(function(n){DI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DI(e,t,r){return t=kI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kI(e){var t=RI(e,"string");return Gn(t)=="symbol"?t:t+""}function RI(e,t){if(Gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function NI(e,t){if(e==null)return{};var r=qI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function qI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function LI(e,t){var r=e.x,n=e.y,i=NI(e,II),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),h=parseInt(l,10);return un(un(un(un(un({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function wg(e){return A.createElement(ZC,Hl({shapeType:"rectangle",propTransformer:LI,activeClassName:"recharts-active-bar"},e))}var BI=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||Jt(),r)}},FI=["value","background"],Px;function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function zI(e,t){if(e==null)return{};var r=WI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function WI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function aa(){return aa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},aa.apply(this,arguments)}function Og(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Og(Object(r),!0).forEach(function(n){jt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Og(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function UI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _g(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tx(n.key),n)}}function HI(e,t,r){return t&&_g(e.prototype,t),r&&_g(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function GI(e,t,r){return t=oa(t),KI(e,Ex()?Reflect.construct(t,r||[],oa(e).constructor):t.apply(e,r))}function KI(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return VI(e)}function VI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ex(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Ex=function(){return!!e})()}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function XI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gl(e,t)}function Gl(e,t){return Gl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gl(e,t)}function jt(e,t,r){return t=Tx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tx(e){var t=YI(e,"string");return Cr(t)=="symbol"?t:t+""}function YI(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ii=function(e){function t(){var r;UI(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=GI(this,t,[].concat(i)),jt(r,"state",{isAnimationFinished:!1}),jt(r,"id",Gr("recharts-bar-")),jt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),jt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return XI(t,e),HI(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=Y(this.props,!1);return n&&n.map(function(l,h){var p=h===c,y=p?s:o,v=ve(ve(ve({},f),l),{},{isActive:p,option:y,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return A.createElement(ue,aa({className:"recharts-bar-rectangle"},wi(i.props,l,h),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(h)}),A.createElement(wg,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return A.createElement(ut,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var y=p.t,v=a.map(function(d,b){var x=h&&h[b];if(x){var w=Ae(x.x,d.x),O=Ae(x.y,d.y),m=Ae(x.width,d.width),g=Ae(x.height,d.height);return ve(ve({},d),{},{x:w(y),y:O(y),width:m(y),height:g(y)})}if(o==="horizontal"){var _=Ae(0,d.height),S=_(y);return ve(ve({},d),{},{y:d.y+d.height-S,height:S})}var E=Ae(0,d.width),M=E(y);return ve(ve({},d),{},{width:M})});return A.createElement(ue,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Er(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=Y(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,h=zI(s,FI);if(!l)return null;var p=ve(ve(ve(ve(ve({},h),{},{fill:"#eee"},l),c),wi(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return A.createElement(wg,aa({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},p))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ke(f,ni);if(!l)return null;var h=s==="vertical"?o[0].height/2:o[0].width/2,p=function(d,b){var x=Array.isArray(d.value)?d.value[1]:d.value;return{x:d.x,y:d.y,value:x,errorVal:Ce(d,b)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(ue,y,l.map(function(v){return A.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:h,dataPointFormatter:p})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,h=n.height,p=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var d=this.state.isAnimationFinished,b=Q("recharts-bar",o),x=u&&u.allowDataOverflow,w=c&&c.allowDataOverflow,O=x||w,m=J(v)?this.id:v;return A.createElement(ue,{className:b},x||w?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(m)},A.createElement("rect",{x:x?s:s-l/2,y:w?f:f-h/2,width:x?l:l*2,height:w?h:h*2}))):null,A.createElement(ue,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(m,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,m),(!p||d)&&mt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);Px=ii;jt(ii,"displayName","Bar");jt(ii,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!tr.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});jt(ii,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,p=bj(n,r);if(!p)return null;var y=t.layout,v=r.type.defaultProps,d=v!==void 0?ve(ve({},v),r.props):r.props,b=d.dataKey,x=d.children,w=d.minPointSize,O=y==="horizontal"?o:a,m=s?O.scale.domain():null,g=Ej({numericAxis:O}),_=Ke(x,i0),S=l.map(function(E,M){var P,T,j,C,$,D;s?P=xj(s[f+M],m):(P=Ce(E,b),Array.isArray(P)||(P=[g,P]));var k=BI(w,Px.defaultProps.minPointSize)(P[1],M);if(y==="horizontal"){var L,B=[o.scale(P[0]),o.scale(P[1])],U=B[0],G=B[1];T=sm({axis:a,ticks:u,bandSize:i,offset:p.offset,entry:E,index:M}),j=(L=G!=null?G:U)!==null&&L!==void 0?L:void 0,C=p.size;var z=U-G;if($=Number.isNaN(z)?0:z,D={x:T,y:o.y,width:C,height:o.height},Math.abs(k)>0&&Math.abs($)<Math.abs(k)){var K=et($||k)*(Math.abs(k)-Math.abs($));j-=K,$+=K}}else{var ce=[a.scale(P[0]),a.scale(P[1])],de=ce[0],Ne=ce[1];if(T=de,j=sm({axis:o,ticks:c,bandSize:i,offset:p.offset,entry:E,index:M}),C=Ne-de,$=p.size,D={x:a.x,y:j,width:a.width,height:$},Math.abs(k)>0&&Math.abs(C)<Math.abs(k)){var Rt=et(C||k)*(Math.abs(k)-Math.abs(C));C+=Rt}}return ve(ve(ve({},E),{},{x:T,y:j,width:C,height:$,value:s?P:P[1],payload:E,background:D},_&&_[M]&&_[M].props),{},{tooltipPayload:[lx(r,E)],tooltipPosition:{x:T+C/2,y:j+$/2}})});return ve({data:S,layout:y},h)});function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function ZI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,jx(n.key),n)}}function JI(e,t,r){return t&&Sg(e.prototype,t),r&&Sg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ag(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ze(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ag(Object(r),!0).forEach(function(n){Fa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ag(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fa(e,t,r){return t=jx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jx(e){var t=QI(e,"string");return Kn(t)=="symbol"?t:t+""}function QI(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Mx=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!Be(s,ii);return f.reduce(function(p,y){var v=r[y],d=v.orientation,b=v.domain,x=v.padding,w=x===void 0?{}:x,O=v.mirror,m=v.reversed,g="".concat(d).concat(O?"Mirror":""),_,S,E,M,P;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var T=b[1]-b[0],j=1/0,C=v.categoricalDomain.sort(iO);if(C.forEach(function(ce,de){de>0&&(j=Math.min((ce||0)-(C[de-1]||0),j))}),Number.isFinite(j)){var $=j/T,D=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(_=$*D/2),v.padding==="no-gap"){var k=Yt(t.barCategoryGap,$*D),L=$*D/2;_=L-k-(L-k)/D*k}}}i==="xAxis"?S=[n.left+(w.left||0)+(_||0),n.left+n.width-(w.right||0)-(_||0)]:i==="yAxis"?S=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(_||0),n.top+n.height-(w.bottom||0)-(_||0)]:S=v.range,m&&(S=[S[1],S[0]]);var B=mj(v,a,h),U=B.scale,G=B.realScaleType;U.domain(b).range(S),gj(U);var z=Pj(U,Ze(Ze({},v),{},{realScaleType:G}));i==="xAxis"?(P=d==="top"&&!O||d==="bottom"&&O,E=n.left,M=l[g]-P*v.height):i==="yAxis"&&(P=d==="left"&&!O||d==="right"&&O,E=l[g]-P*v.width,M=n.top);var K=Ze(Ze(Ze({},v),z),{},{realScaleType:G,x:E,y:M,scale:U,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return K.bandSize=Ki(K,z),!v.hide&&i==="xAxis"?l[g]+=(P?-1:1)*K.height:v.hide||(l[g]+=(P?-1:1)*K.width),Ze(Ze({},p),{},Fa({},y,K))},{})},$x=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},eD=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return $x({x:r,y:n},{x:i,y:a})},Cx=function(){function e(t){ZI(this,e),this.scale=t}return JI(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();Fa(Cx,"EPS",1e-4);var uh=function(t){var r=Object.keys(t).reduce(function(n,i){return Ze(Ze({},n),{},Fa({},i,Cx.create(t[i])))},{});return Ze(Ze({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return TI(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Ax(i,function(a,o){return r[o].isInRange(a)})}})};function tD(e){return(e%180+180)%180}var rD=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=tD(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},Ls,Pg;function nD(){if(Pg)return Ls;Pg=1;var e=Ct(),t=Jn(),r=Pa();function n(i){return function(a,o,u){var c=Object(a);if(!t(a)){var s=e(o,3);a=r(a),o=function(l){return s(c[l],l,c)}}var f=i(a,o,u);return f>-1?c[s?a[f]:f]:void 0}}return Ls=n,Ls}var Bs,Eg;function iD(){if(Eg)return Bs;Eg=1;var e=wx();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return Bs=t,Bs}var Fs,Tg;function aD(){if(Tg)return Fs;Tg=1;var e=Ub(),t=Ct(),r=iD(),n=Math.max;function i(a,o,u){var c=a==null?0:a.length;if(!c)return-1;var s=u==null?0:r(u);return s<0&&(s=n(c+s,0)),e(a,t(o,3),s)}return Fs=i,Fs}var zs,jg;function oD(){if(jg)return zs;jg=1;var e=nD(),t=aD(),r=e(t);return zs=r,zs}var uD=oD();const cD=le(uD);var sD=ab();const lD=le(sD);var fD=lD(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),ch=q.createContext(void 0),sh=q.createContext(void 0),Ix=q.createContext(void 0),Dx=q.createContext({}),kx=q.createContext(void 0),Rx=q.createContext(0),Nx=q.createContext(0),Mg=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=fD(a);return A.createElement(ch.Provider,{value:n},A.createElement(sh.Provider,{value:i},A.createElement(Dx.Provider,{value:a},A.createElement(Ix.Provider,{value:f},A.createElement(kx.Provider,{value:o},A.createElement(Rx.Provider,{value:s},A.createElement(Nx.Provider,{value:c},u)))))))},hD=function(){return q.useContext(kx)},qx=function(t){var r=q.useContext(ch);r==null&&Jt();var n=r[t];return n==null&&Jt(),n},pD=function(){var t=q.useContext(ch);return Et(t)},dD=function(){var t=q.useContext(sh),r=cD(t,function(n){return Ax(n.domain,Number.isFinite)});return r||Et(t)},Lx=function(t){var r=q.useContext(sh);r==null&&Jt();var n=r[t];return n==null&&Jt(),n},vD=function(){var t=q.useContext(Ix);return t},yD=function(){return q.useContext(Dx)},lh=function(){return q.useContext(Nx)},fh=function(){return q.useContext(Rx)};function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function mD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Fx(n.key),n)}}function bD(e,t,r){return t&&gD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function xD(e,t,r){return t=ua(t),wD(e,Bx()?Reflect.construct(t,r||[],ua(e).constructor):t.apply(e,r))}function wD(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return OD(e)}function OD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Bx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Bx=function(){return!!e})()}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}function _D(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kl(e,t)}function Kl(e,t){return Kl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Kl(e,t)}function $g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Cg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$g(Object(r),!0).forEach(function(n){hh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$g(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hh(e,t,r){return t=Fx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fx(e){var t=SD(e,"string");return Ir(t)=="symbol"?t:t+""}function SD(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function AD(e,t){return jD(e)||TD(e,t)||ED(e,t)||PD()}function PD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ED(e,t){if(e){if(typeof e=="string")return Ig(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ig(e,t)}}function Ig(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function TD(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function jD(e){if(Array.isArray(e))return e}function Vl(){return Vl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vl.apply(this,arguments)}var MD=function(t,r){var n;return A.isValidElement(t)?n=A.cloneElement(t,r):V(t)?n=t(r):n=A.createElement("line",Vl({},r,{className:"recharts-reference-line-line"})),n},$D=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,h=a.width,p=a.height;if(n){var y=s.y,v=t.y.apply(y,{position:o});if(at(s,"discard")&&!t.y.isInRange(v))return null;var d=[{x:f+h,y:v},{x:f,y:v}];return c==="left"?d.reverse():d}if(r){var b=s.x,x=t.x.apply(b,{position:o});if(at(s,"discard")&&!t.x.isInRange(x))return null;var w=[{x,y:l+p},{x,y:l}];return u==="top"?w.reverse():w}if(i){var O=s.segment,m=O.map(function(g){return t.apply(g,{position:o})});return at(s,"discard")&&SI(m,function(g){return!t.isInRange(g)})?null:m}return null};function CD(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=hD(),f=qx(i),l=Lx(a),h=vD();if(!s||!h)return null;vt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var p=uh({x:f.scale,y:l.scale}),y=xe(t),v=xe(r),d=n&&n.length===2,b=$D(p,y,v,d,h,e.position,f.orientation,l.orientation,e);if(!b)return null;var x=AD(b,2),w=x[0],O=w.x,m=w.y,g=x[1],_=g.x,S=g.y,E=at(e,"hidden")?"url(#".concat(s,")"):void 0,M=Cg(Cg({clipPath:E},Y(e,!0)),{},{x1:O,y1:m,x2:_,y2:S});return A.createElement(ue,{className:Q("recharts-reference-line",u)},MD(o,M),Te.renderCallByParent(e,eD({x1:O,y1:m,x2:_,y2:S})))}var ph=function(e){function t(){return mD(this,t),xD(this,t,arguments)}return _D(t,e),bD(t,[{key:"render",value:function(){return A.createElement(CD,this.props)}}])}(A.Component);hh(ph,"displayName","ReferenceLine");hh(ph,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Xl(){return Xl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xl.apply(this,arguments)}function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function Dg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function kg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dg(Object(r),!0).forEach(function(n){za(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ID(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function DD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Wx(n.key),n)}}function kD(e,t,r){return t&&DD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function RD(e,t,r){return t=ca(t),ND(e,zx()?Reflect.construct(t,r||[],ca(e).constructor):t.apply(e,r))}function ND(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return qD(e)}function qD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(zx=function(){return!!e})()}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function LD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yl(e,t)}function Yl(e,t){return Yl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yl(e,t)}function za(e,t,r){return t=Wx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wx(e){var t=BD(e,"string");return Dr(t)=="symbol"?t:t+""}function BD(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var FD=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=uh({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return at(t,"discard")&&!o.isInRange(u)?null:u},Wa=function(e){function t(){return ID(this,t),RD(this,t,arguments)}return LD(t,e),kD(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=xe(i),f=xe(a);if(vt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=FD(this.props);if(!l)return null;var h=l.x,p=l.y,y=this.props,v=y.shape,d=y.className,b=at(this.props,"hidden")?"url(#".concat(c,")"):void 0,x=kg(kg({clipPath:b},Y(this.props,!0)),{},{cx:h,cy:p});return A.createElement(ue,{className:Q("recharts-reference-dot",d)},t.renderDot(v,x),Te.renderCallByParent(this.props,{x:h-o,y:p-o,width:2*o,height:2*o}))}}])}(A.Component);za(Wa,"displayName","ReferenceDot");za(Wa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});za(Wa,"renderDot",function(e,t){var r;return A.isValidElement(e)?r=A.cloneElement(e,t):V(e)?r=e(t):r=A.createElement(qa,Xl({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Zl(){return Zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zl.apply(this,arguments)}function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function Rg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ng(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rg(Object(r),!0).forEach(function(n){Ua(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function WD(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hx(n.key),n)}}function UD(e,t,r){return t&&WD(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function HD(e,t,r){return t=sa(t),GD(e,Ux()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function GD(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return KD(e)}function KD(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ux(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Ux=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function VD(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jl(e,t)}function Jl(e,t){return Jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Jl(e,t)}function Ua(e,t,r){return t=Hx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hx(e){var t=XD(e,"string");return kr(t)=="symbol"?t:t+""}function XD(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var YD=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=uh({x:f.scale,y:l.scale}),p={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(s,{position:"end"}):h.y.rangeMax};return at(a,"discard")&&(!h.isInRange(p)||!h.isInRange(y))?null:$x(p,y)},Ha=function(e){function t(){return zD(this,t),HD(this,t,arguments)}return VD(t,e),UD(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;vt(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=xe(i),h=xe(a),p=xe(o),y=xe(u),v=this.props.shape;if(!l&&!h&&!p&&!y&&!v)return null;var d=YD(l,h,p,y,this.props);if(!d&&!v)return null;var b=at(this.props,"hidden")?"url(#".concat(f,")"):void 0;return A.createElement(ue,{className:Q("recharts-reference-area",c)},t.renderRect(v,Ng(Ng({clipPath:b},Y(this.props,!0)),d)),Te.renderCallByParent(this.props,d))}}])}(A.Component);Ua(Ha,"displayName","ReferenceArea");Ua(Ha,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Ua(Ha,"renderRect",function(e,t){var r;return A.isValidElement(e)?r=A.cloneElement(e,t):V(e)?r=e(t):r=A.createElement(oh,Zl({},t,{className:"recharts-reference-area-rect"})),r});function Gx(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function ZD(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return rD(n,r)}function JD(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function la(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function QD(e,t){return Gx(e,t+1)}function ek(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var y=n==null?void 0:n[c];if(y===void 0)return{v:Gx(n,s)};var v=c,d,b=function(){return d===void 0&&(d=r(y,v)),d},x=y.coordinate,w=c===0||la(e,x,b,f,u);w||(c=0,f=o,s+=1),w&&(f=x+e*(b()/2+i),c+=s)},h;s<=a.length;)if(h=l(),h)return h.v;return[]}function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}function qg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qg(Object(r),!0).forEach(function(n){tk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tk(e,t,r){return t=rk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rk(e){var t=nk(e,"string");return Vn(t)=="symbol"?t:t+""}function nk(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ik(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(h){var p=a[h],y,v=function(){return y===void 0&&(y=r(p,h)),y};if(h===o-1){var d=e*(p.coordinate+e*v()/2-c);a[h]=p=Ee(Ee({},p),{},{tickCoord:d>0?p.coordinate-d*e:p.coordinate})}else a[h]=p=Ee(Ee({},p),{},{tickCoord:p.coordinate});var b=la(e,p.tickCoord,v,u,c);b&&(c=p.tickCoord-e*(v()/2+i),a[h]=Ee(Ee({},p),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function ak(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=Ee(Ee({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var p=la(e,f.tickCoord,function(){return l},c,s);p&&(s=f.tickCoord-e*(l/2+i),o[u-1]=Ee(Ee({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(x){var w=o[x],O,m=function(){return O===void 0&&(O=r(w,x)),O};if(x===0){var g=e*(w.coordinate-e*m()/2-c);o[x]=w=Ee(Ee({},w),{},{tickCoord:g<0?w.coordinate-g*e:w.coordinate})}else o[x]=w=Ee(Ee({},w),{},{tickCoord:w.coordinate});var _=la(e,w.tickCoord,m,c,s);_&&(c=w.tickCoord+e*(m()/2+i),o[x]=Ee(Ee({},w),{},{isShow:!0}))},d=0;d<y;d++)v(d);return o}function dh(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(N(c)||tr.isSsr)return QD(i,typeof c=="number"&&N(c)?c:0);var h=[],p=u==="top"||u==="bottom"?"width":"height",y=f&&p==="width"?fn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(w,O){var m=V(s)?s(w.value,O):w.value;return p==="width"?ZD(fn(m,{fontSize:t,letterSpacing:r}),y,l):fn(m,{fontSize:t,letterSpacing:r})[p]},d=i.length>=2?et(i[1].coordinate-i[0].coordinate):1,b=JD(a,d,p);return c==="equidistantPreserveStart"?ek(d,b,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?h=ak(d,b,v,i,o,c==="preserveStartEnd"):h=ik(d,b,v,i,o),h.filter(function(x){return x.isShow}))}var ok=["viewBox"],uk=["viewBox"],ck=["ticks"];function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function hr(){return hr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hr.apply(this,arguments)}function Lg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lg(Object(r),!0).forEach(function(n){vh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ws(e,t){if(e==null)return{};var r=sk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function sk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function lk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vx(n.key),n)}}function fk(e,t,r){return t&&Bg(e.prototype,t),r&&Bg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function hk(e,t,r){return t=fa(t),pk(e,Kx()?Reflect.construct(t,r||[],fa(e).constructor):t.apply(e,r))}function pk(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return dk(e)}function dk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Kx=function(){return!!e})()}function fa(e){return fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fa(e)}function vk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ql(e,t)}function Ql(e,t){return Ql=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ql(e,t)}function vh(e,t,r){return t=Vx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vx(e){var t=yk(e,"string");return Rr(t)=="symbol"?t:t+""}function yk(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Zr=function(e){function t(r){var n;return lk(this,t),n=hk(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return vk(t,e),fk(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=Ws(n,ok),u=this.props,c=u.viewBox,s=Ws(u,uk);return!dr(a,c)||!dr(o,s)||!dr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,p,y,v,d,b,x,w=l?-1:1,O=n.tickSize||f,m=N(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":p=y=n.coordinate,d=o+ +!l*c,v=d-w*O,x=v-w*h,b=m;break;case"left":v=d=n.coordinate,y=a+ +!l*u,p=y-w*O,b=p-w*h,x=m;break;case"right":v=d=n.coordinate,y=a+ +l*u,p=y+w*O,b=p+w*h,x=m;break;default:p=y=n.coordinate,d=o+ +l*c,v=d+w*O,x=v+w*h,b=m;break}return{line:{x1:p,y1:v,x2:y,y2:d},tick:{x:b,y:x}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=Me(Me(Me({},Y(this.props,!1)),Y(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var h=+(c==="top"&&!s||c==="bottom"&&s);l=Me(Me({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var p=+(c==="left"&&!s||c==="right"&&s);l=Me(Me({},l),{},{x1:i+p*o,y1:a,x2:i+p*o,y2:a+u})}return A.createElement("line",hr({},l,{className:Q("recharts-cartesian-axis-line",Ge(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,p=dh(Me(Me({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),d=Y(this.props,!1),b=Y(f,!1),x=Me(Me({},d),{},{fill:"none"},Y(c,!1)),w=p.map(function(O,m){var g=o.getTickLineCoord(O),_=g.line,S=g.tick,E=Me(Me(Me(Me({textAnchor:y,verticalAnchor:v},d),{},{stroke:"none",fill:s},b),S),{},{index:m,payload:O,visibleTicksCount:p.length,tickFormatter:l});return A.createElement(ue,hr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},wi(o.props,O,m)),c&&A.createElement("line",hr({},x,_,{className:Q("recharts-cartesian-axis-tick-line",Ge(c,"className"))})),f&&t.renderTickItem(f,E,"".concat(V(l)?l(O.value,m):O.value).concat(h||"")))});return A.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,p=Ws(l,ck),y=h;return V(c)&&(y=h&&h.length>0?c(this.props):c(p)),o<=0||u<=0||!y||!y.length?null:A.createElement(ue,{className:Q("recharts-cartesian-axis",s),ref:function(d){n.layerReference=d}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),Te.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return A.isValidElement(n)?o=A.cloneElement(n,i):V(n)?o=n(i):o=A.createElement(Ci,hr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);vh(Zr,"displayName","CartesianAxis");vh(Zr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var mk=["x1","y1","x2","y2","key"],gk=["offset"];function Qt(e){"@babel/helpers - typeof";return Qt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(e)}function Fg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fg(Object(r),!0).forEach(function(n){bk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bk(e,t,r){return t=xk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xk(e){var t=wk(e,"string");return Qt(t)=="symbol"?t:t+""}function wk(e,t){if(Qt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Qt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ht(){return Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ht.apply(this,arguments)}function zg(e,t){if(e==null)return{};var r=Ok(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ok(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var _k=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return A.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Xx(e,t){var r;if(A.isValidElement(e))r=A.cloneElement(e,t);else if(V(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=zg(t,mk),s=Y(c,!1);s.offset;var f=zg(s,gk);r=A.createElement("line",Ht({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function Sk(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=je(je({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return Xx(i,s)});return A.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function Ak(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=je(je({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return Xx(i,s)});return A.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function Pk(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,p){return h-p});i!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?i+o-h:f[p+1]-h;if(v<=0)return null;var d=p%t.length;return A.createElement("rect",{key:"react-".concat(p),y:h,x:n,height:v,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function Ek(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(h){return Math.round(h+a-a)}).sort(function(h,p){return h-p});a!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?a+u-h:f[p+1]-h;if(v<=0)return null;var d=p%n.length;return A.createElement("rect",{key:"react-".concat(p),x:h,y:o,width:v,height:c,stroke:"none",fill:n[d],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return A.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var Tk=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return cx(dh(je(je(je({},Zr.defaultProps),n),{},{ticks:pt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},jk=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return cx(dh(je(je(je({},Zr.defaultProps),n),{},{ticks:pt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},sr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Mk(e){var t,r,n,i,a,o,u=lh(),c=fh(),s=yD(),f=je(je({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:sr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:sr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:sr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:sr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:sr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:sr.verticalFill,x:N(e.x)?e.x:s.left,y:N(e.y)?e.y:s.top,width:N(e.width)?e.width:s.width,height:N(e.height)?e.height:s.height}),l=f.x,h=f.y,p=f.width,y=f.height,v=f.syncWithTicks,d=f.horizontalValues,b=f.verticalValues,x=pD(),w=dD();if(!N(p)||p<=0||!N(y)||y<=0||!N(l)||l!==+l||!N(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||Tk,m=f.horizontalCoordinatesGenerator||jk,g=f.horizontalPoints,_=f.verticalPoints;if((!g||!g.length)&&V(m)){var S=d&&d.length,E=m({yAxis:w?je(je({},w),{},{ticks:S?d:w.ticks}):void 0,width:u,height:c,offset:s},S?!0:v);vt(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Qt(E),"]")),Array.isArray(E)&&(g=E)}if((!_||!_.length)&&V(O)){var M=b&&b.length,P=O({xAxis:x?je(je({},x),{},{ticks:M?b:x.ticks}):void 0,width:u,height:c,offset:s},M?!0:v);vt(Array.isArray(P),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Qt(P),"]")),Array.isArray(P)&&(_=P)}return A.createElement("g",{className:"recharts-cartesian-grid"},A.createElement(_k,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),A.createElement(Sk,Ht({},f,{offset:s,horizontalPoints:g,xAxis:x,yAxis:w})),A.createElement(Ak,Ht({},f,{offset:s,verticalPoints:_,xAxis:x,yAxis:w})),A.createElement(Pk,Ht({},f,{horizontalPoints:g})),A.createElement(Ek,Ht({},f,{verticalPoints:_})))}Mk.displayName="CartesianGrid";var $k=["type","layout","connectNulls","ref"],Ck=["key"];function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function Wg(e,t){if(e==null)return{};var r=Ik(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ik(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function vn(){return vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},vn.apply(this,arguments)}function Ug(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ug(Object(r),!0).forEach(function(n){Je(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ug(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lr(e){return Nk(e)||Rk(e)||kk(e)||Dk()}function Dk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kk(e,t){if(e){if(typeof e=="string")return ef(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ef(e,t)}}function Rk(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Nk(e){if(Array.isArray(e))return ef(e)}function ef(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function qk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Hg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Zx(n.key),n)}}function Lk(e,t,r){return t&&Hg(e.prototype,t),r&&Hg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Bk(e,t,r){return t=ha(t),Fk(e,Yx()?Reflect.construct(t,r||[],ha(e).constructor):t.apply(e,r))}function Fk(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return zk(e)}function zk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Yx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Yx=function(){return!!e})()}function ha(e){return ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ha(e)}function Wk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tf(e,t)}function tf(e,t){return tf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},tf(e,t)}function Je(e,t,r){return t=Zx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Zx(e){var t=Uk(e,"string");return Nr(t)=="symbol"?t:t+""}function Uk(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ga=function(e){function t(){var r;qk(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Bk(this,t,[].concat(i)),Je(r,"state",{isAnimationFinished:!0,totalLength:0}),Je(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),Je(r,"getStrokeDasharray",function(o,u,c){var s=c.reduce(function(b,x){return b+x});if(!s)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/s),l=o%s,h=u-o,p=[],y=0,v=0;y<c.length;v+=c[y],++y)if(v+c[y]>l){p=[].concat(lr(c.slice(0,y)),[l-v]);break}var d=p.length%2===0?[0,h]:[h];return[].concat(lr(t.repeat(c,f)),lr(p),d).map(function(b){return"".concat(b,"px")}).join(", ")}),Je(r,"id",Gr("recharts-line-")),Je(r,"pathRef",function(o){r.mainCurve=o}),Je(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),Je(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return Wk(t,e),Lk(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch(i){return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ke(f,ni);if(!l)return null;var h=function(v,d){return{x:v.x,y:v.y,value:v.value,errorVal:Ce(v.payload,d)}},p={clipPath:n?"url(#clipPath-".concat(i,")"):null};return A.createElement(ue,p,l.map(function(y){return A.cloneElement(y,{key:"bar-".concat(y.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,dataPointFormatter:h})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,s=u.points,f=u.dataKey,l=Y(this.props,!1),h=Y(c,!0),p=s.map(function(v,d){var b=qe(qe(qe({key:"dot-".concat(d),r:3},l),h),{},{index:d,cx:v.x,cy:v.y,value:v.value,dataKey:f,payload:v.payload,points:s});return t.renderDotItem(c,b)}),y={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return A.createElement(ue,vn({className:"recharts-line-dots",key:"dots"},y),p)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,s=u.layout,f=u.connectNulls;u.ref;var l=Wg(u,$k),h=qe(qe(qe({},Y(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:s,connectNulls:f});return A.createElement(gr,vn({},h,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,h=o.animationEasing,p=o.animationId,y=o.animateNewValues,v=o.width,d=o.height,b=this.state,x=b.prevPoints,w=b.totalLength;return A.createElement(ut,{begin:f,duration:l,isActive:s,easing:h,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(O){var m=O.t;if(x){var g=x.length/u.length,_=u.map(function(T,j){var C=Math.floor(j*g);if(x[C]){var $=x[C],D=Ae($.x,T.x),k=Ae($.y,T.y);return qe(qe({},T),{},{x:D(m),y:k(m)})}if(y){var L=Ae(v*2,T.x),B=Ae(d/2,T.y);return qe(qe({},T),{},{x:L(m),y:B(m)})}return qe(qe({},T),{},{x:T.x,y:T.y})});return a.renderCurveStatically(_,n,i)}var S=Ae(0,w),E=S(m),M;if(c){var P="".concat(c).split(/[,\s]+/gim).map(function(T){return parseFloat(T)});M=a.getStrokeDasharray(E,w,P)}else M=a.generateSimpleStrokeDasharray(w,E);return a.renderCurveStatically(u,n,i,{strokeDasharray:M})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,s=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!s&&f>0||!Er(s,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.xAxis,f=i.yAxis,l=i.top,h=i.left,p=i.width,y=i.height,v=i.isAnimationActive,d=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,x=u.length===1,w=Q("recharts-line",c),O=s&&s.allowDataOverflow,m=f&&f.allowDataOverflow,g=O||m,_=J(d)?this.id:d,S=(n=Y(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},E=S.r,M=E===void 0?3:E,P=S.strokeWidth,T=P===void 0?2:P,j=lb(o)?o:{},C=j.clipDot,$=C===void 0?!0:C,D=M*2+T;return A.createElement(ue,{className:w},O||m?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(_)},A.createElement("rect",{x:O?h:h-p/2,y:m?l:l-y/2,width:O?p:p*2,height:m?y:y*2})),!$&&A.createElement("clipPath",{id:"clipPath-dots-".concat(_)},A.createElement("rect",{x:h-D/2,y:l-D/2,width:p+D,height:y+D}))):null,!x&&this.renderCurve(g,_),this.renderErrorBar(g,_),(x||o)&&this.renderDots(g,$,_),(!v||b)&&mt.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(lr(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(lr(o),lr(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(A.isValidElement(n))a=A.cloneElement(n,i);else if(V(n))a=n(i);else{var o=i.key,u=Wg(i,Ck),c=Q("recharts-line-dot",typeof n!="boolean"?n.className:"");a=A.createElement(qa,vn({key:o},u,{className:c}))}return a}}])}(q.PureComponent);Je(Ga,"displayName","Line");Je(Ga,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!tr.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});Je(Ga,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(h,p){var y=Ce(h,o);return f==="horizontal"?{x:Gi({axis:r,ticks:i,bandSize:u,entry:h,index:p}),y:J(y)?null:n.scale(y),value:y,payload:h}:{x:J(y)?null:r.scale(y),y:Gi({axis:n,ticks:a,bandSize:u,entry:h,index:p}),value:y,payload:h}});return qe({points:l,layout:f},s)});var Hk=["layout","type","stroke","connectNulls","isRange","ref"],Gk=["key"],Jx;function qr(e){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qr(e)}function Qx(e,t){if(e==null)return{};var r=Kk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Kk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Gt(){return Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Gt.apply(this,arguments)}function Gg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function At(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gg(Object(r),!0).forEach(function(n){nt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Vk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Kg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tw(n.key),n)}}function Xk(e,t,r){return t&&Kg(e.prototype,t),r&&Kg(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Yk(e,t,r){return t=pa(t),Zk(e,ew()?Reflect.construct(t,r||[],pa(e).constructor):t.apply(e,r))}function Zk(e,t){if(t&&(qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Jk(e)}function Jk(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ew(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ew=function(){return!!e})()}function pa(e){return pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},pa(e)}function Qk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rf(e,t)}function rf(e,t){return rf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},rf(e,t)}function nt(e,t,r){return t=tw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tw(e){var t=eR(e,"string");return qr(t)=="symbol"?t:t+""}function eR(e,t){if(qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ar=function(e){function t(){var r;Vk(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Yk(this,t,[].concat(i)),nt(r,"state",{isAnimationFinished:!0}),nt(r,"id",Gr("recharts-area-")),nt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),V(o)&&o()}),nt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),V(o)&&o()}),r}return Qk(t,e),Xk(t,[{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive,u=this.state.isAnimationFinished;if(o&&!u)return null;var c=this.props,s=c.dot,f=c.points,l=c.dataKey,h=Y(this.props,!1),p=Y(s,!0),y=f.map(function(d,b){var x=At(At(At({key:"dot-".concat(b),r:3},h),p),{},{index:b,cx:d.x,cy:d.y,dataKey:l,value:d.value,payload:d.payload,points:f});return t.renderDotItem(s,x)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return A.createElement(ue,Gt({className:"recharts-area-dots"},v),y)}},{key:"renderHorizontalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].x,s=o[o.length-1].x,f=n*Math.abs(c-s),l=Tt(o.map(function(h){return h.y||0}));return N(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max(Tt(a.map(function(h){return h.y||0})),l)),N(l)?A.createElement("rect",{x:c<s?c:c-f,y:0,width:f,height:Math.floor(l+(u?parseInt("".concat(u),10):1))}):null}},{key:"renderVerticalRect",value:function(n){var i=this.props,a=i.baseLine,o=i.points,u=i.strokeWidth,c=o[0].y,s=o[o.length-1].y,f=n*Math.abs(c-s),l=Tt(o.map(function(h){return h.x||0}));return N(a)&&typeof a=="number"?l=Math.max(a,l):a&&Array.isArray(a)&&a.length&&(l=Math.max(Tt(a.map(function(h){return h.x||0})),l)),N(l)?A.createElement("rect",{x:0,y:c<s?c:c-f,width:l+(u?parseInt("".concat(u),10):1),height:Math.floor(f)}):null}},{key:"renderClipRect",value:function(n){var i=this.props.layout;return i==="vertical"?this.renderVerticalRect(n):this.renderHorizontalRect(n)}},{key:"renderAreaStatically",value:function(n,i,a,o){var u=this.props,c=u.layout,s=u.type,f=u.stroke,l=u.connectNulls,h=u.isRange;u.ref;var p=Qx(u,Hk);return A.createElement(ue,{clipPath:a?"url(#clipPath-".concat(o,")"):null},A.createElement(gr,Gt({},Y(p,!0),{points:n,connectNulls:l,type:s,baseLine:i,layout:c,stroke:"none",className:"recharts-area-area"})),f!=="none"&&A.createElement(gr,Gt({},Y(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:n})),f!=="none"&&h&&A.createElement(gr,Gt({},Y(this.props,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:l,fill:"none",points:i})))}},{key:"renderAreaWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.baseLine,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,h=o.animationEasing,p=o.animationId,y=this.state,v=y.prevPoints,d=y.prevBaseLine;return A.createElement(ut,{begin:f,duration:l,isActive:s,easing:h,from:{t:0},to:{t:1},key:"area-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(b){var x=b.t;if(v){var w=v.length/u.length,O=u.map(function(S,E){var M=Math.floor(E*w);if(v[M]){var P=v[M],T=Ae(P.x,S.x),j=Ae(P.y,S.y);return At(At({},S),{},{x:T(x),y:j(x)})}return S}),m;if(N(c)&&typeof c=="number"){var g=Ae(d,c);m=g(x)}else if(J(c)||Hr(c)){var _=Ae(d,0);m=_(x)}else m=c.map(function(S,E){var M=Math.floor(E*w);if(d[M]){var P=d[M],T=Ae(P.x,S.x),j=Ae(P.y,S.y);return At(At({},S),{},{x:T(x),y:j(x)})}return S});return a.renderAreaStatically(O,m,n,i)}return A.createElement(ue,null,A.createElement("defs",null,A.createElement("clipPath",{id:"animationClipPath-".concat(i)},a.renderClipRect(x))),A.createElement(ue,{clipPath:"url(#animationClipPath-".concat(i,")")},a.renderAreaStatically(u,c,n,i)))})}},{key:"renderArea",value:function(n,i){var a=this.props,o=a.points,u=a.baseLine,c=a.isAnimationActive,s=this.state,f=s.prevPoints,l=s.prevBaseLine,h=s.totalLength;return c&&o&&o.length&&(!f&&h>0||!Er(f,o)||!Er(l,u))?this.renderAreaWithAnimation(n,i):this.renderAreaStatically(o,u,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.top,f=i.left,l=i.xAxis,h=i.yAxis,p=i.width,y=i.height,v=i.isAnimationActive,d=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,x=u.length===1,w=Q("recharts-area",c),O=l&&l.allowDataOverflow,m=h&&h.allowDataOverflow,g=O||m,_=J(d)?this.id:d,S=(n=Y(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},E=S.r,M=E===void 0?3:E,P=S.strokeWidth,T=P===void 0?2:P,j=lb(o)?o:{},C=j.clipDot,$=C===void 0?!0:C,D=M*2+T;return A.createElement(ue,{className:w},O||m?A.createElement("defs",null,A.createElement("clipPath",{id:"clipPath-".concat(_)},A.createElement("rect",{x:O?f:f-p/2,y:m?s:s-y/2,width:O?p:p*2,height:m?y:y*2})),!$&&A.createElement("clipPath",{id:"clipPath-dots-".concat(_)},A.createElement("rect",{x:f-D/2,y:s-D/2,width:p+D,height:y+D}))):null,x?null:this.renderArea(g,_),(o||x)&&this.renderDots(g,$,_),(!v||b)&&mt.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,curBaseLine:n.baseLine,prevPoints:i.curPoints,prevBaseLine:i.curBaseLine}:n.points!==i.curPoints||n.baseLine!==i.curBaseLine?{curPoints:n.points,curBaseLine:n.baseLine}:null}}])}(q.PureComponent);Jx=ar;nt(ar,"displayName","Area");nt(ar,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!tr.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"});nt(ar,"getBaseValue",function(e,t,r,n){var i=e.layout,a=e.baseValue,o=t.props.baseValue,u=o!=null?o:a;if(N(u)&&typeof u=="number")return u;var c=i==="horizontal"?n:r,s=c.scale.domain();if(c.type==="number"){var f=Math.max(s[0],s[1]),l=Math.min(s[0],s[1]);return u==="dataMin"?l:u==="dataMax"||f<0?f:Math.max(Math.min(s[0],s[1]),0)}return u==="dataMin"?s[0]:u==="dataMax"?s[1]:s[0]});nt(ar,"getComposedData",function(e){var t=e.props,r=e.item,n=e.xAxis,i=e.yAxis,a=e.xAxisTicks,o=e.yAxisTicks,u=e.bandSize,c=e.dataKey,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,p=t.layout,y=s&&s.length,v=Jx.getBaseValue(t,r,n,i),d=p==="horizontal",b=!1,x=l.map(function(O,m){var g;y?g=s[f+m]:(g=Ce(O,c),Array.isArray(g)?b=!0:g=[v,g]);var _=g[1]==null||y&&Ce(O,c)==null;return d?{x:Gi({axis:n,ticks:a,bandSize:u,entry:O,index:m}),y:_?null:i.scale(g[1]),value:g,payload:O}:{x:_?null:n.scale(g[1]),y:Gi({axis:i,ticks:o,bandSize:u,entry:O,index:m}),value:g,payload:O}}),w;return y||b?w=x.map(function(O){var m=Array.isArray(O.value)?O.value[0]:null;return d?{x:O.x,y:m!=null&&O.y!=null?i.scale(m):null}:{x:m!=null?n.scale(m):null,y:O.y}}):w=d?i.scale(v):n.scale(v),At({points:x,baseLine:w,layout:p,isRange:b},h)});nt(ar,"renderDotItem",function(e,t){var r;if(A.isValidElement(e))r=A.cloneElement(e,t);else if(V(e))r=e(t);else{var n=Q("recharts-area-dot",typeof e!="boolean"?e.className:""),i=t.key,a=Qx(t,Gk);r=A.createElement(qa,Gt({},a,{key:i,className:n}))}return r});function Lr(e){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(e)}function tR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,iw(n.key),n)}}function nR(e,t,r){return t&&rR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function iR(e,t,r){return t=da(t),aR(e,rw()?Reflect.construct(t,r||[],da(e).constructor):t.apply(e,r))}function aR(e,t){if(t&&(Lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oR(e)}function oR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rw=function(){return!!e})()}function da(e){return da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},da(e)}function uR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nf(e,t)}function nf(e,t){return nf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},nf(e,t)}function nw(e,t,r){return t=iw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iw(e){var t=cR(e,"string");return Lr(t)=="symbol"?t:t+""}function cR(e,t){if(Lr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Lr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function af(){return af=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},af.apply(this,arguments)}function sR(e){var t=e.xAxisId,r=lh(),n=fh(),i=qx(t);return i==null?null:A.createElement(Zr,af({},i,{className:Q("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return pt(o,!0)}}))}var Ka=function(e){function t(){return tR(this,t),iR(this,t,arguments)}return uR(t,e),nR(t,[{key:"render",value:function(){return A.createElement(sR,this.props)}}])}(A.Component);nw(Ka,"displayName","XAxis");nw(Ka,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function lR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uw(n.key),n)}}function hR(e,t,r){return t&&fR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function pR(e,t,r){return t=va(t),dR(e,aw()?Reflect.construct(t,r||[],va(e).constructor):t.apply(e,r))}function dR(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return vR(e)}function vR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function aw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(aw=function(){return!!e})()}function va(e){return va=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},va(e)}function yR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&of(e,t)}function of(e,t){return of=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},of(e,t)}function ow(e,t,r){return t=uw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uw(e){var t=mR(e,"string");return Br(t)=="symbol"?t:t+""}function mR(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function uf(){return uf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},uf.apply(this,arguments)}var gR=function(t){var r=t.yAxisId,n=lh(),i=fh(),a=Lx(r);return a==null?null:A.createElement(Zr,uf({},a,{className:Q("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return pt(u,!0)}}))},Va=function(e){function t(){return lR(this,t),pR(this,t,arguments)}return yR(t,e),hR(t,[{key:"render",value:function(){return A.createElement(gR,this.props)}}])}(A.Component);ow(Va,"displayName","YAxis");ow(Va,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Vg(e){return OR(e)||wR(e)||xR(e)||bR()}function bR(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xR(e,t){if(e){if(typeof e=="string")return cf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cf(e,t)}}function wR(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function OR(e){if(Array.isArray(e))return cf(e)}function cf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var sf=function(t,r,n,i,a){var o=Ke(t,ph),u=Ke(t,Wa),c=[].concat(Vg(o),Vg(u)),s=Ke(t,Ha),f="".concat(i,"Id"),l=i[0],h=r;if(c.length&&(h=c.reduce(function(v,d){if(d.props[f]===n&&at(d.props,"extendDomain")&&N(d.props[l])){var b=d.props[l];return[Math.min(v[0],b),Math.max(v[1],b)]}return v},h)),s.length){var p="".concat(l,"1"),y="".concat(l,"2");h=s.reduce(function(v,d){if(d.props[f]===n&&at(d.props,"extendDomain")&&N(d.props[p])&&N(d.props[y])){var b=d.props[p],x=d.props[y];return[Math.min(v[0],b,x),Math.max(v[1],b,x)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,d){return N(d)?[Math.min(v[0],d),Math.max(v[1],d)]:v},h)),h},Us={exports:{}},Xg;function _R(){return Xg||(Xg=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var p=new i(f,l||c,h),y=r?r+s:s;return c._events[y]?c._events[y].fn?c._events[y]=[c._events[y],p]:c._events[y].push(p):(c._events[y]=p,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,p=l.length,y=new Array(p);h<p;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,p,y){var v=r?r+s:s;if(!this._events[v])return!1;var d=this._events[v],b=arguments.length,x,w;if(d.fn){switch(d.once&&this.removeListener(s,d.fn,void 0,!0),b){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,f),!0;case 3:return d.fn.call(d.context,f,l),!0;case 4:return d.fn.call(d.context,f,l,h),!0;case 5:return d.fn.call(d.context,f,l,h,p),!0;case 6:return d.fn.call(d.context,f,l,h,p,y),!0}for(w=1,x=new Array(b-1);w<b;w++)x[w-1]=arguments[w];d.fn.apply(d.context,x)}else{var O=d.length,m;for(w=0;w<O;w++)switch(d[w].once&&this.removeListener(s,d[w].fn,void 0,!0),b){case 1:d[w].fn.call(d[w].context);break;case 2:d[w].fn.call(d[w].context,f);break;case 3:d[w].fn.call(d[w].context,f,l);break;case 4:d[w].fn.call(d[w].context,f,l,h);break;default:if(!x)for(m=1,x=new Array(b-1);m<b;m++)x[m-1]=arguments[m];d[w].fn.apply(d[w].context,x)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var p=r?r+s:s;if(!this._events[p])return this;if(!f)return o(this,p),this;var y=this._events[p];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,p);else{for(var v=0,d=[],b=y.length;v<b;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&d.push(y[v]);d.length?this._events[p]=d.length===1?d[0]:d:o(this,p)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(Us)),Us.exports}var SR=_R();const AR=le(SR);var Hs=new AR,Gs="recharts.syncMouseEvents";function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function PR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ER(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cw(n.key),n)}}function TR(e,t,r){return t&&ER(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ks(e,t,r){return t=cw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cw(e){var t=jR(e,"string");return Xn(t)=="symbol"?t:t+""}function jR(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var MR=function(){function e(){PR(this,e),Ks(this,"activeIndex",0),Ks(this,"coordinateList",[]),Ks(this,"layout","horizontal")}return TR(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,p=h===void 0?null:h;this.coordinateList=(n=a!=null?a:this.coordinateList)!==null&&n!==void 0?n:[],this.container=u!=null?u:this.container,this.layout=s!=null?s:this.layout,this.offset=l!=null?l:this.offset,this.mouseHandlerCallback=p!=null?p:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function $R(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&N(n)&&N(i))return!0}return!1}function CR(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function sw(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Pe(t,r,n,i),u=Pe(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function IR(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=Pe(u,c,s,l),p=Pe(u,c,f,l);n=h.x,i=h.y,a=p.x,o=p.y}else return sw(t);return[{x:n,y:i},{x:a,y:o}]}function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}function Yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yg(Object(r),!0).forEach(function(n){DR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function DR(e,t,r){return t=kR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kR(e){var t=RR(e,"string");return Yn(t)=="symbol"?t:t+""}function RR(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function NR(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,p=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!p||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var y,v=gr;if(h==="ScatterChart")y=o,v=SC;else if(h==="BarChart")y=CR(l,o,c,f),v=oh;else if(l==="radial"){var d=sw(o),b=d.cx,x=d.cy,w=d.radius,O=d.startAngle,m=d.endAngle;y={cx:b,cy:x,startAngle:O,endAngle:m,innerRadius:w,outerRadius:w},v=px}else y={points:IR(l,o,c)},v=gr;var g=mi(mi(mi(mi({stroke:"#ccc",pointerEvents:"none"},c),y),Y(p,!1)),{},{payload:u,payloadIndex:s,className:Q("recharts-tooltip-cursor",p.className)});return q.isValidElement(p)?q.cloneElement(p,g):q.createElement(v,g)}var qR=["item"],LR=["children","className","width","height","style","compact","title","desc"];function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function pr(){return pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pr.apply(this,arguments)}function Zg(e,t){return zR(e)||FR(e,t)||fw(e,t)||BR()}function BR(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function FR(e,t){var r=e==null?null:typeof Symbol!="undefined"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function zR(e){if(Array.isArray(e))return e}function Jg(e,t){if(e==null)return{};var r=WR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function WR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function UR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function HR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hw(n.key),n)}}function GR(e,t,r){return t&&HR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function KR(e,t,r){return t=ya(t),VR(e,lw()?Reflect.construct(t,r||[],ya(e).constructor):t.apply(e,r))}function VR(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return XR(e)}function XR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function lw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lw=function(){return!!e})()}function ya(e){return ya=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ya(e)}function YR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lf(e,t)}function lf(e,t){return lf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},lf(e,t)}function zr(e){return QR(e)||JR(e)||fw(e)||ZR()}function ZR(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fw(e,t){if(e){if(typeof e=="string")return ff(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ff(e,t)}}function JR(e){if(typeof Symbol!="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function QR(e){if(Array.isArray(e))return ff(e)}function ff(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Qg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qg(Object(r),!0).forEach(function(n){H(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H(e,t,r){return t=hw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hw(e){var t=e2(e,"string");return Fr(t)=="symbol"?t:t+""}function e2(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var t2={xAxis:["bottom","top"],yAxis:["left","right"]},r2={width:"100%",height:"100%"},pw={x:0,y:0};function gi(e){return e}var n2=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},i2=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return I(I(I({},i),Pe(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return I(I(I({},i),Pe(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return pw},Xa=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n!=null?n:[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(zr(u),zr(s)):u},[]);return o.length>0?o:t&&t.length&&N(i)&&N(a)?t.slice(i,a+1):[]};function dw(e){return e==="number"?[0,"auto"]:void 0}var hf=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Xa(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var p=l===void 0?u:l;h=bi(p,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(zr(c),[lx(s,h)]):c},[])},eb=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=n2(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=fj(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=hf(t,r,f,l),p=i2(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:p}}return null},a2=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,p=ux(f,a);return n.reduce(function(y,v){var d,b=v.type.defaultProps!==void 0?I(I({},v.type.defaultProps),v.props):v.props,x=b.type,w=b.dataKey,O=b.allowDataOverflow,m=b.allowDuplicatedCategory,g=b.scale,_=b.ticks,S=b.includeHidden,E=b[o];if(y[E])return y;var M=Xa(t.data,{graphicalItems:i.filter(function(z){var K,ce=o in z.props?z.props[o]:(K=z.type.defaultProps)===null||K===void 0?void 0:K[o];return ce===E}),dataStartIndex:c,dataEndIndex:s}),P=M.length,T,j,C;$R(b.domain,O,x)&&(T=Tl(b.domain,null,O),p&&(x==="number"||g!=="auto")&&(C=pn(M,w,"category")));var $=dw(x);if(!T||T.length===0){var D,k=(D=b.domain)!==null&&D!==void 0?D:$;if(w){if(T=pn(M,w,x),x==="category"&&p){var L=nO(T);m&&L?(j=T,T=ra(0,P)):m||(T=hm(k,T,v).reduce(function(z,K){return z.indexOf(K)>=0?z:[].concat(zr(z),[K])},[]))}else if(x==="category")m?T=T.filter(function(z){return z!==""&&!J(z)}):T=hm(k,T,v).reduce(function(z,K){return z.indexOf(K)>=0||K===""||J(K)?z:[].concat(zr(z),[K])},[]);else if(x==="number"){var B=yj(M,i.filter(function(z){var K,ce,de=o in z.props?z.props[o]:(K=z.type.defaultProps)===null||K===void 0?void 0:K[o],Ne="hide"in z.props?z.props.hide:(ce=z.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return de===E&&(S||!Ne)}),w,a,f);B&&(T=B)}p&&(x==="number"||g!=="auto")&&(C=pn(M,w,"category"))}else p?T=ra(0,P):u&&u[E]&&u[E].hasStack&&x==="number"?T=h==="expand"?[0,1]:sx(u[E].stackGroups,c,s):T=ox(M,i.filter(function(z){var K=o in z.props?z.props[o]:z.type.defaultProps[o],ce="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return K===E&&(S||!ce)}),x,f,!0);if(x==="number")T=sf(l,T,E,a,_),k&&(T=Tl(k,T,O));else if(x==="category"&&k){var U=k,G=T.every(function(z){return U.indexOf(z)>=0});G&&(T=U)}}return I(I({},y),{},H({},E,I(I({},b),{},{axisType:a,domain:T,categoricalDomain:C,duplicateDomain:j,originalDomain:(d=b.domain)!==null&&d!==void 0?d:$,isCategorical:p,layout:f})))},{})},o2=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=Xa(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),p=h.length,y=ux(f,a),v=-1;return n.reduce(function(d,b){var x=b.type.defaultProps!==void 0?I(I({},b.type.defaultProps),b.props):b.props,w=x[o],O=dw("number");if(!d[w]){v++;var m;return y?m=ra(0,p):u&&u[w]&&u[w].hasStack?(m=sx(u[w].stackGroups,c,s),m=sf(l,m,w,a)):(m=Tl(O,ox(h,n.filter(function(g){var _,S,E=o in g.props?g.props[o]:(_=g.type.defaultProps)===null||_===void 0?void 0:_[o],M="hide"in g.props?g.props.hide:(S=g.type.defaultProps)===null||S===void 0?void 0:S.hide;return E===w&&!M}),"number",f),i.defaultProps.allowDataOverflow),m=sf(l,m,w,a)),I(I({},d),{},H({},w,I(I({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ge(t2,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:O,isCategorical:y,layout:f})))}return d},{})},u2=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=Ke(f,a),p={};return h&&h.length?p=a2(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(p=o2(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),p},c2=function(t){var r=Et(t),n=pt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:If(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Ki(r,n)}},tb=function(t){var r=t.children,n=t.defaultShowTooltip,i=Be(r,$r),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},s2=function(t){return!t||!t.length?!1:t.some(function(r){var n=dt(r&&r.type);return n&&n.indexOf("Bar")>=0})},rb=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},l2=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},p=Be(l,$r),y=Be(l,vr),v=Object.keys(c).reduce(function(m,g){var _=c[g],S=_.orientation;return!_.mirror&&!_.hide?I(I({},m),{},H({},S,m[S]+_.width)):m},{left:h.left||0,right:h.right||0}),d=Object.keys(o).reduce(function(m,g){var _=o[g],S=_.orientation;return!_.mirror&&!_.hide?I(I({},m),{},H({},S,Ge(m,"".concat(S))+_.height)):m},{top:h.top||0,bottom:h.bottom||0}),b=I(I({},d),v),x=b.bottom;p&&(b.bottom+=p.props.height||$r.defaultProps.height),y&&r&&(b=dj(b,i,n,r));var w=s-b.left-b.right,O=f-b.top-b.bottom;return I(I({brushBottom:x},b),{},{width:Math.max(w,0),height:Math.max(O,0)})},f2=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},vw=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(b,x){var w=x.graphicalItems,O=x.stackGroups,m=x.offset,g=x.updateId,_=x.dataStartIndex,S=x.dataEndIndex,E=b.barSize,M=b.layout,P=b.barGap,T=b.barCategoryGap,j=b.maxBarSize,C=rb(M),$=C.numericAxisName,D=C.cateAxisName,k=s2(w),L=[];return w.forEach(function(B,U){var G=Xa(b.data,{graphicalItems:[B],dataStartIndex:_,dataEndIndex:S}),z=B.type.defaultProps!==void 0?I(I({},B.type.defaultProps),B.props):B.props,K=z.dataKey,ce=z.maxBarSize,de=z["".concat($,"Id")],Ne=z["".concat(D,"Id")],Rt={},Ie=c.reduce(function(Nt,qt){var Ya=x["".concat(qt.axisType,"Map")],yh=z["".concat(qt.axisType,"Id")];Ya&&Ya[yh]||qt.axisType==="zAxis"||Jt();var mh=Ya[yh];return I(I({},Nt),{},H(H({},qt.axisType,mh),"".concat(qt.axisType,"Ticks"),pt(mh)))},Rt),F=Ie[D],X=Ie["".concat(D,"Ticks")],Z=O&&O[de]&&O[de].hasStack&&Tj(B,O[de].stackGroups),R=dt(B.type).indexOf("Bar")>=0,he=Ki(F,X),ee=[],me=k&&hj({barSize:E,stackGroups:O,totalSize:f2(Ie,D)});if(R){var ge,De,St=J(ce)?j:ce,or=(ge=(De=Ki(F,X,!0))!==null&&De!==void 0?De:St)!==null&&ge!==void 0?ge:0;ee=pj({barGap:P,barCategoryGap:T,bandSize:or!==he?or:he,sizeList:me[Ne],maxBarSize:St}),or!==he&&(ee=ee.map(function(Nt){return I(I({},Nt),{},{position:I(I({},Nt.position),{},{offset:Nt.position.offset-or/2})})}))}var ai=B&&B.type&&B.type.getComposedData;ai&&L.push({props:I(I({},ai(I(I({},Ie),{},{displayedData:G,props:b,dataKey:K,item:B,bandSize:he,barPosition:ee,offset:m,stackedData:Z,layout:M,dataStartIndex:_,dataEndIndex:S}))),{},H(H(H({key:B.key||"item-".concat(U)},$,Ie[$]),D,Ie[D]),"animationId",g)),childIndex:vO(B,b.children),item:B})}),L},p=function(b,x){var w=b.props,O=b.dataStartIndex,m=b.dataEndIndex,g=b.updateId;if(!Ep({props:w}))return null;var _=w.children,S=w.layout,E=w.stackOffset,M=w.data,P=w.reverseStackOrder,T=rb(S),j=T.numericAxisName,C=T.cateAxisName,$=Ke(_,n),D=Aj(M,$,"".concat(j,"Id"),"".concat(C,"Id"),E,P),k=c.reduce(function(z,K){var ce="".concat(K.axisType,"Map");return I(I({},z),{},H({},ce,u2(w,I(I({},K),{},{graphicalItems:$,stackGroups:K.axisType===j&&D,dataStartIndex:O,dataEndIndex:m}))))},{}),L=l2(I(I({},k),{},{props:w,graphicalItems:$}),x==null?void 0:x.legendBBox);Object.keys(k).forEach(function(z){k[z]=f(w,k[z],L,z.replace("Map",""),r)});var B=k["".concat(C,"Map")],U=c2(B),G=h(w,I(I({},k),{},{dataStartIndex:O,dataEndIndex:m,updateId:g,graphicalItems:$,stackGroups:D,offset:L}));return I(I({formattedGraphicalItems:G,graphicalItems:$,offset:L,stackGroups:D},U),k)},y=function(d){function b(x){var w,O,m;return UR(this,b),m=KR(this,b,[x]),H(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),H(m,"accessibilityManager",new MR),H(m,"handleLegendBBoxUpdate",function(g){if(g){var _=m.state,S=_.dataStartIndex,E=_.dataEndIndex,M=_.updateId;m.setState(I({legendBBox:g},p({props:m.props,dataStartIndex:S,dataEndIndex:E,updateId:M},I(I({},m.state),{},{legendBBox:g}))))}}),H(m,"handleReceiveSyncEvent",function(g,_,S){if(m.props.syncId===g){if(S===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(_)}}),H(m,"handleBrushChange",function(g){var _=g.startIndex,S=g.endIndex;if(_!==m.state.dataStartIndex||S!==m.state.dataEndIndex){var E=m.state.updateId;m.setState(function(){return I({dataStartIndex:_,dataEndIndex:S},p({props:m.props,dataStartIndex:_,dataEndIndex:S,updateId:E},m.state))}),m.triggerSyncEvent({dataStartIndex:_,dataEndIndex:S})}}),H(m,"handleMouseEnter",function(g){var _=m.getMouseInfo(g);if(_){var S=I(I({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var E=m.props.onMouseEnter;V(E)&&E(S,g)}}),H(m,"triggeredAfterMouseMove",function(g){var _=m.getMouseInfo(g),S=_?I(I({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(S),m.triggerSyncEvent(S);var E=m.props.onMouseMove;V(E)&&E(S,g)}),H(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),H(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),H(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),H(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};m.setState(_),m.triggerSyncEvent(_);var S=m.props.onMouseLeave;V(S)&&S(_,g)}),H(m,"handleOuterEvent",function(g){var _=dO(g),S=Ge(m.props,"".concat(_));if(_&&V(S)){var E,M;/.*touch.*/i.test(_)?M=m.getMouseInfo(g.changedTouches[0]):M=m.getMouseInfo(g),S((E=M)!==null&&E!==void 0?E:{},g)}}),H(m,"handleClick",function(g){var _=m.getMouseInfo(g);if(_){var S=I(I({},_),{},{isTooltipActive:!0});m.setState(S),m.triggerSyncEvent(S);var E=m.props.onClick;V(E)&&E(S,g)}}),H(m,"handleMouseDown",function(g){var _=m.props.onMouseDown;if(V(_)){var S=m.getMouseInfo(g);_(S,g)}}),H(m,"handleMouseUp",function(g){var _=m.props.onMouseUp;if(V(_)){var S=m.getMouseInfo(g);_(S,g)}}),H(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),H(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),H(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),H(m,"handleDoubleClick",function(g){var _=m.props.onDoubleClick;if(V(_)){var S=m.getMouseInfo(g);_(S,g)}}),H(m,"handleContextMenu",function(g){var _=m.props.onContextMenu;if(V(_)){var S=m.getMouseInfo(g);_(S,g)}}),H(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&Hs.emit(Gs,m.props.syncId,g,m.eventEmitterSymbol)}),H(m,"applySyncEvent",function(g){var _=m.props,S=_.layout,E=_.syncMethod,M=m.state.updateId,P=g.dataStartIndex,T=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState(I({dataStartIndex:P,dataEndIndex:T},p({props:m.props,dataStartIndex:P,dataEndIndex:T,updateId:M},m.state)));else if(g.activeTooltipIndex!==void 0){var j=g.chartX,C=g.chartY,$=g.activeTooltipIndex,D=m.state,k=D.offset,L=D.tooltipTicks;if(!k)return;if(typeof E=="function")$=E(L,g);else if(E==="value"){$=-1;for(var B=0;B<L.length;B++)if(L[B].value===g.activeLabel){$=B;break}}var U=I(I({},k),{},{x:k.left,y:k.top}),G=Math.min(j,U.x+U.width),z=Math.min(C,U.y+U.height),K=L[$]&&L[$].value,ce=hf(m.state,m.props.data,$),de=L[$]?{x:S==="horizontal"?L[$].coordinate:G,y:S==="horizontal"?z:L[$].coordinate}:pw;m.setState(I(I({},g),{},{activeLabel:K,activeCoordinate:de,activePayload:ce,activeTooltipIndex:$}))}else m.setState(g)}),H(m,"renderCursor",function(g){var _,S=m.state,E=S.isTooltipActive,M=S.activeCoordinate,P=S.activePayload,T=S.offset,j=S.activeTooltipIndex,C=S.tooltipAxisBandSize,$=m.getTooltipEventType(),D=(_=g.props.active)!==null&&_!==void 0?_:E,k=m.props.layout,L=g.key||"_recharts-cursor";return A.createElement(NR,{key:L,activeCoordinate:M,activePayload:P,activeTooltipIndex:j,chartName:r,element:g,isActive:D,layout:k,offset:T,tooltipAxisBandSize:C,tooltipEventType:$})}),H(m,"renderPolarAxis",function(g,_,S){var E=Ge(g,"type.axisType"),M=Ge(m.state,"".concat(E,"Map")),P=g.type.defaultProps,T=P!==void 0?I(I({},P),g.props):g.props,j=M&&M[T["".concat(E,"Id")]];return q.cloneElement(g,I(I({},j),{},{className:Q(E,j.className),key:g.key||"".concat(_,"-").concat(S),ticks:pt(j,!0)}))}),H(m,"renderPolarGrid",function(g){var _=g.props,S=_.radialLines,E=_.polarAngles,M=_.polarRadius,P=m.state,T=P.radiusAxisMap,j=P.angleAxisMap,C=Et(T),$=Et(j),D=$.cx,k=$.cy,L=$.innerRadius,B=$.outerRadius;return q.cloneElement(g,{polarAngles:Array.isArray(E)?E:pt($,!0).map(function(U){return U.coordinate}),polarRadius:Array.isArray(M)?M:pt(C,!0).map(function(U){return U.coordinate}),cx:D,cy:k,innerRadius:L,outerRadius:B,key:g.key||"polar-grid",radialLines:S})}),H(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,_=m.props,S=_.children,E=_.width,M=_.height,P=m.props.margin||{},T=E-(P.left||0)-(P.right||0),j=ix({children:S,formattedGraphicalItems:g,legendWidth:T,legendContent:s});if(!j)return null;var C=j.item,$=Jg(j,qR);return q.cloneElement(C,I(I({},$),{},{chartWidth:E,chartHeight:M,margin:P,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),H(m,"renderTooltip",function(){var g,_=m.props,S=_.children,E=_.accessibilityLayer,M=Be(S,lt);if(!M)return null;var P=m.state,T=P.isTooltipActive,j=P.activeCoordinate,C=P.activePayload,$=P.activeLabel,D=P.offset,k=(g=M.props.active)!==null&&g!==void 0?g:T;return q.cloneElement(M,{viewBox:I(I({},D),{},{x:D.left,y:D.top}),active:k,label:$,payload:k?C:[],coordinate:j,accessibilityLayer:E})}),H(m,"renderBrush",function(g){var _=m.props,S=_.margin,E=_.data,M=m.state,P=M.offset,T=M.dataStartIndex,j=M.dataEndIndex,C=M.updateId;return q.cloneElement(g,{key:g.key||"_recharts-brush",onChange:pi(m.handleBrushChange,g.props.onChange),data:E,x:N(g.props.x)?g.props.x:P.left,y:N(g.props.y)?g.props.y:P.top+P.height+P.brushBottom-(S.bottom||0),width:N(g.props.width)?g.props.width:P.width,startIndex:T,endIndex:j,updateId:"brush-".concat(C)})}),H(m,"renderReferenceElement",function(g,_,S){if(!g)return null;var E=m,M=E.clipPathId,P=m.state,T=P.xAxisMap,j=P.yAxisMap,C=P.offset,$=g.type.defaultProps||{},D=g.props,k=D.xAxisId,L=k===void 0?$.xAxisId:k,B=D.yAxisId,U=B===void 0?$.yAxisId:B;return q.cloneElement(g,{key:g.key||"".concat(_,"-").concat(S),xAxis:T[L],yAxis:j[U],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:M})}),H(m,"renderActivePoints",function(g){var _=g.item,S=g.activePoint,E=g.basePoint,M=g.childIndex,P=g.isRange,T=[],j=_.props.key,C=_.item.type.defaultProps!==void 0?I(I({},_.item.type.defaultProps),_.item.props):_.item.props,$=C.activeDot,D=C.dataKey,k=I(I({index:M,dataKey:D,cx:S.x,cy:S.y,r:4,fill:ah(_.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},Y($,!1)),xi($));return T.push(b.renderActiveDot($,k,"".concat(j,"-activePoint-").concat(M))),E?T.push(b.renderActiveDot($,I(I({},k),{},{cx:E.x,cy:E.y}),"".concat(j,"-basePoint-").concat(M))):P&&T.push(null),T}),H(m,"renderGraphicChild",function(g,_,S){var E=m.filterFormatItem(g,_,S);if(!E)return null;var M=m.getTooltipEventType(),P=m.state,T=P.isTooltipActive,j=P.tooltipAxis,C=P.activeTooltipIndex,$=P.activeLabel,D=m.props.children,k=Be(D,lt),L=E.props,B=L.points,U=L.isRange,G=L.baseLine,z=E.item.type.defaultProps!==void 0?I(I({},E.item.type.defaultProps),E.item.props):E.item.props,K=z.activeDot,ce=z.hide,de=z.activeBar,Ne=z.activeShape,Rt=!!(!ce&&T&&k&&(K||de||Ne)),Ie={};M!=="axis"&&k&&k.props.trigger==="click"?Ie={onClick:pi(m.handleItemMouseEnter,g.props.onClick)}:M!=="axis"&&(Ie={onMouseLeave:pi(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:pi(m.handleItemMouseEnter,g.props.onMouseEnter)});var F=q.cloneElement(g,I(I({},E.props),Ie));function X(qt){return typeof j.dataKey=="function"?j.dataKey(qt.payload):null}if(Rt)if(C>=0){var Z,R;if(j.dataKey&&!j.allowDuplicatedCategory){var he=typeof j.dataKey=="function"?X:"payload.".concat(j.dataKey.toString());Z=bi(B,he,$),R=U&&G&&bi(G,he,$)}else Z=B==null?void 0:B[C],R=U&&G&&G[C];if(Ne||de){var ee=g.props.activeIndex!==void 0?g.props.activeIndex:C;return[q.cloneElement(g,I(I(I({},E.props),Ie),{},{activeIndex:ee})),null,null]}if(!J(Z))return[F].concat(zr(m.renderActivePoints({item:E,activePoint:Z,basePoint:R,childIndex:C,isRange:U})))}else{var me,ge=(me=m.getItemByXY(m.state.activeCoordinate))!==null&&me!==void 0?me:{graphicalItem:F},De=ge.graphicalItem,St=De.item,or=St===void 0?g:St,ai=De.childIndex,Nt=I(I(I({},E.props),Ie),{},{activeIndex:ai});return[q.cloneElement(or,Nt),null,null]}return U?[F,null,null]:[F,null]}),H(m,"renderCustomized",function(g,_,S){return q.cloneElement(g,I(I({key:"recharts-customized-".concat(S)},m.props),m.state))}),H(m,"renderMap",{CartesianGrid:{handler:gi,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:gi},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:gi},YAxis:{handler:gi},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((w=x.id)!==null&&w!==void 0?w:Gr("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=n0(m.triggeredAfterMouseMove,(O=x.throttleDelay)!==null&&O!==void 0?O:1e3/60),m.state={},m}return YR(b,d),GR(b,[{key:"componentDidMount",value:function(){var w,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(w=this.props.margin.left)!==null&&w!==void 0?w:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var w=this.props,O=w.children,m=w.data,g=w.height,_=w.layout,S=Be(O,lt);if(S){var E=S.props.defaultIndex;if(!(typeof E!="number"||E<0||E>this.state.tooltipTicks.length-1)){var M=this.state.tooltipTicks[E]&&this.state.tooltipTicks[E].value,P=hf(this.state,m,E,M),T=this.state.tooltipTicks[E].coordinate,j=(this.state.offset.top+g)/2,C=_==="horizontal",$=C?{x:T,y:j}:{y:T,x:j},D=this.state.formattedGraphicalItems.find(function(L){var B=L.item;return B.type.name==="Scatter"});D&&($=I(I({},$),D.props.points[E].tooltipPosition),P=D.props.points[E].tooltipPayload);var k={activeTooltipIndex:E,isTooltipActive:!0,activeLabel:M,activePayload:P,activeCoordinate:$};this.setState(k),this.renderCursor(S),this.accessibilityManager.setIndex(E)}}}},{key:"getSnapshotBeforeUpdate",value:function(w,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==w.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==w.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(w){Ys([Be(w.children,lt)],[Be(this.props.children,lt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var w=Be(this.props.children,lt);if(w&&typeof w.props.shared=="boolean"){var O=w.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(w){if(!this.container)return null;var O=this.container,m=O.getBoundingClientRect(),g=tA(m),_={chartX:Math.round(w.pageX-g.left),chartY:Math.round(w.pageY-g.top)},S=m.width/O.offsetWidth||1,E=this.inRange(_.chartX,_.chartY,S);if(!E)return null;var M=this.state,P=M.xAxisMap,T=M.yAxisMap,j=this.getTooltipEventType(),C=eb(this.state,this.props.data,this.props.layout,E);if(j!=="axis"&&P&&T){var $=Et(P).scale,D=Et(T).scale,k=$&&$.invert?$.invert(_.chartX):null,L=D&&D.invert?D.invert(_.chartY):null;return I(I({},_),{},{xValue:k,yValue:L},C)}return C?I(I({},_),C):null}},{key:"inRange",value:function(w,O){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,_=w/m,S=O/m;if(g==="horizontal"||g==="vertical"){var E=this.state.offset,M=_>=E.left&&_<=E.left+E.width&&S>=E.top&&S<=E.top+E.height;return M?{x:_,y:S}:null}var P=this.state,T=P.angleAxisMap,j=P.radiusAxisMap;if(T&&j){var C=Et(T);return vm({x:_,y:S},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var w=this.props.children,O=this.getTooltipEventType(),m=Be(w,lt),g={};m&&O==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=xi(this.props,this.handleOuterEvent);return I(I({},_),g)}},{key:"addListener",value:function(){Hs.on(Gs,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Hs.removeListener(Gs,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(w,O,m){for(var g=this.state.formattedGraphicalItems,_=0,S=g.length;_<S;_++){var E=g[_];if(E.item===w||E.props.key===w.key||O===dt(E.item.type)&&m===E.childIndex)return E}return null}},{key:"renderClipPath",value:function(){var w=this.clipPathId,O=this.state.offset,m=O.left,g=O.top,_=O.height,S=O.width;return A.createElement("defs",null,A.createElement("clipPath",{id:w},A.createElement("rect",{x:m,y:g,height:_,width:S})))}},{key:"getXScales",value:function(){var w=this.state.xAxisMap;return w?Object.entries(w).reduce(function(O,m){var g=Zg(m,2),_=g[0],S=g[1];return I(I({},O),{},H({},_,S.scale))},{}):null}},{key:"getYScales",value:function(){var w=this.state.yAxisMap;return w?Object.entries(w).reduce(function(O,m){var g=Zg(m,2),_=g[0],S=g[1];return I(I({},O),{},H({},_,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(w){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(w){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(w){var O=this.state,m=O.formattedGraphicalItems,g=O.activeItem;if(m&&m.length)for(var _=0,S=m.length;_<S;_++){var E=m[_],M=E.props,P=E.item,T=P.type.defaultProps!==void 0?I(I({},P.type.defaultProps),P.props):P.props,j=dt(P.type);if(j==="Bar"){var C=(M.data||[]).find(function(L){return dC(w,L)});if(C)return{graphicalItem:E,payload:C}}else if(j==="RadialBar"){var $=(M.data||[]).find(function(L){return vm(w,L)});if($)return{graphicalItem:E,payload:$}}else if(La(E,g)||Ba(E,g)||Un(E,g)){var D=iI({graphicalItem:E,activeTooltipItem:g,itemData:T.data}),k=T.activeIndex===void 0?D:T.activeIndex;return{graphicalItem:I(I({},E),{},{childIndex:k}),payload:Un(E,g)?T.data[D]:E.props.data[D]}}}return null}},{key:"render",value:function(){var w=this;if(!Ep(this))return null;var O=this.props,m=O.children,g=O.className,_=O.width,S=O.height,E=O.style,M=O.compact,P=O.title,T=O.desc,j=Jg(O,LR),C=Y(j,!1);if(M)return A.createElement(Mg,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement(Js,pr({},C,{width:_,height:S,title:P,desc:T}),this.renderClipPath(),jp(m,this.renderMap)));if(this.props.accessibilityLayer){var $,D;C.tabIndex=($=this.props.tabIndex)!==null&&$!==void 0?$:0,C.role=(D=this.props.role)!==null&&D!==void 0?D:"application",C.onKeyDown=function(L){w.accessibilityManager.keyboardEvent(L)},C.onFocus=function(){w.accessibilityManager.focus()}}var k=this.parseEventsOfWrapper();return A.createElement(Mg,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},A.createElement("div",pr({className:Q("recharts-wrapper",g),style:I({position:"relative",cursor:"default",width:_,height:S},E)},k,{ref:function(B){w.container=B}}),A.createElement(Js,pr({},C,{width:_,height:S,title:P,desc:T,style:r2}),this.renderClipPath(),jp(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);H(y,"displayName",r),H(y,"defaultProps",I({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),H(y,"getDerivedStateFromProps",function(d,b){var x=d.dataKey,w=d.data,O=d.children,m=d.width,g=d.height,_=d.layout,S=d.stackOffset,E=d.margin,M=b.dataStartIndex,P=b.dataEndIndex;if(b.updateId===void 0){var T=tb(d);return I(I(I({},T),{},{updateId:0},p(I(I({props:d},T),{},{updateId:0}),b)),{},{prevDataKey:x,prevData:w,prevWidth:m,prevHeight:g,prevLayout:_,prevStackOffset:S,prevMargin:E,prevChildren:O})}if(x!==b.prevDataKey||w!==b.prevData||m!==b.prevWidth||g!==b.prevHeight||_!==b.prevLayout||S!==b.prevStackOffset||!dr(E,b.prevMargin)){var j=tb(d),C={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},$=I(I({},eb(b,w,_)),{},{updateId:b.updateId+1}),D=I(I(I({},j),C),$);return I(I(I({},D),p(I({props:d},D),b)),{},{prevDataKey:x,prevData:w,prevWidth:m,prevHeight:g,prevLayout:_,prevStackOffset:S,prevMargin:E,prevChildren:O})}if(!Ys(O,b.prevChildren)){var k,L,B,U,G=Be(O,$r),z=G&&(k=(L=G.props)===null||L===void 0?void 0:L.startIndex)!==null&&k!==void 0?k:M,K=G&&(B=(U=G.props)===null||U===void 0?void 0:U.endIndex)!==null&&B!==void 0?B:P,ce=z!==M||K!==P,de=!J(w),Ne=de&&!ce?b.updateId:b.updateId+1;return I(I({updateId:Ne},p(I(I({props:d},b),{},{updateId:Ne,dataStartIndex:z,dataEndIndex:K}),b)),{},{prevChildren:O,dataStartIndex:z,dataEndIndex:K})}return null}),H(y,"renderActiveDot",function(d,b,x){var w;return q.isValidElement(d)?w=q.cloneElement(d,b):V(d)?w=d(b):w=A.createElement(qa,b),A.createElement(ue,{className:"recharts-active-dot",key:x},w)});var v=q.forwardRef(function(b,x){return A.createElement(y,pr({},b,{ref:x}))});return v.displayName=y.displayName,v},g2=vw({chartName:"LineChart",GraphicalChild:Ga,axisComponents:[{axisType:"xAxis",AxisComp:Ka},{axisType:"yAxis",AxisComp:Va}],formatAxisMap:Mx}),b2=vw({chartName:"AreaChart",GraphicalChild:ar,axisComponents:[{axisType:"xAxis",AxisComp:Ka},{axisType:"yAxis",AxisComp:Va}],formatAxisMap:Mx});export{ut as A,ii as B,gr as C,ni as E,tr as G,ue as L,m2 as R,ZC as S,lt as T,Ka as X,Va as Y,Af as a,wi as b,Er as c,Ke as d,y2 as e,Y as f,Ce as g,V as h,Ae as i,J as j,mt as k,i0 as l,Gi as m,vw as n,Ga as o,ar as p,Mx as q,b2 as r,Mk as s,vr as t,Gr as u,g2 as v,ph as w};
