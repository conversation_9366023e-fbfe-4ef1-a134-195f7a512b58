const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/EnhancedRevenueDashboard-8SMCrQQt.js","js/chunk-DX4Z_LyS.js","js/chunk-Cai8ouo_.js","assets/main-CGUKzV0x.js","js/chunk-BV1TipCO.js","js/chunk-D0ItFSwi.js","js/chunk-D8IZ3rty.js","js/chunk-BiNxGM8y.js","css/main-BKSiHMgh.css","js/RevenueAnalytics-DPG4P7QI.js","js/RevenueSettings-uH8xhc7E.js","js/RevenueDistribution-CQutXqZv.js"])))=>i.map(i=>d[i]);
import{r as x,j as e,m as s,h as n,c as a,b as t,S as v,T as r,a as c,_ as d}from"./chunk-DX4Z_LyS.js";import{U as f}from"../assets/main-CGUKzV0x.js";import{Q as j,D as _,aB as o,O as p,z as w,J as z,e as E}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";const P=c.lazy(()=>d(()=>import("./EnhancedRevenueDashboard-8SMCrQQt.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),T=c.lazy(()=>d(()=>import("./RevenueAnalytics-DPG4P7QI.js"),__vite__mapDeps([9,1,2,3,4,5,6,7,8]))),R=c.lazy(()=>d(()=>import("./RevenueSettings-uH8xhc7E.js"),__vite__mapDeps([10,1,2,3,4,5,6,7,8])));c.lazy(()=>d(()=>import("../assets/main-CGUKzV0x.js").then(u=>u.R),__vite__mapDeps([3,1,2,4,5,6,7,8])));c.lazy(()=>d(()=>import("./RevenueDistribution-CQutXqZv.js"),__vite__mapDeps([11,1,2,3,4,5,6,7,8])));const V=()=>{const{currentUser:u}=x.useContext(f),[i,m]=x.useState("dashboard"),[g,y]=x.useState("6m"),N=({message:h="Loading..."})=>e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:h})]})}),b=()=>{},l=h=>{switch(h){case"withdraw":break;case"goals":m("settings");break;case"analytics":m("analytics");break}};return e.jsx("div",{className:"enhanced-revenue-page min-h-screen bg-gray-50 dark:bg-gray-900",children:e.jsxs("div",{className:"container mx-auto px-4 py-6 max-w-7xl",children:[e.jsxs(s.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-4xl font-bold flex items-center gap-3",children:[e.jsx(j,{className:"text-green-500",size:40}),"Treasury"]}),e.jsx("p",{className:"text-gray-600 text-lg mt-2",children:"Comprehensive revenue management and financial insights"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(n,{variant:"light",startContent:e.jsx(_,{size:18}),onPress:b,children:"Export Data"}),e.jsx(n,{color:"primary",startContent:e.jsx(o,{size:18}),onPress:()=>l("withdraw"),children:"Withdraw Funds"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1},children:e.jsx(a,{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white",children:e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-green-100 text-sm",children:"Total Earnings"}),e.jsx("p",{className:"text-2xl font-bold",children:"$12,450"})]}),e.jsx(j,{size:24,className:"text-green-200"})]})})})}),e.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.2},children:e.jsx(a,{className:"bg-gradient-to-r from-blue-500 to-cyan-600 text-white",children:e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-blue-100 text-sm",children:"This Month"}),e.jsx("p",{className:"text-2xl font-bold",children:"$3,200"})]}),e.jsx(p,{size:24,className:"text-blue-200"})]})})})}),e.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.3},children:e.jsx(a,{className:"bg-gradient-to-r from-purple-500 to-indigo-600 text-white",children:e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-purple-100 text-sm",children:"ORB Balance"}),e.jsx("p",{className:"text-2xl font-bold",children:"15,420"})]}),e.jsx(o,{size:24,className:"text-purple-200"})]})})})}),e.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.4},children:e.jsx(a,{className:"bg-gradient-to-r from-orange-500 to-red-600 text-white",children:e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-orange-100 text-sm",children:"Goal Progress"}),e.jsx("p",{className:"text-2xl font-bold",children:"80%"})]}),e.jsx(w,{size:24,className:"text-orange-200"})]})})})})]})]}),e.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:e.jsx(a,{className:"mb-6",children:e.jsx(t,{className:"p-0",children:e.jsxs(v,{selectedKey:i,onSelectionChange:m,variant:"underlined",size:"lg",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-primary",tab:"max-w-fit px-6 py-4 h-16",tabContent:"group-data-[selected=true]:text-primary text-lg"},children:[e.jsx(r,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(j,{size:20}),e.jsx("span",{children:"Dashboard"})]})},"dashboard"),e.jsx(r,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{size:20}),e.jsx("span",{children:"Analytics"})]})},"analytics"),e.jsx(r,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(p,{size:20}),e.jsx("span",{children:"Tracker"})]})},"tracker"),e.jsx(r,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{size:20}),e.jsx("span",{children:"Distribution"})]})},"distribution"),e.jsx(r,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(E,{size:20}),e.jsx("span",{children:"Settings"})]})},"settings")]})})})}),e.jsx(s.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:e.jsxs(x.Suspense,{fallback:e.jsx(N,{message:"Loading revenue data..."}),children:[i==="dashboard"&&e.jsx(P,{onQuickAction:l,period:g}),i==="analytics"&&e.jsx(T,{period:g,onPeriodChange:y}),i==="tracker"&&e.jsx("div",{className:"space-y-6",children:e.jsx(a,{children:e.jsx(t,{className:"p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(p,{size:48,className:"mx-auto mb-4 text-gray-400"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Revenue Tracker"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Track revenue entries and manage project-specific earnings"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Select a specific project to view detailed revenue tracking"})]})})})}),i==="distribution"&&e.jsx("div",{className:"space-y-6",children:e.jsx(a,{children:e.jsx(t,{className:"p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(o,{size:48,className:"mx-auto mb-4 text-gray-400"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Revenue Distribution"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Configure and manage revenue sharing across projects"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Select a specific project to configure revenue distribution"})]})})})}),i==="settings"&&e.jsx(R,{})]})},i),e.jsx(s.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.8},className:"fixed bottom-6 right-6 z-50",children:e.jsx(a,{className:"shadow-lg",children:e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(n,{size:"sm",color:"success",variant:"flat",onPress:()=>l("withdraw"),className:"w-full",children:"Quick Withdraw"}),e.jsx(n,{size:"sm",color:"primary",variant:"flat",onPress:()=>l("goals"),className:"w-full",children:"Set Goals"}),e.jsx(n,{size:"sm",color:"secondary",variant:"flat",onPress:()=>l("analytics"),className:"w-full",children:"View Analytics"})]})})})})]})})};export{V as default};
