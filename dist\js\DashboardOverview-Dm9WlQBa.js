var I=(i,o,a)=>new Promise((p,l)=>{var h=n=>{try{c(a.next(n))}catch(s){l(s)}},x=n=>{try{c(a.throw(n))}catch(s){l(s)}},c=n=>n.done?p(n.value):Promise.resolve(n.value).then(h,x);c((a=a.apply(i,o)).next())});import{r as u,j as e,m as r,c as L,b as O,w as Q,h as f,e as j}from"./chunk-DX4Z_LyS.js";import{U,s as m}from"../assets/main-CGUKzV0x.js";import{a as W}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";import"./chunk-BiNxGM8y.js";const Z=()=>{var n;const{currentUser:i}=u.useContext(U),o=W(),[a,p]=u.useState({totalProjects:0,activeContributions:0,totalRevenue:0,pendingValidations:0}),[l,h]=u.useState(!0);u.useEffect(()=>{I(null,null,function*(){var d,b,y,v,w,N,C,_,k,P,S,R,q,A,V,z;if(i)try{let g=0;try{const{count:t}=yield m.from("projects").select("id",{count:"exact",head:!0}).eq("created_by",i.id);g=t||0}catch(t){(d=t.message)!=null&&d.includes("401")||(b=t.message)!=null&&b.includes("permission")||(y=t.message)!=null&&y.includes("relation")||(v=t.message)!=null&&v.includes("does not exist")}let D=0;try{const{count:t}=yield m.from("contributions").select("id",{count:"exact",head:!0}).eq("user_id",i.id).eq("status","approved");D=t||0}catch(t){(w=t.message)!=null&&w.includes("401")||(N=t.message)!=null&&N.includes("permission")||(C=t.message)!=null&&C.includes("relation")||(_=t.message)!=null&&_.includes("does not exist")}let E=0;try{const{data:t}=yield m.from("revenue_entries").select(`
              amount,
              project_id,
              projects!inner(
                project_contributors!inner(user_id)
              )
            `).eq("projects.project_contributors.user_id",i.id);E=(t==null?void 0:t.reduce((T,F)=>T+(F.amount||0),0))||0}catch(t){(k=t.message)!=null&&k.includes("401")||(P=t.message)!=null&&P.includes("permission")||(S=t.message)!=null&&S.includes("relation")||(R=t.message)!=null&&R.includes("does not exist")}let H=0;try{const{count:t}=yield m.from("contributions").select("id",{count:"exact",head:!0}).eq("user_id",i.id).eq("status","pending");H=t||0}catch(t){(q=t.message)!=null&&q.includes("401")||(A=t.message)!=null&&A.includes("permission")||(V=t.message)!=null&&V.includes("relation")||(z=t.message)!=null&&z.includes("does not exist")}p({totalProjects:g,activeContributions:D,totalRevenue:E,pendingValidations:H})}catch(g){}finally{h(!1)}})},[i]);const x=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s),c=[{title:"Active Projects",value:a.totalProjects,icon:"📁",color:"from-blue-500 to-cyan-500",description:"Projects you own or contribute to"},{title:"Contributions",value:a.activeContributions,icon:"⏱️",color:"from-green-500 to-emerald-500",description:"Approved contributions this month"},{title:"Total Revenue",value:x(a.totalRevenue),icon:"💰",color:"from-yellow-500 to-orange-500",description:"Lifetime earnings from projects"},{title:"Pending Reviews",value:a.pendingValidations,icon:"⏳",color:"from-purple-500 to-pink-500",description:"Contributions awaiting validation"}];return l?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx(r.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"})}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs(r.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-center",children:[e.jsxs("h1",{className:"text-4xl font-bold text-white mb-2",children:["Welcome back, ",((n=i==null?void 0:i.user_metadata)==null?void 0:n.full_name)||(i==null?void 0:i.email),"! 👋"]}),e.jsx("p",{className:"text-white/70 text-lg",children:"Here's what's happening with your projects today"})]}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:c.map((s,d)=>e.jsx(r.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3,delay:.1*d},whileHover:{scale:1.05},className:"h-full",children:e.jsx(L,{className:"h-full bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all duration-300",children:e.jsxs(O,{className:"p-6",children:[e.jsx("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${s.color} flex items-center justify-center mb-4`,children:e.jsx("span",{className:"text-2xl",children:s.icon})}),e.jsx("h3",{className:"text-white/90 text-sm font-medium mb-1",children:s.title}),e.jsx("p",{className:"text-white text-2xl font-bold mb-2",children:s.value}),e.jsx("p",{className:"text-white/60 text-xs",children:s.description})]})})},s.title))}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},children:e.jsxs(L,{className:"bg-white/10 backdrop-blur-md border-white/20",children:[e.jsx(Q,{children:e.jsx("h2",{className:"text-xl font-bold text-white",children:"Quick Actions"})}),e.jsx(O,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx(f,{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white",size:"lg",startContent:e.jsx("span",{children:"🚀"}),onClick:()=>o("/start"),children:"Start New Project"}),e.jsx(f,{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white",size:"lg",startContent:e.jsx("span",{children:"⏱️"}),onClick:()=>o("/track"),children:"Track Contribution"}),e.jsx(f,{className:"bg-gradient-to-r from-orange-500 to-red-600 text-white",size:"lg",startContent:e.jsx("span",{children:"📊"}),onClick:()=>o("/analytics"),children:"View Analytics"})]})})]})}),e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.6},className:"flex flex-wrap gap-2 justify-center",children:[e.jsx(j,{color:"success",variant:"flat",children:"System Online"}),e.jsxs(j,{color:"primary",variant:"flat",children:[a.totalProjects," Active Projects"]}),e.jsxs(j,{color:"warning",variant:"flat",children:[a.pendingValidations," Pending Reviews"]})]})]})};export{Z as default};
