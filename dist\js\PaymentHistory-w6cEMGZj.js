var V=Object.defineProperty,Z=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var P=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var E=(t,i,l)=>i in t?V(t,i,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[i]=l,h=(t,i)=>{for(var l in i||(i={}))G.call(i,l)&&E(t,l,i[l]);if(P)for(var l of P(i))Q.call(i,l)&&E(t,l,i[l]);return t},m=(t,i)=>Z(t,B(i));var C=(t,i,l)=>new Promise((g,d)=>{var b=c=>{try{o(l.next(c))}catch(u){d(u)}},r=c=>{try{o(l.throw(c))}catch(u){d(u)}},o=c=>c.done?g(c.value):Promise.resolve(c.value).then(b,r);o((l=l.apply(t,i)).next())});import{Z as X,r as j,j as e,c as H,w as Y,h as A,b as L,p as ee,u as N,v as n,H as se,I as ae,J as _,K as te,L as re,M as f,e as T,a7 as ne,C as le,l as ie,n as de,o as ce,E as oe}from"./chunk-DX4Z_LyS.js";import{j as he,s as me}from"../assets/main-CGUKzV0x.js";import{a as xe,b as M,f as O}from"./chunk-CqI-o4WX.js";import{D as ue,w as je,E as _e,aE as fe,aF as pe}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";const Te=()=>{const{currentUser:t}=he(),{isOpen:i,onOpen:l,onClose:g}=X(),[d,b]=j.useState(null),[r,o]=j.useState({search:"",status:"all",method:"all",direction:"all",dateRange:"30"}),[c,u]=j.useState({page:1,limit:10,total:0}),[q,k]=j.useState([]),[F,D]=j.useState(!0);j.useEffect(()=>{t&&U()},[t,r,c.page]);const U=()=>C(null,null,function*(){D(!0);try{let s=me.from("payment_transactions").select(`
          *,
          from_user:from_user_id(full_name, avatar_url),
          to_user:to_user_id(full_name, avatar_url),
          from_account:from_account_id(account_name, institution_name),
          to_account:to_account_id(account_name, institution_name)
        `,{count:"exact"}).or(`from_user_id.eq.${t.id},to_user_id.eq.${t.id}`).order("created_at",{ascending:!1});if(r.status!=="all"&&(s=s.eq("status",r.status)),r.method!=="all"&&(s=s.eq("payment_method",r.method)),r.direction!=="all"&&(r.direction==="inbound"?s=s.eq("to_user_id",t.id):s=s.eq("from_user_id",t.id)),r.dateRange!=="all"){const w=parseInt(r.dateRange),S=new Date;S.setDate(S.getDate()-w),s=s.gte("created_at",S.toISOString())}r.search&&(s=s.or(`description.ilike.%${r.search}%,reference_id.ilike.%${r.search}%`));const a=(c.page-1)*c.limit,x=a+c.limit-1;s=s.range(a,x);const{data:y,error:p,count:J}=yield s;if(p)throw p;k(y||[]),u(w=>m(h({},w),{total:J||0}))}catch(s){}finally{D(!1)}}),$=s=>{b(s),l()},K=()=>C(null,null,function*(){}),R=s=>({completed:"success",pending:"warning",processing:"primary",failed:"danger",cancelled:"default",returned:"warning",reversed:"danger"})[s]||"default",z=s=>s.to_user_id===t.id?e.jsx(fe,{className:"w-4 h-4 text-green-500"}):e.jsx(pe,{className:"w-4 h-4 text-red-500"}),W=s=>{const a=parseFloat(s.amount),x=s.to_user_id===t.id;return{formatted:O(a),sign:x?"+":"-",color:x?"text-green-600":"text-red-600"}},v=s=>({ach_standard:"ACH Standard",ach_same_day:"ACH Same Day",rtp:"Real-Time Payment",wire_domestic:"Wire Transfer",wire_international:"International Wire"})[s]||s,I=Math.ceil(c.total/c.limit);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs(H,{children:[e.jsx(Y,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Payment History"}),e.jsx(A,{color:"primary",variant:"flat",startContent:e.jsx(ue,{className:"w-4 h-4"}),onClick:K,children:"Export"})]})}),e.jsx(L,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4",children:[e.jsx(ee,{placeholder:"Search transactions...",value:r.search,onChange:s=>o(a=>m(h({},a),{search:s.target.value})),startContent:e.jsx(je,{className:"w-4 h-4 text-gray-400"}),className:"lg:col-span-2"}),e.jsxs(N,{placeholder:"Status",selectedKeys:[r.status],onSelectionChange:s=>o(a=>m(h({},a),{status:Array.from(s)[0]})),children:[e.jsx(n,{children:"All Status"},"all"),e.jsx(n,{children:"Completed"},"completed"),e.jsx(n,{children:"Pending"},"pending"),e.jsx(n,{children:"Processing"},"processing"),e.jsx(n,{children:"Failed"},"failed"),e.jsx(n,{children:"Cancelled"},"cancelled")]}),e.jsxs(N,{placeholder:"Method",selectedKeys:[r.method],onSelectionChange:s=>o(a=>m(h({},a),{method:Array.from(s)[0]})),children:[e.jsx(n,{children:"All Methods"},"all"),e.jsx(n,{children:"ACH Standard"},"ach_standard"),e.jsx(n,{children:"ACH Same Day"},"ach_same_day"),e.jsx(n,{children:"Real-Time Payment"},"rtp"),e.jsx(n,{children:"Wire Transfer"},"wire_domestic")]}),e.jsxs(N,{placeholder:"Direction",selectedKeys:[r.direction],onSelectionChange:s=>o(a=>m(h({},a),{direction:Array.from(s)[0]})),children:[e.jsx(n,{children:"All Directions"},"all"),e.jsx(n,{children:"Received"},"inbound"),e.jsx(n,{children:"Sent"},"outbound")]}),e.jsxs(N,{placeholder:"Date Range",selectedKeys:[r.dateRange],onSelectionChange:s=>o(a=>m(h({},a),{dateRange:Array.from(s)[0]})),children:[e.jsx(n,{children:"Last 7 days"},"7"),e.jsx(n,{children:"Last 30 days"},"30"),e.jsx(n,{children:"Last 90 days"},"90"),e.jsx(n,{children:"Last year"},"365"),e.jsx(n,{children:"All time"},"all")]})]})})]}),e.jsx(H,{children:e.jsxs(L,{className:"p-0",children:[e.jsxs(se,{"aria-label":"Payment history table",classNames:{wrapper:"min-h-[400px]"},children:[e.jsxs(ae,{children:[e.jsx(_,{children:"TRANSACTION"}),e.jsx(_,{children:"AMOUNT"}),e.jsx(_,{children:"METHOD"}),e.jsx(_,{children:"STATUS"}),e.jsx(_,{children:"DATE"}),e.jsx(_,{children:"ACTIONS"})]}),e.jsx(te,{items:q,isLoading:F,emptyContent:"No transactions found",children:s=>{var y,p;const a=W(s),x=s.to_user_id===t.id;return e.jsxs(re,{children:[e.jsx(f,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[z(s),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.description||`${v(s.payment_method)} Transfer`}),e.jsxs("p",{className:"text-sm text-gray-600",children:[x?"From":"To",": ",x?((y=s.from_user)==null?void 0:y.full_name)||"Unknown":((p=s.to_user)==null?void 0:p.full_name)||"Unknown"]})]})]})}),e.jsx(f,{children:e.jsxs("span",{className:`font-semibold ${a.color}`,children:[a.sign,a.formatted]})}),e.jsx(f,{children:e.jsx(T,{size:"sm",variant:"flat",children:v(s.payment_method)})}),e.jsx(f,{children:e.jsx(T,{size:"sm",color:R(s.status),variant:"flat",children:s.status})}),e.jsx(f,{children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm",children:xe(s.created_at)}),e.jsx("p",{className:"text-xs text-gray-500",children:M(s.created_at).split(" ")[1]})]})}),e.jsx(f,{children:e.jsx(A,{size:"sm",variant:"light",isIconOnly:!0,onClick:()=>$(s),children:e.jsx(_e,{className:"w-4 h-4"})})})]},s.id)}})]}),I>1&&e.jsx("div",{className:"flex justify-center p-4",children:e.jsx(ne,{total:I,page:c.page,onChange:s=>u(a=>m(h({},a),{page:s})),showControls:!0,showShadow:!0})})]})}),e.jsx(le,{isOpen:i,onClose:g,size:"2xl",children:e.jsxs(ie,{children:[e.jsx(de,{children:"Transaction Details"}),e.jsx(ce,{children:d&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Transaction ID"}),e.jsx("p",{className:"font-mono text-sm",children:d.id})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Amount"}),e.jsx("p",{className:"font-semibold text-lg",children:O(d.amount)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Status"}),e.jsx(T,{color:R(d.status),variant:"flat",children:d.status})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("p",{children:v(d.payment_method)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Created"}),e.jsx("p",{children:M(d.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Reference"}),e.jsx("p",{className:"font-mono text-sm",children:d.reference_id||"N/A"})]})]}),d.description&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Description"}),e.jsx("p",{children:d.description})]}),d.failure_reason&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Failure Reason"}),e.jsx("p",{className:"text-red-600",children:d.failure_reason})]})]})}),e.jsx(oe,{children:e.jsx(A,{variant:"light",onPress:g,children:"Close"})})]})})]})};export{Te as default};
