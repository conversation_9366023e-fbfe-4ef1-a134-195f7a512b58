# Royaltea - Collaborative Revenue Sharing Platform
# Production robots.txt

User-agent: *
Allow: /

# Allow important pages
Allow: /about
Allow: /features
Allow: /pricing
Allow: /contact
Allow: /blog
Allow: /help
Allow: /legal
Allow: /projects
Allow: /skills

# Disallow private/admin areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /profile/
Disallow: /settings/
Disallow: /api/
Disallow: /auth/
Disallow: /private/

# Disallow temporary/development paths
Disallow: /dev/
Disallow: /test/
Disallow: /staging/
Disallow: /_next/
Disallow: /node_modules/
Disallow: /static/

# Disallow search and filter pages to prevent duplicate content
Disallow: /search?
Disallow: /*?filter=
Disallow: /*?sort=
Disallow: /*?page=

# Allow CSS and JS files for proper rendering
Allow: /*.css
Allow: /*.js
Allow: /*.png
Allow: /*.jpg
Allow: /*.jpeg
Allow: /*.gif
Allow: /*.svg
Allow: /*.webp
Allow: /*.ico

# Sitemap location
Sitemap: https://royalty.technology/sitemap.xml

# Crawl delay (optional - adjust based on server capacity)
Crawl-delay: 1

# Specific bot instructions
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 2

# Block problematic bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

# Social media bots (allow for proper link previews)
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /