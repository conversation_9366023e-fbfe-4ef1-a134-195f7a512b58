var Te=Object.defineProperty,Pe=Object.defineProperties;var Ie=Object.getOwnPropertyDescriptors;var je=Object.getOwnPropertySymbols;var Re=Object.prototype.hasOwnProperty,qe=Object.prototype.propertyIsEnumerable;var be=(a,t,d)=>t in a?Te(a,t,{enumerable:!0,configurable:!0,writable:!0,value:d}):a[t]=d,Y=(a,t)=>{for(var d in t||(t={}))Re.call(t,d)&&be(a,d,t[d]);if(je)for(var d of je(t))qe.call(t,d)&&be(a,d,t[d]);return a},Z=(a,t)=>Pe(a,Ie(t));var X=(a,t,d)=>new Promise((s,C)=>{var w=n=>{try{M(d.next(n))}catch(N){C(N)}},k=n=>{try{M(d.throw(n))}catch(N){C(N)}},M=n=>n.done?s(n.value):Promise.resolve(n.value).then(w,k);M((d=d.apply(a,t)).next())});import{j as e,r as h}from"./chunk-DX4Z_LyS.js";import{c as $e,a as ke,L as de}from"./chunk-BV1TipCO.js";import{U as xe,L as we,P as Fe,S as Ve,s as l,e as ve,l as Me}from"../assets/main-CGUKzV0x.js";import{c as u}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const Ge=({onView:a,onSign:t,onRegenerate:d,canSign:s=!0,regenerating:C=!1})=>e.jsxs("div",{className:"agreement-buttons-container",children:[e.jsxs("button",{className:"agreement-button view-button",onClick:a,type:"button",children:[e.jsx("i",{className:"bi bi-file-earmark-text"}),e.jsx("span",{children:"View Agreement"})]}),s&&e.jsxs("button",{className:"agreement-button sign-button",onClick:t,type:"button",children:[e.jsx("i",{className:"bi bi-pen"}),e.jsx("span",{children:"Sign Agreement"})]}),e.jsxs("button",{className:"agreement-button regenerate-button",onClick:d,disabled:C,type:"button",children:[e.jsx("i",{className:"bi bi-arrow-repeat"}),e.jsx("span",{children:C?"Regenerating...":"Regenerate Agreement"})]})]}),Be=({agreement:a,onClose:t,formatDate:d})=>{var R,b,ee;const[s,C]=h.useState(null),[w,k]=h.useState(!1),n=(()=>{const o=a.previous_versions||[],P={version:a.version||1,agreement_text:a.agreement_text,updated_at:a.updated_at,status:a.status};return[...o,P].sort((F,G)=>G.version-F.version)})(),N=o=>{C(o),k(!1)},U=()=>{w?k(!1):s&&s.version!==n[0].version&&k(!0)},T=(o,P)=>{if(!o||!P)return[];const F=o.split(`
`),G=P.split(`
`),ce=[],le=Math.max(F.length,G.length);for(let B=0;B<le;B++){const te=B<F.length?F[B]:"",re=B<G.length?G[B]:"";te!==re&&ce.push({lineNumber:B+1,oldLine:te,newLine:re,type:te?re?"changed":"removed":"added"})}return ce},y=o=>{const P=n.findIndex(F=>F.version===o.version);return P>0?n[P-1]:null};return e.jsx("div",{className:"modal-overlay",children:e.jsxs("div",{className:"version-history-modal",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Agreement Version History"}),e.jsx("button",{className:"close-button",onClick:t,children:"×"})]}),e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"version-history-container",children:[e.jsxs("div",{className:"version-list",children:[e.jsx("h4",{children:"Versions"}),e.jsx("ul",{children:n.map(o=>e.jsxs("li",{className:`version-item ${(s==null?void 0:s.version)===o.version?"selected":""}`,onClick:()=>N(o),children:[e.jsxs("div",{className:"version-number",children:["Version ",o.version]}),e.jsx("div",{className:"version-date",children:d(o.updated_at)}),e.jsx("div",{className:"version-status",children:o.status})]},o.version))})]}),e.jsx("div",{className:"version-content",children:s?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"version-content-header",children:[e.jsxs("h4",{children:["Version ",s.version,s.version===n[0].version?" (Current)":""]}),e.jsx("div",{className:"version-actions",children:s.version!==n[0].version&&e.jsx("button",{className:`compare-button ${w?"active":""}`,onClick:U,disabled:s.version===n[0].version,children:w?"Hide Diff":"Show Changes"})})]}),w?e.jsxs("div",{className:"version-diff",children:[e.jsxs("h5",{children:["Changes from Version ",s.version," to ",(R=y(s))==null?void 0:R.version]}),e.jsxs("div",{className:"diff-content",children:[T(s.agreement_text,(b=y(s))==null?void 0:b.agreement_text).map((o,P)=>e.jsxs("div",{className:`diff-line ${o.type}`,children:[e.jsx("span",{className:"line-number",children:o.lineNumber}),e.jsx("div",{className:"line-content",children:o.type==="added"?e.jsxs("div",{className:"added-line",children:["+ ",o.newLine]}):o.type==="removed"?e.jsxs("div",{className:"removed-line",children:["- ",o.oldLine]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"removed-line",children:["- ",o.oldLine]}),e.jsxs("div",{className:"added-line",children:["+ ",o.newLine]})]})})]},P)),T(s.agreement_text,(ee=y(s))==null?void 0:ee.agreement_text).length===0&&e.jsx("div",{className:"no-changes",children:"No changes detected"})]})]}):e.jsx("div",{className:"version-text",children:e.jsx("pre",{children:s.agreement_text})})]}):e.jsx("div",{className:"no-version-selected",children:e.jsx("p",{children:"Select a version to view details"})})})]})})]})})},Le=({projectId:a})=>{var ue,pe,he,_e;const{currentUser:t}=h.useContext(xe),[d,s]=h.useState(!0),[C,w]=h.useState(!1),[k,M]=h.useState([]),[n,N]=h.useState(null),[U,T]=h.useState(!1),[y,R]=h.useState(null),[b,ee]=h.useState(((ue=t==null?void 0:t.user_metadata)==null?void 0:ue.full_name)||""),[o,P]=h.useState(""),[F,G]=h.useState(!1),[ce,le]=h.useState(null),[B,te]=h.useState(!1),[re,ye]=h.useState(!1),[me,Ne]=h.useState(null),ge=()=>X(null,null,function*(){var r;if(!(!a||!t))try{s(!0);const{data:q,error:S}=yield l.from("projects").select("name").eq("id",a).single();S||P(q.name||"");const{data:i,error:A}=yield l.from("project_contributors").select("*").eq("project_id",a).eq("user_id",t.id).single();if(A&&A.code!=="PGRST116")throw A;i&&(le(i.permission_level),te(i.permission_level==="Owner"||i.permission_level==="Admin"));const{data:_,error:I}=yield l.from("contributor_agreements").select(`
          *,
          project_contributors!inner(
            id,
            user_id,
            permission_level,
            display_name,
            email
          )
        `).eq("project_id",a).order("created_at",{ascending:!1}),E=(_==null?void 0:_.map(m=>{var v;return(v=m.project_contributors)==null?void 0:v.user_id}).filter(Boolean))||[];let ne={};if(E.length>0){const{data:m,error:v}=yield l.from("users").select("id, display_name, email, avatar_url").in("id",E);!v&&m&&(ne=m.reduce((D,c)=>(D[c.id]=c,D),{}))}if(I)throw I;const V=_.map(m=>{var c,p,f;const v=(c=m.project_contributors)==null?void 0:c.user_id,D=ne[v]||{display_name:((p=m.project_contributors)==null?void 0:p.display_name)||"Unknown User",email:((f=m.project_contributors)==null?void 0:f.email)||"No email",avatar_url:null};return Z(Y({},m),{contributor:m.project_contributors,user:D})});M(V);const $=V.find(m=>{var v;return((v=m.contributor)==null?void 0:v.user_id)===t.id});if($)N($),(r=$.signature_data)!=null&&r.signature&&(R($.signature_data.signature),ee($.signature_data.full_name||b));else if(V.length>0){const m=Y({},V[0]);m.id=null,m.status="pending",m.signature_data=null,m.signed_at=null,N(m)}}catch(q){u.error("Failed to load agreements")}finally{s(!1)}});h.useEffect(()=>{ge()},[a,t,b]);const Se=r=>{R(r),T(!1)},Ee=()=>X(null,null,function*(){if(!a||!t||!y||!n){u.error("Missing required information for signing");return}if(!b.trim()){u.error("Full name is required");return}s(!0);try{const{data:r,error:q}=yield l.from("project_contributors").select("id").eq("project_id",a).eq("user_id",t.id).single();if(q){u.error("Could not find your contributor record"),s(!1);return}const{data:S,error:i}=yield l.from("contributor_agreements").select("id").eq("project_id",a).eq("contributor_id",r.id).single();if(i&&i.code!=="PGRST116"){u.error("Error checking agreement status"),s(!1);return}if(i&&i.code==="PGRST116"){const{data:_,error:I}=yield l.from("contributor_agreements").insert({project_id:a,contributor_id:r.id,agreement_text:n.agreement_text,version:1,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}).select().single();if(I){u.error("Failed to create agreement"),s(!1);return}N(_);const{error:E}=yield l.from("contributor_agreements").update({signature_data:{signature:y,signed_by:t.id,signed_at:new Date().toISOString(),full_name:b},status:"signed",signed_at:new Date().toISOString(),updated_at:new Date().toISOString(),version:1}).eq("id",_.id)}else{const{error:_}=yield l.from("contributor_agreements").update({signature_data:{signature:y,signed_by:t.id,signed_at:new Date().toISOString(),full_name:b},status:"signed",signed_at:new Date().toISOString(),updated_at:new Date().toISOString(),version:n.version||1}).eq("id",n.id);if(_)throw _}const A=Z(Y({},n),{signature_data:{signature:y,signed_by:t.id,signed_at:new Date().toISOString(),full_name:b},status:"signed",signed_at:new Date().toISOString(),updated_at:new Date().toISOString(),version:n.version||1});N(A),yield Me(a,t.id,"agreement_signed",{agreement_id:n.id,version:n.version||1});try{(yield fetch("/.netlify/functions/agreement-notifications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"sign",agreementId:n.id})})).ok}catch(_){}u.success("Agreement signed successfully")}catch(r){u.error("Failed to sign agreement")}finally{s(!1)}}),ae=r=>r?new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",De=()=>X(null,null,function*(){var r;if(!a||!t){u.error("Missing required information");return}s(!0);try{const{data:q,error:S}=yield l.from("project_contributors").select("id").eq("project_id",a).eq("user_id",t.id).single();if(S){u.error("Could not find your contributor record"),s(!1);return}const{data:i,error:A}=yield l.from("projects").select("*").eq("id",a).single();if(A)throw A;const{data:_,error:I}=yield l.from("project_contributors").select("*").eq("project_id",a);if(I)throw I;const E=new Date,ne=E.toLocaleDateString("en-US",{month:"long"}),V=E.getDate(),$=E.getFullYear().toString(),m=`${ne} ${V}, ${$}`,v=yield fetch("/example-cog-contributor-agreement.md").then(g=>g.text()),D=_.find(g=>g.permission_level==="Owner"),c=(D==null?void 0:D.display_name)||"Project Owner",p=((r=t==null?void 0:t.user_metadata)==null?void 0:r.full_name)||t.email,{data:f,error:L}=yield l.from("project_milestones").select("*").eq("project_id",a).order("due_date",{ascending:!0}),H=()=>{const g=`
### Project Type
${i.project_type||"Software Development"}

### Project Description
${i.description||"A collaborative project"}

### Technical Requirements
- Platform: ${i.platform||"Web, Mobile, Desktop"}
- Technologies: ${i.technologies||"To be determined based on project needs"}
- Compatibility: ${i.compatibility||"Standard browser and device compatibility"}
`;let j=`### Project Milestones

`;return f&&f.length>0?(j+=`The following milestones have been established for this project:

`,f.forEach((x,Q)=>{const fe=x.due_date?new Date(x.due_date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"To be determined";j+=`**${Q+1}. ${x.title}**
`,j+=`   - Description: ${x.description||"No description provided"}
`,j+=`   - Due Date: ${fe}
`,j+=`   - Status: ${x.status||"Pending"}

`})):j+=`Milestones will be defined as the project progresses.

`,{specificationsText:g,milestonesText:j}},{specificationsText:J,milestonesText:z}=H();let W;try{const g=new AgreementGenerator,{companyInfo:j,options:x}=prepareAgreementData(i,_,{email:t.email,user_metadata:{full_name:p}},null,p);W=yield g.generateAgreement(v,i,x)}catch(g){W=v.replace(/\[Project Name\]/g,i.name).replace(/\[Date\]/g,m).replace(/\[Project Owner\]/g,c).replace(/\[Contributor\]/g,p).replace(/Village of The Ages/g,i.name).replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g,i.description||"A collaborative project").replace(/City of Gamers Inc\./gi,i.company_name||c||"Project Owner").replace(/City of Gamers/gi,i.company_name||c||"Project Owner").replace(/\bCOG\b/gi,i.company_name||c||"Project Owner").replace(/Gynell Journigan/gi,c).replace(/Florida/gi,i.state||"Delaware").replace(/Orlando/gi,i.city||"Wilmington").replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi,i.address||"1209 N Orange St, Wilmington, DE 19801").replace(/the applicable jurisdiction/gi,i.state||"Delaware").replace(/billing@cogfuture\.com/gi,i.contact_email||ownerEmail||"<EMAIL>").replace(/\[Project Owner Email\]/gi,i.contact_email||ownerEmail||"<EMAIL>").replace(/\[Company Email\]/gi,i.contact_email||ownerEmail||"<EMAIL>").replace(/\[Email\]/gi,i.contact_email||ownerEmail||"<EMAIL>").replace(/\[ \], 20\[__\]/g,m).replace(/Effective Date: .+?\n/g,`Effective Date: ${m}
`).replace(/THIS CONTRIBUTOR AGREEMENT \(this "Agreement"\) is made as of .+? by and between/g,`THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of ${m} by and between`).replace(/\[_+\]/g,p).replace(/\[Contributor Name\]/g,p).replace(/\[CONTRIBUTOR NAME\]/g,p.toUpperCase()).replace(/\[__\]/g,"").replace(/\[\s*\]/g,"").replace(/\[.*?\]/g,"").replace(/## EXHIBIT I([\s\S]*?)## EXHIBIT II/g,`## EXHIBIT I
### SPECIFICATIONS

${J}

## EXHIBIT II`).replace(/## EXHIBIT II([\s\S]*?)IN WITNESS WHEREOF/g,`## EXHIBIT II
### PRODUCT ROADMAP

${z}

IN WITNESS WHEREOF`),u.error("Error generating agreement. Using basic template instead.")}W.length<v.length*.7;const{data:se,error:ie}=yield l.from("contributor_agreements").insert({project_id:a,contributor_id:q.id,agreement_text:W,version:1,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}).select().single();if(ie){u.error("Failed to create agreement"),s(!1);return}u.success("New agreement created successfully"),N(se);const{data:oe,error:K}=yield l.from("contributor_agreements").select(`
          *,
          project_contributors!inner(
            id,
            user_id,
            permission_level,
            display_name,
            email
          )
        `).eq("project_id",a).order("created_at",{ascending:!1});if(K)throw K;const O=oe.map(g=>{var j,x;return Z(Y({},g),{contributor:g.project_contributors,user:{display_name:((j=g.project_contributors)==null?void 0:j.display_name)||"Unknown User",email:((x=g.project_contributors)==null?void 0:x.email)||"No email",avatar_url:null}})});M(O)}catch(q){u.error("Failed to create new agreement")}finally{s(!1)}}),Ae=()=>X(null,null,function*(){if(!a||!t){u.error("Missing required information");return}w(!0);try{const{data:r,error:q}=yield l.from("projects").select("*").eq("id",a).single();if(q)throw q;const{data:S,error:i}=yield l.from("project_contributors").select("*").eq("project_id",a);if(i)throw i;let A=null;try{const{data:c,error:p}=yield l.from("royalty_models").select("*").eq("project_id",a).maybeSingle();!p&&c?A=c:p&&p.code}catch(c){}const _=yield fetch("/example-cog-contributor-agreement.md");if(!_.ok)throw new Error(`Failed to load template: ${_.status}`);const I=yield _.text();let E=[];try{const{data:c,error:p}=yield l.from("milestones").select("*").eq("project_id",a).order("created_at",{ascending:!0});!p&&c&&c.length>0&&(E=c)}catch(c){}!E||E.length;const V=yield X(null,null,function*(){var c,p,f,L,H,J,z,W,se,ie;try{const oe={project:Z(Y({},r),{projectType:r.project_type||"project",name:r.name||r.title||"Project",description:r.description||"A collaborative project",platform:r.platforms||r.distribution_platforms||"",technologies:r.technology_stack||r.engine||"",compatibility:r.platforms||""}),user:{owner:{name:((c=S.find(O=>O.permission_level==="Owner"))==null?void 0:c.display_name)||"Project Owner",email:((p=S.find(O=>O.permission_level==="Owner"))==null?void 0:p.email)||"[Project Owner Email]",company:r.company_name||((f=S.find(O=>O.permission_level==="Owner"))==null?void 0:f.display_name)||"Project Owner",address:r.company_address||"1209 N Orange St, Wilmington, DE 19801",city:r.city||"Wilmington",state:r.company_state||r.state||"Delaware",zip:"19801"}},contributors:S,currentUser:t,royaltyModel:A,milestones:E||[],fullName:b};return ve.generateAgreement(I,oe,{agreementDate:new Date})}catch(oe){try{const K={project:Z(Y({},r),{projectType:r.project_type||"project",name:r.name||r.title||"Project",description:r.description||"A collaborative project",platform:r.platforms||r.distribution_platforms||"",technologies:r.technology_stack||r.engine||"",compatibility:r.platforms||""}),user:{owner:{name:((L=S.find(g=>g.permission_level==="Owner"))==null?void 0:L.display_name)||"Project Owner",email:((H=S.find(g=>g.permission_level==="Owner"))==null?void 0:H.email)||"<EMAIL>",company:r.company_name||((J=S.find(g=>g.permission_level==="Owner"))==null?void 0:J.display_name)||"Project Owner",address:r.company_address||"1209 N Orange St, Wilmington, DE 19801",city:r.city||"Wilmington",state:r.company_state||r.state||"Delaware",zip:"19801"}},contributors:S,currentUser:t,royaltyModel:A,milestones:E||[],fullName:b};return ve.generateAgreement(I,K,{agreementDate:new Date})}catch(K){const O=new Date,g=`${O.toLocaleDateString("en-US",{month:"long"})} ${O.getDate()}, ${O.getFullYear()}`,j=S.find(Ce=>Ce.permission_level==="Owner"),x=(j==null?void 0:j.display_name)||"Project Owner",Q=(j==null?void 0:j.email)||"<EMAIL>";return I.replace(/\[Project Name\]/g,r.name).replace(/\[Date\]/g,g).replace(/\[Project Owner\]/g,x).replace(/\[Contributor\]/g,b||((z=t==null?void 0:t.user_metadata)==null?void 0:z.full_name)||(t==null?void 0:t.email)||"Contributor").replace(/Village of The Ages/g,r.name).replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g,r.description||"A collaborative project").replace(/City of Gamers Inc\./gi,r.company_name||x||"Project Owner").replace(/City of Gamers/gi,r.company_name||x||"Project Owner").replace(/\bCOG\b/gi,r.company_name||x||"Project Owner").replace(/Gynell Journigan/gi,x).replace(/Florida/gi,r.state||"Delaware").replace(/Orlando/gi,r.city||"Wilmington").replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi,r.address||"1209 N Orange St, Wilmington, DE 19801").replace(/the applicable jurisdiction/gi,r.state||"Delaware").replace(/billing@cogfuture\.com/gi,r.contact_email||Q||"<EMAIL>").replace(/\[Project Owner Email\]/gi,r.contact_email||Q||"<EMAIL>").replace(/\[Company Email\]/gi,r.contact_email||Q||"<EMAIL>").replace(/\[Email\]/gi,r.contact_email||Q||"<EMAIL>").replace(/\[ \], 20\[__\]/g,g).replace(/Effective Date: .+?\n/g,`Effective Date: ${g}
`).replace(/THIS CONTRIBUTOR AGREEMENT \(this "Agreement"\) is made as of .+? by and between/g,`THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of ${g} by and between`).replace(/\[_+\]/g,b||((W=t==null?void 0:t.user_metadata)==null?void 0:W.full_name)||(t==null?void 0:t.email)||"Contributor").replace(/\[Contributor Name\]/g,b||((se=t==null?void 0:t.user_metadata)==null?void 0:se.full_name)||(t==null?void 0:t.email)||"Contributor").replace(/\[CONTRIBUTOR NAME\]/g,(b||((ie=t==null?void 0:t.user_metadata)==null?void 0:ie.full_name)||(t==null?void 0:t.email)||"Contributor").toUpperCase()).replace(/\[__\]/g,"").replace(/\[\s*\]/g,"").replace(/\[.*?\]/g,"")}}}),{data:$,error:m}=yield l.from("project_contributors").select("id").eq("project_id",a).eq("user_id",t.id).single();if(m){u.error("Could not find your contributor record"),w(!1);return}const{data:v,error:D}=yield l.from("contributor_agreements").select("id").eq("project_id",a).eq("contributor_id",$.id).single();if(D&&D.code!=="PGRST116"){u.error("Error checking agreement status"),w(!1);return}if(D&&D.code==="PGRST116"){const{data:c,error:p}=yield l.from("contributor_agreements").insert({project_id:a,contributor_id:$.id,agreement_text:V,version:1,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()}).select().single();if(p){u.error("Failed to create agreement"),w(!1);return}N(c),u.success("New agreement created successfully")}else{const p=((n==null?void 0:n.version)||1)+1,{data:f,error:L}=yield l.from("contributor_agreements").select("*").eq("id",v.id).single();if(L)throw L;const H=f.previous_versions||[];H.push({version:f.version||1,agreement_text:f.agreement_text,updated_at:f.updated_at,created_at:f.created_at,updated_by:t.id,status:f.status,signature_data:f.signature_data,signed_at:f.signed_at});const{data:J,error:z}=yield l.from("contributor_agreements").update({agreement_text:V,version:p,status:"pending",signature_data:null,signed_at:null,updated_at:new Date().toISOString(),created_at:new Date().toISOString(),previous_versions:H}).eq("id",v.id).select().single();if(z){u.error("Failed to update agreement"),w(!1);return}N(J),R(null),u.success("Agreement updated to version "+p)}setTimeout(()=>{ge()},500)}catch(r){u.error("Failed to regenerate agreement")}finally{w(!1)}}),Oe=r=>{switch(r){case"signed":return e.jsx("span",{className:"status-badge signed",children:"Signed"});case"pending":return e.jsx("span",{className:"status-badge pending",children:"Pending"});case"rejected":return e.jsx("span",{className:"status-badge rejected",children:"Rejected"});default:return e.jsx("span",{className:"status-badge",children:r})}};return d?e.jsx(we,{}):e.jsxs("div",{className:"agreement-manager",children:[F&&n&&e.jsx(Fe,{agreementText:n.agreement_text,metadata:{title:"Contributor Agreement",projectName:o||"Project",date:ae(n.created_at),filename:`${o||"project"}_agreement_v${n.version||1}.pdf`,version:n.version||1,signature:n.signature_data?{name:n.signature_data.full_name,date:ae(n.signature_data.signed_at),image:n.signature_data.signature}:null},onClose:()=>G(!1)}),re&&me&&e.jsx(Be,{agreement:me,onClose:()=>{ye(!1),Ne(null)},formatDate:ae}),U&&e.jsx("div",{className:"modal-overlay",children:e.jsxs("div",{className:"signature-modal",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Digital Signature"}),e.jsx("button",{className:"close-button",onClick:()=>T(!1),children:"×"})]}),e.jsx("div",{className:"modal-body",children:e.jsx(Ve,{onSave:Se,initialValue:y})})]})}),e.jsx("h2",{className:"section-title",children:"Project Agreements"}),n?e.jsxs("div",{className:"current-agreement",children:[e.jsx("h3",{children:"Your Agreement"}),e.jsxs("div",{className:"agreement-card",children:[e.jsxs("div",{className:"agreement-header",children:[e.jsxs("div",{className:"agreement-info",children:[e.jsx("div",{className:"agreement-title",children:"Contributor Agreement"}),e.jsxs("div",{className:"agreement-date",children:["Created on ",ae(n.created_at)]}),e.jsxs("div",{className:"agreement-version",children:["Version ",n.version||1]})]}),e.jsx("div",{className:"agreement-status",children:Oe(n.status)})]}),e.jsx(Ge,{onView:()=>G(!0),onSign:()=>T(!0),onRegenerate:Ae,canSign:n.status!=="signed",regenerating:C}),n.status!=="signed"&&e.jsxs("div",{className:"signature-section",children:[e.jsx("h4",{children:"Digital Signature"}),e.jsx("p",{className:"signature-info",children:"By signing this agreement, you acknowledge that you have read, understood, and agree to the terms and conditions outlined in the agreement."}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"fullName",children:"Full Name"}),e.jsx("input",{type:"text",id:"fullName",value:b,onChange:r=>ee(r.target.value),placeholder:"Enter your full legal name",className:"form-control"})]}),y?e.jsxs("div",{className:"signature-preview",children:[e.jsx("div",{className:"signature-image",children:e.jsx("img",{src:y,alt:"Your signature"})}),e.jsx("button",{className:"change-signature-button",onClick:()=>T(!0),children:"Change Signature"})]}):e.jsxs("button",{className:"add-signature-button",onClick:()=>T(!0),children:[e.jsx("i",{className:"bi bi-pen"})," Add Your Signature"]}),e.jsx("button",{className:"sign-agreement-button",onClick:Ee,disabled:!y||!b.trim(),children:"Sign Agreement"})]}),n.status==="signed"&&e.jsxs("div",{className:"signed-info",children:[e.jsxs("div",{className:"signed-details",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Signed by:"})," ",(pe=n.signature_data)==null?void 0:pe.full_name]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Signed on:"})," ",ae((he=n.signature_data)==null?void 0:he.signed_at)]})]}),e.jsx("div",{className:"signature-display",children:e.jsx("img",{src:(_e=n.signature_data)==null?void 0:_e.signature,alt:"Signature",className:"signature-image"})})]})]})]}):e.jsxs("div",{className:"no-agreement",children:[e.jsx("p",{children:"You don't have an agreement for this project yet."}),e.jsxs("button",{className:"create-agreement-button",onClick:De,children:[e.jsx("i",{className:"bi bi-file-earmark-plus"})," Create New Agreement"]})]})]})},Qe=()=>{const{id:a}=$e(),t=ke(),{currentUser:d}=h.useContext(xe),[s,C]=h.useState(null),[w,k]=h.useState(!0),[M,n]=h.useState(null);return h.useEffect(()=>{X(null,null,function*(){if(!a||!d){t("/projects");return}try{k(!0);const{data:U,error:T}=yield l.from("projects").select("*").eq("id",a).single();if(T)throw T;C(U);const{data:y,error:R}=yield l.from("project_contributors").select("*").eq("project_id",a).eq("user_id",d.id).single();if(R&&R.code!=="PGRST116")throw R;y?n(y.permission_level):(u.error("You do not have access to this project"),t("/projects"))}catch(U){u.error("Failed to load project data"),t("/projects")}finally{k(!1)}})},[a,d,t]),w?e.jsx(we,{}):s?e.jsxs("div",{className:"project-agreements-page",children:[e.jsxs("div",{className:"page-header",children:[e.jsxs("div",{className:"header-content",children:[e.jsx("h1",{className:"page-title",children:"Project Agreements"}),e.jsxs("div",{className:"project-info",children:[e.jsx(de,{to:`/project/${a}`,className:"project-name-link",children:s.name}),e.jsxs("div",{className:"project-meta",children:[e.jsx("span",{className:"project-type",children:s.project_type}),e.jsx("span",{className:"project-status",children:s.is_active?"Active":"Inactive"})]})]})]}),e.jsx("div",{className:"header-actions",children:e.jsxs(de,{to:`/project/${a}`,className:"btn btn-secondary",children:[e.jsx("i",{className:"bi bi-arrow-left"})," Back to Project"]})})]}),e.jsx("div",{className:"page-content",children:e.jsx(Le,{projectId:a})})]}):e.jsxs("div",{className:"project-not-found",children:[e.jsx("h2",{children:"Project Not Found"}),e.jsx("p",{children:"The project you're looking for doesn't exist or you don't have access to it."}),e.jsx(de,{to:"/projects",className:"btn btn-primary",children:"Back to Projects"})]})};export{Qe as default};
