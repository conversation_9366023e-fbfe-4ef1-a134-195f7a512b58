var we=Object.defineProperty,Ne=Object.defineProperties;var _e=Object.getOwnPropertyDescriptors;var re=Object.getOwnPropertySymbols;var ke=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable;var ie=(n,r,a)=>r in n?we(n,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[r]=a,p=(n,r)=>{for(var a in r||(r={}))ke.call(r,a)&&ie(n,a,r[a]);if(re)for(var a of re(r))Ce.call(r,a)&&ie(n,a,r[a]);return n},N=(n,r)=>Ne(n,_e(r));var V=(n,r,a)=>new Promise((f,D)=>{var _=u=>{try{v(a.next(u))}catch(k){D(k)}},A=u=>{try{v(a.throw(u))}catch(k){D(k)}},v=u=>u.done?f(u.value):Promise.resolve(u.value).then(_,A);v((a=a.apply(n,r)).next())});import{r as c,j as e}from"./chunk-DX4Z_LyS.js";import{U as oe,c as Se,f as ne,a as Z,s as x}from"../assets/main-CGUKzV0x.js";import{a as De,c as Ae,L as le}from"./chunk-BV1TipCO.js";import{c as P}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const qe=({projectId:n,revenueId:r,onSave:a,onCancel:f})=>{const{currentUser:D}=c.useContext(oe),[_,A]=c.useState(!0),[v,u]=c.useState(!1),[k,Y]=c.useState(null),[J,z]=c.useState(null),[C,q]=c.useState([]),[T,G]=c.useState([]),[g,S]=c.useState("equal_split"),[h,L]=c.useState([]),[y,I]=c.useState(0),[F,ee]=c.useState("USD"),[w,W]=c.useState({tasks:{weight:33.33},time:{weight:33.33},difficulty:{weight:33.34},roles:{}}),[B,K]=c.useState({}),[R,ce]=c.useState(!1),[O,de]=c.useState(!1);c.useEffect(()=>{V(null,null,function*(){if(!(!n||!D))try{A(!0);const{data:i,error:l}=yield x.from("projects").select("*").eq("id",n).single();if(l)throw l;Y(i);const{data:t,error:d}=yield x.from("royalty_models").select("*").eq("project_id",n).single();if(d&&d.code,t){i.royalty_model=t;const{model_type:o,model_schema:b}=t;if(o==="equal_split")S("equal_split");else if(o==="task_based")S("task_based");else if(o==="time_based")S("time_based");else if(o==="role_based")S("role_based");else if(o==="custom"&&b==="cog"&&(S("cog_model"),t.configuration)){const{tasks_weight:U,hours_weight:H,difficulty_weight:ve}=t.configuration;W(ye=>N(p({},ye),{tasks:{weight:U||33.33},time:{weight:H||33.33},difficulty:{weight:ve||33.34}}))}}const{data:m,error:E}=yield x.from("project_contributors").select(`
            id,
            user_id,
            role,
            status
          `).eq("project_id",n).eq("status","active");if(E)throw E;const $=m.map(o=>o.user_id).filter(Boolean);let j={};if($.length>0){const{data:o,error:b}=yield x.from("users").select("id, display_name, email").in("id",$);if(b)throw b;j=o.reduce((U,H)=>(U[H.id]=H,U),{})}const M=m.filter(o=>o.user_id&&j[o.user_id]).map(o=>{var b,U;return{id:o.id,user_id:o.user_id,name:((b=j[o.user_id])==null?void 0:b.display_name)||((U=j[o.user_id])==null?void 0:U.email)||"Unknown",role:o.role,weight:1}});q(M);const te={};M.forEach(o=>{te[o.user_id]=1}),W(o=>N(p({},o),{roles:te}));const{data:be,error:ae}=yield x.from("contributions").select("*").eq("project_id",n).eq("validation_status","approved");if(ae)throw ae;if(G(be||[]),r){const{data:o,error:b}=yield x.from("revenue").select("*").eq("id",r).single();if(b)throw b;z(o),I(o.amount),ee(o.currency||"USD")}}catch(i){P.error("Failed to load data for royalty calculation")}finally{A(!1)}})},[n,r,D]),c.useEffect(()=>{_||Q()},[g,y,w,_]);const Q=()=>{if(!C||C.length===0)return;let s={};switch(g){case"cog_model":s={weights:{tasks:w.tasks.weight/100,time:w.time.weight/100,difficulty:w.difficulty.weight/100}};break;case"role_based":const l=C.map(t=>N(p({},t),{weight:w.roles[t.user_id]||1}));q(l);break}const i=Se(g,y,C,T,s);if(Object.keys(B).length>0){const l=i.map(t=>{const d=B[t.user_id];return d?N(p({},t),{amount:t.amount+d.amount,percentage:t.percentage+d.percentage,manual_adjustment:d}):t});L(l)}else L(i)},ue=s=>{S(s.target.value),K({})},he=s=>{const i=parseFloat(s.target.value)||0;I(i)},me=s=>{ee(s.target.value)},X=(s,i)=>{const l=parseFloat(i)||0;W(t=>{const d=p({},t);d[s].weight=l;const m=100-l,E=["tasks","time","difficulty"].filter(j=>j!==s),$=E.reduce((j,M)=>j+(t[M].weight||0),0);if($>0){const j=m/$;E.forEach(M=>{d[M].weight=parseFloat((t[M].weight*j).toFixed(2))})}else E.forEach(j=>{d[j].weight=parseFloat((m/E.length).toFixed(2))});return d})},je=(s,i)=>{const l=parseFloat(i)||1;W(t=>N(p({},t),{roles:N(p({},t.roles),{[s]:l})}))},se=(s,i,l)=>{const t=parseFloat(l)||0;K(d=>{const m=p({},d);return m[s]||(m[s]={amount:0,percentage:0}),m[s][i]=t,i==="percentage"&&(m[s].amount=t/100*y),i==="amount"&&(m[s].percentage=y>0?t/y*100:0),m}),Q()},ge=()=>{W(s=>N(p({},s),{tasks:{weight:33.33},time:{weight:33.33},difficulty:{weight:33.34}})),P.success("Weights reset to equal distribution")},pe=()=>{K({}),Q(),P.success("Manual adjustments reset")},xe=()=>V(null,null,function*(){if(!n||!h||h.length===0){P.error("No distribution to save");return}try{u(!0);const s=h.map(t=>({revenue_id:r,project_id:n,contributor_id:t.contributor_id,user_id:t.user_id,amount:t.amount,currency:F,percentage:t.percentage,calculation_method:g,calculation_details:N(p({},t.calculation_details),{manual_adjustment:t.manual_adjustment}),status:"pending"})),{data:i,error:l}=yield x.from("revenue_distribution").insert(s);if(l)throw l;if(r){const{error:t}=yield x.from("revenue").update({distribution_status:"in_progress"}).eq("id",r);if(t)throw t}P.success("Royalty distribution saved successfully"),a&&a(s)}catch(s){P.error("Failed to save royalty distribution")}finally{u(!1)}}),fe=()=>{switch(g){case"equal_split":return"Equal Split distributes revenue equally among all contributors, regardless of their contribution amount or type.";case"task_based":return"Task-Based distribution allocates revenue based on the number of tasks completed by each contributor.";case"time_based":return"Time-Based distribution allocates revenue based on the hours spent by each contributor.";case"role_based":return"Role-Based distribution allocates revenue based on the role and weight assigned to each contributor.";case"cog_model":return"The CoG Model (Tasks-Time-Difficulty) distributes revenue based on a weighted combination of tasks completed, hours tracked, and task difficulty.";default:return""}};return _?e.jsx("div",{className:"royalty-calculator loading",children:e.jsxs("div",{className:"loading-spinner",children:[e.jsx("i",{className:"bi bi-arrow-repeat spinning"}),e.jsx("span",{children:"Loading royalty calculator..."})]})}):e.jsxs("div",{className:"royalty-calculator",children:[e.jsxs("div",{className:"calculator-header",children:[e.jsx("h2",{children:"Royalty Calculator"}),k&&e.jsx("div",{className:"project-info",children:e.jsx("span",{className:"project-name",children:k.name})})]}),e.jsxs("div",{className:"calculator-form",children:[e.jsxs("div",{className:"form-row",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"calculation-model",children:"Calculation Model"}),e.jsxs("select",{id:"calculation-model",value:g,onChange:ue,className:"form-select",children:[e.jsx("option",{value:"equal_split",children:"Equal Split"}),e.jsx("option",{value:"task_based",children:"Task-Based"}),e.jsx("option",{value:"time_based",children:"Time-Based"}),e.jsx("option",{value:"role_based",children:"Role-Based"}),e.jsx("option",{value:"cog_model",children:"CoG Model (Tasks-Time-Difficulty)"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"total-amount",children:"Total Amount"}),e.jsxs("div",{className:"input-group",children:[e.jsx("input",{id:"total-amount",type:"number",value:y,onChange:he,className:"form-control",min:"0",step:"0.01",disabled:!!r}),e.jsxs("select",{value:F,onChange:me,className:"form-select currency-select",disabled:!!r,children:[e.jsx("option",{value:"USD",children:"USD"}),e.jsx("option",{value:"EUR",children:"EUR"}),e.jsx("option",{value:"GBP",children:"GBP"}),e.jsx("option",{value:"CAD",children:"CAD"}),e.jsx("option",{value:"AUD",children:"AUD"}),e.jsx("option",{value:"JPY",children:"JPY"})]})]})]})]}),e.jsxs("div",{className:"model-description",children:[e.jsxs("button",{className:"toggle-details-btn",onClick:()=>de(!O),children:[e.jsx("i",{className:`bi bi-info-circle${O?"-fill":""}`}),O?"Hide Model Details":"Show Model Details"]}),O&&e.jsxs("div",{className:"model-details",children:[e.jsx("p",{children:fe()}),g==="cog_model"&&e.jsxs("div",{className:"cog-weights",children:[e.jsx("h4",{children:"CoG Model Weights"}),e.jsxs("div",{className:"weights-container",children:[e.jsxs("div",{className:"weight-group",children:[e.jsx("label",{children:"Tasks Weight"}),e.jsx("input",{type:"number",value:w.tasks.weight,onChange:s=>X("tasks",s.target.value),min:"0",max:"100",step:"0.01"}),e.jsx("span",{className:"weight-percentage",children:"%"})]}),e.jsxs("div",{className:"weight-group",children:[e.jsx("label",{children:"Time Weight"}),e.jsx("input",{type:"number",value:w.time.weight,onChange:s=>X("time",s.target.value),min:"0",max:"100",step:"0.01"}),e.jsx("span",{className:"weight-percentage",children:"%"})]}),e.jsxs("div",{className:"weight-group",children:[e.jsx("label",{children:"Difficulty Weight"}),e.jsx("input",{type:"number",value:w.difficulty.weight,onChange:s=>X("difficulty",s.target.value),min:"0",max:"100",step:"0.01"}),e.jsx("span",{className:"weight-percentage",children:"%"})]}),e.jsx("button",{className:"reset-weights-btn",onClick:ge,title:"Reset to equal weights",children:e.jsx("i",{className:"bi bi-arrow-counterclockwise"})})]})]}),g==="role_based"&&e.jsxs("div",{className:"role-weights",children:[e.jsx("h4",{children:"Role Weights"}),e.jsx("div",{className:"role-weights-container",children:C.map(s=>e.jsxs("div",{className:"role-weight-group",children:[e.jsx("label",{children:s.name}),e.jsx("input",{type:"number",value:w.roles[s.user_id]||1,onChange:i=>je(s.user_id,i.target.value),min:"0.1",step:"0.1"}),e.jsx("span",{className:"role-label",children:s.role||"Contributor"})]},s.user_id))})]})]})]})]}),e.jsxs("div",{className:"distribution-preview",children:[e.jsxs("div",{className:"preview-header",children:[e.jsx("h3",{children:"Distribution Preview"}),e.jsx("div",{className:"preview-actions",children:e.jsxs("button",{className:`toggle-adjustments-btn ${R?"active":""}`,onClick:()=>ce(!R),children:[e.jsx("i",{className:`bi bi-sliders${R?"-fill":""}`}),R?"Hide Adjustments":"Manual Adjustments"]})})]}),h.length>0?e.jsx("div",{className:"distribution-table-container",children:e.jsxs("table",{className:"distribution-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Contributor"}),e.jsx("th",{children:"Amount"}),e.jsx("th",{children:"Percentage"}),R&&e.jsx("th",{children:"Adjustment"})]})}),e.jsx("tbody",{children:h.map(s=>{var i,l,t;return e.jsxs("tr",{children:[e.jsx("td",{className:"contributor-name",children:((i=C.find(d=>d.user_id===s.user_id))==null?void 0:i.name)||"Unknown"}),e.jsx("td",{className:"amount",children:ne(s.amount,F)}),e.jsx("td",{className:"percentage",children:Z(s.percentage)}),R&&e.jsx("td",{className:"adjustment",children:e.jsxs("div",{className:"adjustment-inputs",children:[e.jsxs("div",{className:"adjustment-input-group",children:[e.jsx("input",{type:"number",value:((l=B[s.user_id])==null?void 0:l.amount)||0,onChange:d=>se(s.user_id,"amount",d.target.value),step:"0.01",placeholder:"Amount"}),e.jsx("span",{className:"adjustment-currency",children:F})]}),e.jsxs("div",{className:"adjustment-input-group",children:[e.jsx("input",{type:"number",value:((t=B[s.user_id])==null?void 0:t.percentage)||0,onChange:d=>se(s.user_id,"percentage",d.target.value),step:"0.01",placeholder:"Percentage"}),e.jsx("span",{className:"adjustment-percentage",children:"%"})]})]})})]},s.user_id)})}),e.jsx("tfoot",{children:e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("strong",{children:"Total"})}),e.jsx("td",{children:e.jsx("strong",{children:ne(h.reduce((s,i)=>s+i.amount,0),F)})}),e.jsx("td",{children:e.jsx("strong",{children:Z(h.reduce((s,i)=>s+i.percentage,0))})}),R&&e.jsx("td",{children:e.jsxs("button",{className:"reset-adjustments-btn",onClick:pe,disabled:Object.keys(B).length===0,children:[e.jsx("i",{className:"bi bi-arrow-counterclockwise"}),"Reset"]})})]})})]})}):e.jsx("div",{className:"no-distribution",children:e.jsx("p",{children:"No distribution data available. Please check your calculation model and settings."})}),e.jsx("div",{className:"distribution-visualization",children:h.length>0&&e.jsx("div",{className:"distribution-chart",children:h.map((s,i)=>{const l=C.find(d=>d.user_id===s.user_id),t=["var(--primary-color)","var(--success-color)","var(--info-color)","var(--warning-color)","var(--danger-color)"];return e.jsx("div",{className:"distribution-bar",style:{width:`${s.percentage}%`,backgroundColor:t[i%t.length]},title:`${(l==null?void 0:l.name)||"Unknown"}: ${Z(s.percentage)}`,children:s.percentage>=5&&e.jsx("span",{className:"bar-label",children:(l==null?void 0:l.name)||"Unknown"})},s.user_id)})})})]}),e.jsxs("div",{className:"calculator-actions",children:[f&&e.jsx("button",{className:"cancel-btn",onClick:f,disabled:v,children:"Cancel"}),e.jsx("button",{className:"save-btn",onClick:xe,disabled:v||h.length===0,children:v?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"bi bi-arrow-repeat spinning"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"bi bi-save"}),"Save Distribution"]})})]})]})},Be=()=>{const{currentUser:n}=c.useContext(oe),r=De(),{projectId:a,revenueId:f}=Ae(),[D,_]=c.useState(!0),[A,v]=c.useState(null),[u,k]=c.useState(null),[Y,J]=c.useState(!1);c.useEffect(()=>{V(null,null,function*(){if(!(!n||!a))try{_(!0);const{data:q,error:T}=yield x.from("project_contributors").select("role").eq("project_id",a).eq("user_id",n.id).eq("status","active").single();if(T&&T.code!=="PGRST116")throw T;const{data:G,error:g}=yield x.from("projects").select("*").eq("id",a).single();if(g)throw g;const S=G.created_by===n.id,h=(q==null?void 0:q.role)==="admin";if(J(S||h),v(G),f){const{data:L,error:y}=yield x.from("revenue").select("*").eq("id",f).eq("project_id",a).single();if(y)throw y;k(L)}}catch(q){J(!1)}finally{_(!1)}})},[n,a,f]);const z=()=>{r(f?`/project/${a}/revenue`:`/project/${a}/royalty-calculator`)};return D?e.jsx("div",{className:"royalty-calculator-page loading",children:e.jsxs("div",{className:"loading-spinner",children:[e.jsx("i",{className:"bi bi-arrow-repeat spinning"}),e.jsx("span",{children:"Loading..."})]})}):n?Y?e.jsxs("div",{className:"royalty-calculator-page",children:[e.jsxs("div",{className:"page-header",children:[e.jsxs("div",{className:"header-content",children:[e.jsx("h1",{children:"Royalty Calculator"}),A&&e.jsxs("div",{className:"project-info",children:[e.jsx("span",{className:"project-name",children:A.name}),u&&e.jsxs("span",{className:"revenue-info",children:["Revenue: ",new Intl.NumberFormat("en-US",{style:"currency",currency:u.currency||"USD"}).format(u.amount)]})]})]}),e.jsx("div",{className:"header-actions",children:e.jsxs(le,{to:`/project/${a}/revenue`,className:"back-btn",children:[e.jsx("i",{className:"bi bi-arrow-left"}),"Back to Revenue"]})})]}),e.jsx("div",{className:"page-content",children:e.jsx(qe,{projectId:a,revenueId:f,onSave:z,onCancel:()=>r(`/project/${a}/revenue`)})})]}):e.jsx("div",{className:"royalty-calculator-page",children:e.jsxs("div",{className:"access-denied",children:[e.jsx("h2",{children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to access the royalty calculator for this project."}),e.jsx(le,{to:"/track",className:"btn btn-primary",children:"Go to Projects"})]})}):e.jsx("div",{className:"royalty-calculator-page",children:e.jsxs("div",{className:"auth-required",children:[e.jsx("h2",{children:"Authentication Required"}),e.jsx("p",{children:"Please log in to access the royalty calculator."}),e.jsx("button",{className:"btn btn-primary",onClick:()=>r("/login"),children:"Log In"})]})})};export{Be as default};
