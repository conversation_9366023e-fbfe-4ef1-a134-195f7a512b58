import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON> } from 'react-minimal-pie-chart';
import { toast } from 'react-hot-toast';
import {
  Card,
  CardBody,
  Input,
  Button,
  Select,
  SelectItem,
  Slider,
  Chip,
  Divider,
  Progress
} from '@heroui/react';
import { motion } from 'framer-motion';

const RoyaltyModel = ({ projectData, setProjectData }) => {
  const [modelType, setModelType] = useState(projectData.royalty_model.model_type || 'custom');
  const [modelSchema, setModelSchema] = useState(projectData.royalty_model.model_schema || 'cog');
  const [tasksWeight, setTasksWeight] = useState(
    projectData.royalty_model.configuration.tasks_weight || 33.33
  );
  const [hoursWeight, setHoursWeight] = useState(
    projectData.royalty_model.configuration.hours_weight || 33.33
  );
  const [difficultyWeight, setDifficultyWeight] = useState(
    projectData.royalty_model.configuration.difficulty_weight || 33.34
  );
  // Note: We don't need platform fee settings as they're handled by the platforms directly
  const [isPreExpense, setIsPreExpense] = useState(
    projectData.royalty_model.is_pre_expense !== undefined
      ? projectData.royalty_model.is_pre_expense
      : true
  );

  // Financial parameters
  const [minPayout, setMinPayout] = useState(
    projectData.royalty_model.min_payout !== undefined
      ? projectData.royalty_model.min_payout
      : 10000
  );

  const [maxPayout, setMaxPayout] = useState(
    projectData.royalty_model.max_payout !== undefined
      ? projectData.royalty_model.max_payout
      : 1000000
  );

  const [revenueShare, setRevenueShare] = useState(
    projectData.royalty_model.revenue_share !== undefined
      ? projectData.royalty_model.revenue_share
      : 50
  );

  // Sample data for royalty distribution preview
  const [previewData, setPreviewData] = useState({
    contributors: [
      { name: 'Alice', tasks: 10, hours: 40, difficulty: 4 },
      { name: 'Bob', tasks: 5, hours: 30, difficulty: 5 },
      { name: 'Charlie', tasks: 8, hours: 20, difficulty: 3 }
    ]
  });

  // State for editing preview data
  const [isEditingPreview, setIsEditingPreview] = useState(false);
  const [editedPreviewData, setEditedPreviewData] = useState(null);

  // Handle preview data editing
  const handleEditPreviewData = () => {
    setEditedPreviewData(JSON.parse(JSON.stringify(previewData)));
    setIsEditingPreview(true);
  };

  // Handle contributor data change
  const handleContributorChange = (index, field, value) => {
    const newData = { ...editedPreviewData };
    newData.contributors[index][field] = field === 'name' ? value : parseInt(value, 10) || 0;
    setEditedPreviewData(newData);
  };

  // Save edited preview data
  const savePreviewData = () => {
    setPreviewData(editedPreviewData);
    setIsEditingPreview(false);
    toast.success('Preview data updated');
  };

  // Cancel preview data editing
  const cancelPreviewDataEdit = () => {
    setIsEditingPreview(false);
    setEditedPreviewData(null);
  };

  // Update project data when model type changes
  useEffect(() => {
    setProjectData({
      ...projectData,
      royalty_model: {
        model_type: modelType,
        model_schema: modelSchema,
        configuration: {
          tasks_weight: tasksWeight,
          hours_weight: hoursWeight,
          difficulty_weight: difficultyWeight
        },
        is_pre_expense: isPreExpense,
        min_payout: minPayout,
        max_payout: maxPayout,
        revenue_share: revenueShare
        // Platform fee percentage removed as it's handled by platforms directly
      }
    });
  }, [modelType, modelSchema, tasksWeight, hoursWeight, difficultyWeight, isPreExpense, minPayout, maxPayout, revenueShare]);

  // Handle weight change
  const handleWeightChange = (type, value) => {
    const numValue = parseFloat(value);

    if (type === 'tasks') {
      setTasksWeight(numValue);

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const ratio = hoursWeight / (hoursWeight + difficultyWeight);

      setHoursWeight(parseFloat((remainingWeight * ratio).toFixed(2)));
      setDifficultyWeight(parseFloat((remainingWeight * (1 - ratio)).toFixed(2)));
    } else if (type === 'hours') {
      setHoursWeight(numValue);

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const ratio = tasksWeight / (tasksWeight + difficultyWeight);

      setTasksWeight(parseFloat((remainingWeight * ratio).toFixed(2)));
      setDifficultyWeight(parseFloat((remainingWeight * (1 - ratio)).toFixed(2)));
    } else if (type === 'difficulty') {
      setDifficultyWeight(numValue);

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const ratio = tasksWeight / (tasksWeight + hoursWeight);

      setTasksWeight(parseFloat((remainingWeight * ratio).toFixed(2)));
      setHoursWeight(parseFloat((remainingWeight * (1 - ratio)).toFixed(2)));
    }
  };

  // Reset weights to equal distribution
  const resetWeights = () => {
    setTasksWeight(33.33);
    setHoursWeight(33.33);
    setDifficultyWeight(33.34);
    toast.success('Weights reset to equal distribution');
  };

  // Calculate royalty distribution based on the selected model
  const calculateRoyaltyDistribution = () => {
    const { contributors } = previewData;
    let distribution = [];

    if (modelType === 'equal') {
      // Equal split model
      const equalShare = 100 / contributors.length;
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(equalShare.toFixed(2))
      }));
    } else if (modelType === 'task') {
      // Task-based model
      const totalTasks = contributors.reduce((sum, c) => sum + c.tasks, 0);
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(((contributor.tasks / totalTasks) * 100).toFixed(2))
      }));
    } else if (modelType === 'time') {
      // Time-based model
      const totalHours = contributors.reduce((sum, c) => sum + c.hours, 0);
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(((contributor.hours / totalHours) * 100).toFixed(2))
      }));
    } else if (modelType === 'role') {
      // Role-based model (simplified example)
      distribution = [
        { name: 'Alice (Developer)', percentage: 40 },
        { name: 'Bob (Designer)', percentage: 30 },
        { name: 'Charlie (Manager)', percentage: 30 }
      ];
    } else if (modelType === 'custom') {
      // Custom model (CoG) - Improved to prevent task count from heavily skewing results
      // Calculate scores for each contributor
      const totalTasks = contributors.reduce((sum, c) => sum + c.tasks, 0);
      const totalHours = contributors.reduce((sum, c) => sum + c.hours, 0);

      // Calculate weighted difficulty - using average difficulty instead of multiplying by tasks
      const avgDifficulties = contributors.map(c => ({
        name: c.name,
        avgDifficulty: c.difficulty // Already an average
      }));
      const totalAvgDifficulty = avgDifficulties.reduce((sum, c) => sum + c.avgDifficulty, 0);

      const scores = contributors.map(contributor => {
        // Task score - normalized to reduce impact of high task counts
        const taskScore = totalTasks > 0 ?
          (Math.sqrt(contributor.tasks) / contributors.reduce((sum, c) => sum + Math.sqrt(c.tasks), 0)) *
          (tasksWeight / 100) : 0;

        // Hour score - standard calculation
        const hourScore = totalHours > 0 ?
          (contributor.hours / totalHours) * (hoursWeight / 100) : 0;

        // Difficulty score - using average difficulty instead of multiplying by tasks
        const difficultyScore = totalAvgDifficulty > 0 ?
          (contributor.difficulty / totalAvgDifficulty) * (difficultyWeight / 100) : 0;

        return {
          name: contributor.name,
          score: taskScore + hourScore + difficultyScore,
          components: { taskScore, hourScore, difficultyScore } // Store components for debugging
        };
      });

      const totalScore = scores.reduce((sum, s) => sum + s.score, 0);

      distribution = scores.map(score => ({
        name: score.name,
        percentage: parseFloat(((score.score / totalScore) * 100).toFixed(2))
      }));
    }

    return distribution;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold text-foreground mb-2">Royalty Model Configuration</h2>
        <p className="text-default-500 mb-6">
          Choose how royalties will be distributed among contributors.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card
            className={`cursor-pointer transition-all ${modelType === 'custom' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-default-50'}`}
            onPress={() => setModelType('custom')}
            isPressable
          >
            <CardBody className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="text-2xl">⚙️</div>
                <h3 className="text-lg font-semibold">Custom Model</h3>
              </div>
              <p className="text-sm text-default-500">
                Create a custom model that combines multiple factors with weighted percentages.
              </p>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card
            className={`cursor-pointer transition-all ${modelType === 'equal' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-default-50'}`}
            onPress={() => setModelType('equal')}
            isPressable
          >
            <CardBody className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="text-2xl">⚖️</div>
                <h3 className="text-lg font-semibold">Equal Split</h3>
              </div>
              <p className="text-sm text-default-500">
                All contributors receive an equal share of royalties regardless of their contribution amount.
              </p>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card
            className={`cursor-pointer transition-all ${modelType === 'task' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-default-50'}`}
            onPress={() => setModelType('task')}
            isPressable
          >
            <CardBody className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="text-2xl">✅</div>
                <h3 className="text-lg font-semibold">Task-based</h3>
              </div>
              <p className="text-sm text-default-500">
                Royalties are distributed based on the number of tasks completed by each contributor.
              </p>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card
            className={`cursor-pointer transition-all ${modelType === 'time' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-default-50'}`}
            onPress={() => setModelType('time')}
            isPressable
          >
            <CardBody className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="text-2xl">⏰</div>
                <h3 className="text-lg font-semibold">Time-based</h3>
              </div>
              <p className="text-sm text-default-500">
                Royalties are distributed based on the hours tracked by each contributor.
              </p>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Card
            className={`cursor-pointer transition-all ${modelType === 'role' ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-default-50'}`}
            onPress={() => setModelType('role')}
            isPressable
          >
            <CardBody className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="text-2xl">👥</div>
                <h3 className="text-lg font-semibold">Role-based</h3>
              </div>
              <p className="text-sm text-default-500">
                Royalties are distributed based on predefined percentages for each role.
              </p>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {modelType === 'custom' && (
        <Card className="mt-6">
          <CardBody className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">Custom Model Builder</h3>
              <div className="flex gap-3">
                <Select
                  size="sm"
                  selectedKeys={[modelSchema]}
                  onSelectionChange={(keys) => setModelSchema(Array.from(keys)[0])}
                  className="w-64"
                >
                  <SelectItem key="cog">CoG Model (Tasks-Hours-Difficulty)</SelectItem>
                  <SelectItem key="studio-a">Studio A Algorithm (Coming Soon)</SelectItem>
                  <SelectItem key="studio-b">Studio B Algorithm (Coming Soon)</SelectItem>
                </Select>
                <Button
                  size="sm"
                  variant="bordered"
                  onPress={resetWeights}
                >
                  Reset to Equal
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Tasks Weight</span>
                      <Chip size="sm" variant="flat" color="primary">
                        ℹ️
                      </Chip>
                    </div>
                    <span className="font-semibold text-primary">{tasksWeight}%</span>
                  </div>
                  <Slider
                    size="lg"
                    step={1}
                    minValue={0}
                    maxValue={100}
                    value={tasksWeight}
                    onChange={(value) => handleWeightChange('tasks', value)}
                    className="w-full"
                    color="primary"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Hours Weight</span>
                      <Chip size="sm" variant="flat" color="success">
                        ℹ️
                      </Chip>
                    </div>
                    <span className="font-semibold text-success">{hoursWeight}%</span>
                  </div>
                  <Slider
                    size="lg"
                    step={1}
                    minValue={0}
                    maxValue={100}
                    value={hoursWeight}
                    onChange={(value) => handleWeightChange('hours', value)}
                    className="w-full"
                    color="success"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Difficulty Weight</span>
                      <Chip size="sm" variant="flat" color="warning">
                        ℹ️
                      </Chip>
                    </div>
                    <span className="font-semibold text-warning">{difficultyWeight}%</span>
                  </div>
                  <Slider
                    size="lg"
                    step={1}
                    minValue={0}
                    maxValue={100}
                    value={difficultyWeight}
                    onChange={(value) => handleWeightChange('difficulty', value)}
                    className="w-full"
                    color="warning"
                  />
                </div>

                <Divider className="my-6" />

                <div className="space-y-4">
                  <h4 className="text-lg font-medium">Financial Parameters</h4>

                  <Input
                    label="Revenue Share Percentage"
                    type="number"
                    min={1}
                    max={100}
                    value={revenueShare.toString()}
                    onChange={(e) => setRevenueShare(parseInt(e.target.value) || 50)}
                    endContent={<span className="text-default-400">%</span>}
                    variant="bordered"
                    description="Percentage of revenue that will be shared with contributors."
                    classNames={{
                      input: "text-center",
                      inputWrapper: "hover:border-primary focus-within:border-primary"
                    }}
                  />

                  <Input
                    label="Minimum Payout Threshold"
                    type="number"
                    min={0}
                    step={10}
                    value={(minPayout / 100).toString()}
                    onChange={(e) => setMinPayout(Math.round(parseFloat(e.target.value) * 100) || 0)}
                    startContent={<span className="text-default-400">$</span>}
                    variant="bordered"
                    description="Minimum amount that must be accumulated before a payout is processed."
                    classNames={{
                      input: "text-right",
                      inputWrapper: "hover:border-primary focus-within:border-primary"
                    }}
                  />

                  <Input
                    label="Maximum Payout"
                    type="number"
                    min={0}
                    step={1000}
                    value={(maxPayout / 100).toString()}
                    onChange={(e) => setMaxPayout(Math.round(parseFloat(e.target.value) * 100) || 0)}
                    startContent={<span className="text-default-400">$</span>}
                    variant="bordered"
                    description="Maximum amount that can be paid out in total (0 for no limit)."
                    classNames={{
                      input: "text-right",
                      inputWrapper: "hover:border-primary focus-within:border-primary"
                    }}
                  />
                </div>

                <Card className="mt-6 bg-default-50">
                  <CardBody className="p-4">
                    <h4 className="text-md font-medium mb-2">Platform Fee Information</h4>
                    <p className="text-sm text-default-500">
                      Platform fees are specific to each platform and are automatically calculated before the money reaches Royaltea's escrow.
                      You don't need to configure platform fees in the royalty model.
                    </p>
                  </CardBody>
                </Card>
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-medium">Weight Distribution</h4>
                <div className="bg-white rounded-lg p-4">
                  <PieChart
                    data={[
                      { title: 'Tasks', value: tasksWeight, color: '#3b82f6' },
                      { title: 'Hours', value: hoursWeight, color: '#10b981' },
                      { title: 'Difficulty', value: difficultyWeight, color: '#f59e0b' }
                    ]}
                    lineWidth={60}
                    paddingAngle={2}
                    rounded
                    label={({ dataEntry }) => `${dataEntry.title}`}
                    labelStyle={{
                      fontSize: '5px',
                      fontFamily: 'sans-serif',
                      fill: '#fff'
                    }}
                    labelPosition={70}
                    style={{ height: '200px' }}
                  />
                </div>
                <div className="flex justify-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-sm"></div>
                    <span className="text-sm">Tasks</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                    <span className="text-sm">Hours</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-yellow-500 rounded-sm"></div>
                    <span className="text-sm">Difficulty</span>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {modelType === 'custom' && (
        <Card className="mt-6 bg-content1 border border-default-200">
          <CardBody className="p-6">
            <h4 className="text-lg font-medium mb-4 text-foreground">Algorithm Information</h4>

            {modelSchema === 'cog' && (
              <div className="space-y-4">
                <div>
                  <p className="font-medium mb-2">
                    CoG Model (Tasks-Hours-Difficulty) - Our recommended algorithm that balances:
                  </p>
                  <ul className="space-y-1 text-sm text-default-600 ml-4">
                    <li><strong>Tasks:</strong> Number of tasks completed (using square root normalization to prevent skewing)</li>
                    <li><strong>Hours:</strong> Time spent on contributions</li>
                    <li><strong>Difficulty:</strong> Average complexity of completed tasks</li>
                  </ul>
                  <p className="text-sm text-default-500 mt-3">
                    This balanced approach ensures fair compensation based on both quantity and quality of work.
                  </p>
                </div>

                <Card className="bg-content2 border border-default-200">
                  <CardBody className="p-4">
                    <p className="font-medium text-sm mb-2 text-foreground">Technical Details:</p>
                    <ul className="space-y-1 text-xs text-default-700 ml-4">
                      <li>Task score uses square root normalization to prevent contributors with many small tasks from dominating</li>
                      <li>Difficulty is calculated using the average difficulty rating rather than multiplying by task count</li>
                      <li>This creates a more balanced distribution that rewards both quantity and quality</li>
                    </ul>
                  </CardBody>
                </Card>
              </div>
            )}

            {modelSchema === 'studio-a' && (
              <div>
                <p className="font-medium mb-2">
                  Studio A Algorithm - Coming soon
                </p>
                <p className="text-sm text-default-500">
                  This algorithm will be available in a future update. It uses a different weighting system focused on specific studio needs.
                </p>
              </div>
            )}

            {modelSchema === 'studio-b' && (
              <div>
                <p className="font-medium mb-2">
                  Studio B Algorithm - Coming soon
                </p>
                <p className="text-sm text-default-500">
                  This algorithm will be available in a future update. It uses a different approach to contribution valuation.
                </p>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {modelType === 'custom' && modelSchema === 'cog' && (
        <div className="default-difficulty-container mt-4 p-3 border rounded bg-light">
          <h4 className="h6 mb-3">Default Task Types & Difficulty Scores</h4>
          <p className="text-muted small mb-3">
            These default difficulty scores will be used when tracking contributions. You can customize these later.
          </p>

          <div className="table-responsive">
            <table className="table table-sm">
              <thead>
                <tr>
                  <th>Task Type</th>
                  <th>Difficulty Level</th>
                  <th>Points</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Programming</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
                <tr>
                  <td>Art</td>
                  <td>Hard</td>
                  <td>10</td>
                </tr>
                <tr>
                  <td>QA</td>
                  <td>Easy</td>
                  <td>2</td>
                </tr>
                <tr>
                  <td>Writing</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
                <tr>
                  <td>Engineering</td>
                  <td>Hard</td>
                  <td>10</td>
                </tr>
                <tr>
                  <td>Admin</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
                <tr>
                  <td>Design</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Royalty Distribution Preview */}
      <div className="royalty-preview-container mt-4 p-3 border rounded">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <h4 className="h5 mb-0">Royalty Distribution Preview</h4>
          {!isEditingPreview ? (
            <button
              type="button"
              className="btn btn-sm btn-outline-primary"
              onClick={handleEditPreviewData}
            >
              <i className="bi bi-pencil me-1"></i> Edit Sample Data
            </button>
          ) : (
            <div>
              <button
                type="button"
                className="btn btn-sm btn-outline-secondary me-2"
                onClick={cancelPreviewDataEdit}
              >
                Cancel
              </button>
              <button
                type="button"
                className="btn btn-sm btn-primary"
                onClick={savePreviewData}
              >
                Save Changes
              </button>
            </div>
          )}
        </div>
        <p className="text-muted small mb-3">
          This is a preview of how royalties would be distributed based on the selected model and sample data.
        </p>

        <div className="row">
          <div className="col-md-6">
            <h5 className="h6 mb-3">Sample Contributor Data</h5>
            <div className="table-responsive">
              <table className="table table-sm table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>Contributor</th>
                    <th>Tasks</th>
                    <th>Hours</th>
                    <th>Avg. Difficulty</th>
                  </tr>
                </thead>
                <tbody>
                  {!isEditingPreview ? (
                    // Display mode
                    previewData.contributors.map((contributor, index) => (
                      <tr key={index}>
                        <td>{contributor.name}</td>
                        <td>{contributor.tasks}</td>
                        <td>{contributor.hours}</td>
                        <td>{contributor.difficulty}</td>
                      </tr>
                    ))
                  ) : (
                    // Edit mode
                    editedPreviewData.contributors.map((contributor, index) => (
                      <tr key={index}>
                        <td>
                          <input
                            type="text"
                            className="form-control form-control-sm"
                            value={contributor.name}
                            onChange={(e) => handleContributorChange(index, 'name', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={contributor.tasks}
                            min="0"
                            onChange={(e) => handleContributorChange(index, 'tasks', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={contributor.hours}
                            min="0"
                            onChange={(e) => handleContributorChange(index, 'hours', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={contributor.difficulty}
                            min="1"
                            max="10"
                            onChange={(e) => handleContributorChange(index, 'difficulty', e.target.value)}
                          />
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <div className="col-md-6">
            <h5 className="h6 mb-3">Calculated Distribution</h5>
            <div className="table-responsive">
              <table className="table table-sm table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>Contributor</th>
                    <th>Percentage</th>
                    <th>Visual</th>
                  </tr>
                </thead>
                <tbody>
                  {calculateRoyaltyDistribution().map((distribution, index) => (
                    <tr key={index}>
                      <td>{distribution.name}</td>
                      <td>{distribution.percentage}%</td>
                      <td>
                        <div className="progress" style={{ height: '15px' }}>
                          <div
                            className="progress-bar"
                            role="progressbar"
                            style={{
                              width: `${distribution.percentage}%`,
                              backgroundColor: index === 0 ? '#3b82f6' : index === 1 ? '#10b981' : '#f59e0b'
                            }}
                            aria-valuenow={distribution.percentage}
                            aria-valuemin="0"
                            aria-valuemax="100"
                          ></div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <Card className="mt-6 bg-primary/5 border-primary/20">
        <CardBody className="p-6">
          <div className="flex gap-4">
            <div className="text-2xl">ℹ️</div>
            <div>
              <h4 className="text-lg font-medium mb-2">How Royalty Models Work</h4>
              <p className="text-sm text-default-600">
                {modelType === 'equal' && (
                  "With the Equal Split model, all contributors receive an equal share of royalties. For example, if there are 5 contributors, each will receive 20% of the royalties."
                )}
                {modelType === 'task' && (
                  "With the Task-based model, royalties are distributed based on the number of tasks completed by each contributor. Contributors who complete more tasks receive a larger share of royalties."
                )}
                {modelType === 'time' && (
                  "With the Time-based model, royalties are distributed based on the hours tracked by each contributor. Contributors who track more hours receive a larger share of royalties."
                )}
                {modelType === 'role' && (
                  "With the Role-based model, royalties are distributed based on predefined percentages for each role. For example, Developers might receive 40%, Designers 30%, and Project Managers 30%."
                )}
                {modelType === 'custom' && modelSchema === 'cog' && (
                  "With the CoG model (Tasks-Hours-Difficulty), royalties are distributed based on a weighted combination of tasks completed, hours tracked, and task difficulty. This balanced approach uses square root normalization for tasks and average difficulty ratings to prevent any single factor from dominating the calculation."
                )}
                {modelType === 'custom' && modelSchema === 'studio-a' && (
                  "The Studio A Algorithm (coming soon) will offer an alternative approach to royalty distribution based on studio feedback. This algorithm will have its own unique parameters and weighting system."
                )}
                {modelType === 'custom' && modelSchema === 'studio-b' && (
                  "The Studio B Algorithm (coming soon) will provide another option for royalty distribution with different parameters tailored to specific project types and team structures."
                )}
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RoyaltyModel;
