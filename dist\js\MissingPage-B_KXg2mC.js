import{j as e,m as a,c as x,b as p,e as h,h as s}from"./chunk-DX4Z_LyS.js";import{a as y,u as b}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";const N=({pageName:r="Unknown Page",description:c="This page is currently under development.",suggestedRoutes:i=["/"],canvasId:l=null})=>{const n=y(),o=b(),d=()=>{n("/")},m=()=>{window.history.back()};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-6",children:e.jsx(a.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},transition:{duration:.5},className:"max-w-2xl w-full",children:e.jsx(x,{className:"bg-white/80 dark:bg-slate-800/80 backdrop-blur-md border border-white/20",children:e.jsxs(p,{className:"p-8 text-center",children:[e.jsx(a.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"text-8xl mb-6",children:"🚧"}),e.jsx(a.h1,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-3xl font-bold text-slate-800 dark:text-white mb-4",children:r}),e.jsx(a.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.4},className:"mb-6",children:e.jsx(h,{color:"warning",variant:"flat",size:"lg",children:"Under Development"})}),e.jsx(a.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"text-lg text-slate-600 dark:text-slate-300 mb-8",children:c}),e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},className:"bg-slate-100 dark:bg-slate-700 rounded-lg p-4 mb-8 text-left",children:[e.jsx("h3",{className:"font-semibold text-slate-800 dark:text-white mb-3",children:"Development Information"}),e.jsxs("div",{className:"space-y-2 text-sm text-slate-600 dark:text-slate-300",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Current Route:"}),e.jsx("code",{className:"bg-slate-200 dark:bg-slate-600 px-2 py-1 rounded text-xs",children:o.pathname})]}),l&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Canvas ID:"}),e.jsx("code",{className:"bg-slate-200 dark:bg-slate-600 px-2 py-1 rounded text-xs",children:l})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Status:"}),e.jsx("span",{className:"text-orange-600 dark:text-orange-400 font-medium",children:"Planned for Implementation"})]})]})]}),e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.7},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[e.jsx(s,{color:"primary",size:"lg",onClick:d,className:"min-w-32",children:"🏠 Go Home"}),e.jsx(s,{color:"default",variant:"bordered",size:"lg",onClick:m,className:"min-w-32",children:"← Go Back"})]}),i.length>1&&e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.8},className:"mt-8 pt-6 border-t border-slate-200 dark:border-slate-600",children:[e.jsx("h4",{className:"text-sm font-medium text-slate-600 dark:text-slate-400 mb-3",children:"You might be looking for:"}),e.jsx("div",{className:"flex flex-wrap gap-2 justify-center",children:i.slice(1).map((t,f)=>e.jsx(s,{size:"sm",variant:"light",onClick:()=>n(t),className:"text-xs",children:t==="/"?"Dashboard":t.replace("/","").replace("-"," ")},t))})]}),e.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1},className:"mt-8 pt-4 border-t border-slate-200 dark:border-slate-600",children:e.jsxs("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:["This page is part of the Royaltea platform's ongoing development.",e.jsx("br",{}),"Check back soon for updates!"]})})]})})})})};export{N as default};
