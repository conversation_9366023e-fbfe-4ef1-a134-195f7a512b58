/* Minimal base styles - let <PERSON><PERSON> handle everything else */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden; /* Prevent horizontal scroll but allow vertical */
  overflow-y: auto; /* Allow vertical scrolling when needed */
  max-width: 100vw; /* Prevent body from exceeding viewport width */
}

/* Ensure html handles overflow properly */
html {
  overflow-x: hidden; /* Prevent horizontal scroll */
  overflow-y: auto; /* Allow vertical scrolling */
  height: 100%;
  width: 100%;
  max-width: 100vw; /* Prevent html from exceeding viewport width */
}

/* Ensure root container doesn't overflow */
#root {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Fix screen reader elements to not cause overflow */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Content scrolling styles */
.content-scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin; /* Show thin scrollbars in content areas */
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.content-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.content-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.content-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Navigation areas - hide scrollbars completely */
.navigation-area {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navigation-area::-webkit-scrollbar {
  display: none;
}

/* Allow scrolling within canvas content when in content mode */
.canvas-content {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.canvas-content::-webkit-scrollbar {
  width: 6px;
}

.canvas-content::-webkit-scrollbar-track {
  background: transparent;
}

.canvas-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.canvas-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.pointer {
  cursor: pointer;
}

/* Prevent browser extension interference with forms */
input[data-lpignore="true"],
textarea[data-lpignore="true"],
select[data-lpignore="true"] {
  -webkit-text-security: none !important;
  background-image: none !important;
  background-color: transparent !important;
}

/* Prevent password manager icons from appearing */
input[data-form-type="financial"]::-webkit-credentials-auto-fill-button,
input[data-form-type="reference"]::-webkit-credentials-auto-fill-button,
input[data-form-type="description"]::-webkit-credentials-auto-fill-button {
  display: none !important;
  visibility: hidden !important;
}

/* Prevent large text elements from overflowing */
.text-6xl, .text-5xl, .text-4xl, .text-3xl {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Ensure all containers respect viewport width */
* {
  box-sizing: border-box;
}

div, section, main, article, aside, header, footer, nav {
  max-width: 100%;
  overflow-wrap: break-word;
}

/* Fix dropdown and select elements */
select, .dropdown-menu, [role="listbox"], [role="menu"] {
  max-width: 100vw !important;
  overflow-x: hidden !important;
}

/* Fix dropdown containers that are positioned absolutely */
div[role="listbox"], div[role="menu"], .dropdown-content {
  max-width: calc(100vw - 20px) !important;
  overflow-x: hidden !important;
  word-wrap: break-word !important;
}

/* Fix any absolutely positioned divs that might be dropdowns */
div[style*="position: absolute"] {
  max-width: calc(100vw - 20px) !important;
  overflow-x: hidden !important;
}

/* Ensure text content in dropdowns wraps properly */
div[role="listbox"] span, div[role="menu"] span, .dropdown-content span {
  max-width: 100% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  white-space: normal !important;
}

/* Fix text-center containers that might overflow */
.text-center {
  max-width: 100% !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

/* Ensure all containers have proper box-sizing and width constraints */
.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
  max-width: calc(100vw - 48px) !important;
  margin-left: auto !important;
  margin-right: auto !important;
  box-sizing: border-box !important;
}

/* Fix any remaining wide elements */
.w-full {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Error Boundary Styles */
.error-boundary-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #f8f9fa;
}

.error-container {
  max-width: 600px;
  padding: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-container h2 {
  color: #dc3545;
  margin-bottom: 16px;
  font-size: 24px;
}

.error-container p {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 16px;
}

.error-details {
  text-align: left;
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.error-details summary {
  cursor: pointer;
  font-weight: bold;
  color: #495057;
  margin-bottom: 10px;
}

.error-details pre {
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 12px;
  color: #dc3545;
  margin: 10px 0;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.error-reload-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.error-reload-button:hover {
  background-color: #0056b3;
}


