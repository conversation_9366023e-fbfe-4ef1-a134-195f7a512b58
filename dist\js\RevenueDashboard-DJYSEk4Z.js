var O=Object.defineProperty,U=Object.defineProperties;var H=Object.getOwnPropertyDescriptors;var L=Object.getOwnPropertySymbols;var X=Object.prototype.hasOwnProperty,Y=Object.prototype.propertyIsEnumerable;var R=(t,a,r)=>a in t?O(t,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[a]=r,z=(t,a)=>{for(var r in a||(a={}))X.call(a,r)&&R(t,r,a[r]);if(L)for(var r of L(a))Y.call(a,r)&&R(t,r,a[r]);return t},F=(t,a)=>U(t,H(a));var K=(t,a,r)=>new Promise((p,d)=>{var g=n=>{try{s(r.next(n))}catch(o){d(o)}},m=n=>{try{s(r.throw(n))}catch(o){d(o)}},s=n=>n.done?p(n.value):Promise.resolve(n.value).then(g,m);s((r=r.apply(t,a)).next())});import{j as e,c as h,w as _,e as M,b as u,a as Q,h as C,r as W}from"./chunk-DX4Z_LyS.js";import{j as V,s as A}from"../assets/main-CGUKzV0x.js";import{f as v,a as Z}from"./chunk-CqI-o4WX.js";import{n as ee,B as w,X as T,Y as D,q as se,R as I,s as $,T as S,t as B,w as te,v as ae,o as E,r as re,p as ne}from"./chunk-Crqd8jVX.js";import{Q as q,O as ie,q as oe,aC as le,aB as ce,aD as G,aE as de,aF as me}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";var xe=ee({chartName:"BarChart",GraphicalChild:w,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:T},{axisType:"yAxis",AxisComp:D}],formatAxisMap:se});const he=({data:t=[],height:a=300,showLegend:r=!0,showGoalLine:p=!0,goalAmount:d=4e3,className:g="",title:m="Earnings Breakdown"})=>{const s=[{month:"Jan",missions:1200,bounties:800,projects:400,orbs:150,total:2550,goal:4e3},{month:"Feb",missions:1500,bounties:1200,projects:500,orbs:180,total:3380,goal:4e3},{month:"Mar",missions:1100,bounties:900,projects:800,orbs:200,total:3e3,goal:4e3},{month:"Apr",missions:1800,bounties:1500,projects:600,orbs:220,total:4120,goal:4e3},{month:"May",missions:1600,bounties:1100,projects:900,orbs:250,total:3850,goal:4e3},{month:"Jun",missions:2e3,bounties:1800,projects:1e3,orbs:300,total:5100,goal:4e3}],n=t.length>0?t:s,o=({active:x,payload:j,label:y})=>{if(x&&j&&j.length){const c=j.reduce((l,f)=>l+f.value,0);return e.jsxs("div",{className:"bg-white dark:bg-slate-800 p-4 border border-default-200 rounded-lg shadow-lg",children:[e.jsx("p",{className:"font-semibold text-default-900 dark:text-default-100 mb-2",children:y}),e.jsxs("p",{className:"text-sm font-medium mb-2",children:["Total: $",c.toLocaleString()]}),j.map((l,f)=>e.jsxs("p",{style:{color:l.color},className:"text-sm",children:[l.name,": $",l.value.toLocaleString()]},f)),p&&e.jsxs("p",{className:"text-sm text-default-600 mt-2 pt-2 border-t border-default-200",children:["Goal: $",d.toLocaleString()]})]})}return null};return e.jsxs(h,{className:`h-full ${g}`,children:[e.jsx(_,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"💰"}),e.jsx("h3",{className:"text-lg font-semibold",children:m})]}),e.jsx(M,{color:"success",variant:"flat",size:"sm",children:"Earnings"})]})}),e.jsx(u,{className:"pt-0",children:e.jsx("div",{style:{width:"100%",height:a},children:e.jsx(I,{children:e.jsxs(xe,{data:n,margin:{top:20,right:30,left:20,bottom:5},children:[e.jsx($,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(T,{dataKey:"month",stroke:"#64748b",fontSize:12}),e.jsx(D,{stroke:"#64748b",fontSize:12,tickFormatter:x=>`$${x.toLocaleString()}`}),e.jsx(S,{content:e.jsx(o,{})}),r&&e.jsx(B,{}),p&&e.jsx(te,{y:d,stroke:"#ef4444",strokeDasharray:"5 5",label:{value:"Goal",position:"topRight"}}),e.jsx(w,{dataKey:"missions",stackId:"earnings",fill:"#10b981",name:"Missions",radius:[0,0,0,0]}),e.jsx(w,{dataKey:"bounties",stackId:"earnings",fill:"#3b82f6",name:"Bounties",radius:[0,0,0,0]}),e.jsx(w,{dataKey:"projects",stackId:"earnings",fill:"#8b5cf6",name:"Projects",radius:[0,0,0,0]}),e.jsx(w,{dataKey:"orbs",stackId:"earnings",fill:"#f59e0b",name:"ORB Points",radius:[2,2,0,0]})]})})})})]})},ue=({data:t=[],height:a=300,showLegend:r=!0,showProjections:p=!0,className:d="",title:g="Payout History"})=>{const[m,s]=Q.useState("line"),n=[{month:"Jan",payouts:2200,pending:350,cumulative:2200,projected:2400,status:"completed"},{month:"Feb",payouts:2800,pending:580,cumulative:5e3,projected:3e3,status:"completed"},{month:"Mar",payouts:2400,pending:600,cumulative:7400,projected:2600,status:"completed"},{month:"Apr",payouts:3500,pending:620,cumulative:10900,projected:3200,status:"completed"},{month:"May",payouts:3200,pending:850,cumulative:14100,projected:3400,status:"processing"},{month:"Jun",payouts:0,pending:4200,cumulative:14100,projected:4500,status:"pending"}],o=t.length>0?t:n,x=({active:c,payload:l,label:f})=>{if(c&&l&&l.length){const N=l[0].payload;return e.jsxs("div",{className:"bg-white dark:bg-slate-800 p-4 border border-default-200 rounded-lg shadow-lg",children:[e.jsx("p",{className:"font-semibold text-default-900 dark:text-default-100 mb-2",children:f}),l.map((k,P)=>e.jsxs("p",{style:{color:k.color},className:"text-sm",children:[k.name,": $",k.value.toLocaleString()]},P)),e.jsx("div",{className:"mt-2 pt-2 border-t border-default-200",children:e.jsxs("p",{className:"text-sm text-default-600",children:["Status: ",e.jsx("span",{className:`font-medium ${N.status==="completed"?"text-green-600":N.status==="processing"?"text-yellow-600":"text-red-600"}`,children:N.status.charAt(0).toUpperCase()+N.status.slice(1)})]})})]})}return null},j=()=>e.jsxs(ae,{data:o,margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx($,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(T,{dataKey:"month",stroke:"#64748b",fontSize:12}),e.jsx(D,{stroke:"#64748b",fontSize:12,tickFormatter:c=>`$${c.toLocaleString()}`}),e.jsx(S,{content:e.jsx(x,{})}),r&&e.jsx(B,{}),e.jsx(E,{type:"monotone",dataKey:"payouts",stroke:"#10b981",strokeWidth:3,dot:{fill:"#10b981",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#10b981",strokeWidth:2},name:"Completed Payouts"}),e.jsx(E,{type:"monotone",dataKey:"pending",stroke:"#f59e0b",strokeWidth:2,dot:{fill:"#f59e0b",strokeWidth:2,r:3},activeDot:{r:5,stroke:"#f59e0b",strokeWidth:2},name:"Pending Payouts",strokeDasharray:"5 5"}),p&&e.jsx(E,{type:"monotone",dataKey:"projected",stroke:"#8b5cf6",strokeWidth:2,dot:{fill:"#8b5cf6",strokeWidth:2,r:3},activeDot:{r:5,stroke:"#8b5cf6",strokeWidth:2},name:"Projected",strokeDasharray:"3 3"})]}),y=()=>e.jsxs(re,{data:o,margin:{top:10,right:30,left:0,bottom:0},children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"cumulativeGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:"#3b82f6",stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:"#3b82f6",stopOpacity:.1})]})}),e.jsx($,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(T,{dataKey:"month",stroke:"#64748b",fontSize:12}),e.jsx(D,{stroke:"#64748b",fontSize:12,tickFormatter:c=>`$${c.toLocaleString()}`}),e.jsx(S,{content:e.jsx(x,{})}),r&&e.jsx(B,{}),e.jsx(ne,{type:"monotone",dataKey:"cumulative",stroke:"#3b82f6",strokeWidth:2,fill:"url(#cumulativeGradient)",name:"Cumulative Payouts"})]});return e.jsxs(h,{className:`h-full ${d}`,children:[e.jsx(_,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"💳"}),e.jsx("h3",{className:"text-lg font-semibold",children:g})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{size:"sm",variant:m==="line"?"solid":"flat",color:"primary",onPress:()=>s("line"),children:"Trends"}),e.jsx(C,{size:"sm",variant:m==="area"?"solid":"flat",color:"primary",onPress:()=>s("area"),children:"Cumulative"})]})]})}),e.jsx(u,{className:"pt-0",children:e.jsx("div",{style:{width:"100%",height:a},children:e.jsx(I,{children:m==="line"?j():y()})})})]})},_e=()=>{const{currentUser:t}=V(),[a,r]=W.useState({totalEarnings:0,pendingPayments:0,thisMonthEarnings:0,escrowBalance:0,recentTransactions:[],paymentMethods:[],loading:!0});W.useEffect(()=>{t&&p()},[t]);const p=()=>K(null,null,function*(){try{const{data:s,error:n}=yield A.from("payment_transactions").select(`
          *,
          from_user:from_user_id(full_name, avatar_url),
          to_user:to_user_id(full_name, avatar_url)
        `).or(`from_user_id.eq.${t.id},to_user_id.eq.${t.id}`).order("created_at",{ascending:!1}).limit(10);if(n)throw n;const{data:o,error:x}=yield A.from("escrow_accounts").select("*").eq("project_id",t.id);if(x)throw x;const{data:j,error:y}=yield A.from("teller_accounts").select("*").eq("user_id",t.id).eq("is_active",!0);if(y)throw y;const c=(s==null?void 0:s.filter(i=>i.to_user_id===t.id&&i.status==="completed"))||[],l=c.reduce((i,b)=>i+parseFloat(b.amount),0),f=new Date;f.setDate(1);const N=c.filter(i=>new Date(i.created_at)>=f).reduce((i,b)=>i+parseFloat(b.amount),0),P=((s==null?void 0:s.filter(i=>i.to_user_id===t.id&&["pending","processing"].includes(i.status)))||[]).reduce((i,b)=>i+parseFloat(b.amount),0),J=(o==null?void 0:o.reduce((i,b)=>i+parseFloat(b.current_balance),0))||0;r({totalEarnings:l,pendingPayments:P,thisMonthEarnings:N,escrowBalance:J,recentTransactions:s||[],paymentMethods:j||[],loading:!1})}catch(s){r(n=>F(z({},n),{loading:!1}))}}),d=s=>s.to_user_id===t.id?e.jsx(de,{className:"w-4 h-4 text-green-500"}):e.jsx(me,{className:"w-4 h-4 text-red-500"}),g=s=>{const n=parseFloat(s.amount);return s.to_user_id===t.id?`+${v(n)}`:`-${v(n)}`},m=s=>({completed:"success",pending:"warning",processing:"primary",failed:"danger",cancelled:"default"})[s]||"default";return a.loading?e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[...Array(4)].map((s,n)=>e.jsx(h,{className:"animate-pulse",children:e.jsxs(u,{className:"p-6",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded mb-2"}),e.jsx("div",{className:"h-8 bg-gray-200 rounded"})]})},n))})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(h,{className:"border-l-4 border-l-green-500",children:e.jsx(u,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Total Earnings"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:v(a.totalEarnings)})]}),e.jsx(q,{className:"w-8 h-8 text-green-500"})]})})}),e.jsx(h,{className:"border-l-4 border-l-blue-500",children:e.jsx(u,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"This Month"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:v(a.thisMonthEarnings)})]}),e.jsx(ie,{className:"w-8 h-8 text-blue-500"})]})})}),e.jsx(h,{className:"border-l-4 border-l-orange-500",children:e.jsx(u,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Pending Payments"}),e.jsx("p",{className:"text-2xl font-bold text-orange-600",children:v(a.pendingPayments)})]}),e.jsx(oe,{className:"w-8 h-8 text-orange-500"})]})})}),e.jsx(h,{className:"border-l-4 border-l-purple-500",children:e.jsx(u,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Escrow Balance"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:v(a.escrowBalance)})]}),e.jsx(le,{className:"w-8 h-8 text-purple-500"})]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(he,{data:[],height:300,showLegend:!0,showGoalLine:!0,goalAmount:5e3,title:"Monthly Earnings Breakdown"}),e.jsx(ue,{data:[],height:300,showLegend:!0,showProjections:!0,title:"Payout History & Projections"})]}),e.jsxs(h,{children:[e.jsx(_,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center gap-2",children:[e.jsx(ce,{className:"w-5 h-5"}),"Payment Methods"]}),e.jsx(C,{size:"sm",color:"primary",variant:"flat",children:"Add Account"})]})}),e.jsx(u,{children:a.paymentMethods.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(G,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No payment methods connected"}),e.jsx(C,{color:"primary",children:"Connect Bank Account"})]}):e.jsx("div",{className:"space-y-3",children:a.paymentMethods.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(G,{className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.account_name}),e.jsxs("p",{className:"text-sm text-gray-600",children:[s.institution_name," • ",s.account_type]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(M,{size:"sm",color:s.is_verified?"success":"warning",variant:"flat",children:s.is_verified?"Verified":"Pending"})})]},s.id))})})]}),e.jsxs(h,{children:[e.jsx(_,{className:"pb-3",children:e.jsx("h3",{className:"text-lg font-semibold",children:"Recent Transactions"})}),e.jsx(u,{children:a.recentTransactions.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(q,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"No transactions yet"})]}):e.jsx("div",{className:"space-y-3",children:a.recentTransactions.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[d(s),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.description||`${s.payment_method} Transfer`}),e.jsx("p",{className:"text-sm text-gray-600",children:Z(s.created_at)})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:`font-semibold ${s.to_user_id===t.id?"text-green-600":"text-red-600"}`,children:g(s)}),e.jsx(M,{size:"sm",color:m(s.status),variant:"flat",children:s.status})]})]},s.id))})})]})]})};export{_e as default};
