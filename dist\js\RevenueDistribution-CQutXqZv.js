var Y=Object.defineProperty,Z=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var O=Object.getOwnPropertySymbols;var ee=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable;var q=(i,n,r)=>n in i?Y(i,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[n]=r,F=(i,n)=>{for(var r in n||(n={}))ee.call(n,r)&&q(i,r,n[r]);if(O)for(var r of O(n))se.call(n,r)&&q(i,r,n[r]);return i},R=(i,n)=>Z(i,I(n));var P=(i,n,r)=>new Promise((T,f)=>{var y=l=>{try{g(r.next(l))}catch(_){f(_)}},k=l=>{try{g(r.throw(l))}catch(_){f(_)}},g=l=>l.done?T(l.value):Promise.resolve(l.value).then(y,k);g((r=r.apply(i,n)).next())});import{r as h,j as e,c as b,b as v,w as te,h as x,m as ae,e as re,G as ne,y as ie,C as le,l as oe,n as ce,o as de,p as N,u as ue,v as B,E as me}from"./chunk-DX4Z_LyS.js";import{U as he,s as $}from"../assets/main-CGUKzV0x.js";import{c as u}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const ye=({projectId:i,className:n=""})=>{const{currentUser:r}=h.useContext(he),[T,f]=h.useState(!0),[y,k]=h.useState([]),[g,l]=h.useState(!1),[_,L]=h.useState(null),[p,E]=h.useState(""),[o,J]=h.useState("percentage"),[m,C]=h.useState([{id:1,name:"",email:"",percentage:0,amount:0,role:""}]);h.useEffect(()=>{r&&i&&z()},[r,i]);const z=()=>P(null,null,function*(){try{f(!0);const{data:{session:s}}=yield $.auth.getSession(),t=s==null?void 0:s.access_token;if(!t){u.error("Authentication required");return}const a=yield fetch(`/.netlify/functions/financial-transactions?project_id=${i}&type=revenue_distribution`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(a.ok){const c=yield a.json();k(c.distributions||[])}else throw new Error("Failed to fetch revenue distributions")}catch(s){u.error("Failed to load revenue distributions")}finally{f(!1)}}),G=()=>{C([...m,{id:Date.now(),name:"",email:"",percentage:0,amount:0,role:""}])},K=s=>{C(m.filter(t=>t.id!==s))},w=(s,t,a)=>{C(m.map(c=>c.id===s?R(F({},c),{[t]:a}):c))},U=()=>{const s=parseFloat(p)||0;return m.map(t=>R(F({},t),{amount:o==="percentage"?s*(parseFloat(t.percentage)||0)/100:parseFloat(t.amount)||0}))},D=()=>m.reduce((s,t)=>s+(parseFloat(t.percentage)||0),0),M=()=>U().reduce((t,a)=>t+a.amount,0),V=()=>P(null,null,function*(){try{const s=parseFloat(p);if(!s||s<=0){u.error("Please enter a valid total revenue amount");return}if(o==="percentage"&&D()!==100){u.error("Total percentage must equal 100%");return}if(m.filter(d=>d.name&&d.email).length===0){u.error("Please add at least one contributor");return}const{data:{session:a}}=yield $.auth.getSession(),c=a==null?void 0:a.access_token;if(!c){u.error("Authentication required");return}const S={project_id:i,total_amount:s,distribution_model:o,contributors:U().filter(d=>d.name&&d.email),created_by:r.id};if((yield fetch("/.netlify/functions/financial-transactions",{method:"POST",headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"},body:JSON.stringify(F({action:"create_revenue_distribution"},S))})).ok)u.success("Revenue distribution configured successfully!"),l(!1),yield z(),E(""),C([{id:1,name:"",email:"",percentage:0,amount:0,role:""}]);else throw new Error("Failed to save revenue distribution")}catch(s){u.error("Failed to save revenue distribution")}}),H=s=>P(null,null,function*(){try{const{data:{session:t}}=yield $.auth.getSession(),a=t==null?void 0:t.access_token;if(!a){u.error("Authentication required");return}if((yield fetch("/.netlify/functions/financial-transactions",{method:"POST",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},body:JSON.stringify({action:"process_revenue_distribution",distribution_id:s,user_id:r.id})})).ok)u.success("Revenue distribution processed successfully!"),yield z();else throw new Error("Failed to process revenue distribution")}catch(t){u.error("Failed to process revenue distribution")}}),j=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(s||0),Q=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),W=s=>({pending:"warning",processing:"primary",completed:"success",failed:"danger"})[s]||"default";return T?e.jsx(b,{className:n,children:e.jsxs(v,{className:"p-6 text-center",children:[e.jsx("div",{className:"animate-spin text-2xl mb-2",children:"🔄"}),e.jsx("div",{children:"Loading revenue distributions..."})]})}):e.jsxs("div",{className:`revenue-distribution ${n}`,children:[e.jsxs(b,{className:"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 border-2 border-green-200 dark:border-green-700",children:[e.jsx(te,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"💰"}),e.jsx("h3",{className:"text-lg font-semibold",children:"Revenue Distribution"})]}),e.jsx(x,{color:"success",size:"sm",onPress:()=>l(!0),children:"+ Configure Distribution"})]})}),e.jsx(v,{className:"pt-0",children:y.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("span",{className:"text-4xl mb-4 block",children:"📊"}),e.jsx("h4",{className:"text-lg font-medium mb-2",children:"No Revenue Distributions"}),e.jsx("p",{className:"text-default-600 mb-4",children:"Configure revenue sharing to automatically distribute payments to contributors"}),e.jsx(x,{color:"success",onPress:()=>l(!0),children:"Configure First Distribution"})]}):e.jsx("div",{className:"space-y-4",children:y.map((s,t)=>{var a,c,S,A;return e.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.1},children:e.jsx(b,{className:"border border-default-200",children:e.jsxs(v,{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-semibold text-lg",children:[j(s.total_amount)," Distribution"]}),e.jsxs("p",{className:"text-default-600 text-sm",children:[((a=s.contributors)==null?void 0:a.length)||0," contributors • Created ",Q(s.created_at)]})]}),e.jsx(re,{color:W(s.status),variant:"flat",size:"sm",children:s.status})]}),e.jsx("div",{className:"space-y-2 mb-4",children:(c=s.contributors)==null?void 0:c.map((d,X)=>e.jsxs("div",{className:"flex items-center justify-between p-2 bg-default-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ne,{name:d.name,size:"sm"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:d.name}),e.jsx("div",{className:"text-xs text-default-500",children:d.role})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-medium",children:j(d.amount)}),e.jsx("div",{className:"text-xs text-default-500",children:s.distribution_model==="percentage"?`${d.percentage}%`:"Fixed"})]})]},X))}),s.status==="processing"&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Distribution Progress"}),e.jsxs("span",{className:"text-sm text-default-600",children:[s.completed_payments||0," / ",((S=s.contributors)==null?void 0:S.length)||0]})]}),e.jsx(ie,{value:(s.completed_payments||0)/(((A=s.contributors)==null?void 0:A.length)||1)*100,color:"success",size:"sm"})]}),e.jsxs("div",{className:"flex gap-2",children:[s.status==="pending"&&e.jsx(x,{color:"success",size:"sm",onPress:()=>H(s.id),children:"💸 Process Distribution"}),e.jsx(x,{variant:"light",size:"sm",onPress:()=>L(s),children:"📄 View Details"})]})]})})},s.id)})})})]}),e.jsx(le,{isOpen:g,onClose:()=>l(!1),size:"4xl",scrollBehavior:"inside",children:e.jsxs(oe,{children:[e.jsx(ce,{children:e.jsx("span",{className:"text-xl",children:"💰 Configure Revenue Distribution"})}),e.jsx(de,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(N,{label:"Total Revenue",placeholder:"10000.00",startContent:"$",value:p,onChange:s=>E(s.target.value),type:"number"}),e.jsxs(ue,{label:"Distribution Model",selectedKeys:[o],onSelectionChange:s=>J(Array.from(s)[0]),children:[e.jsx(B,{children:"Percentage-based"},"percentage"),e.jsx(B,{children:"Fixed amounts"},"fixed")]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Contributors"}),e.jsx(x,{size:"sm",onPress:G,children:"+ Add Contributor"})]}),e.jsx("div",{className:"space-y-3",children:m.map((s,t)=>e.jsx(b,{className:"border border-default-200",children:e.jsxs(v,{className:"p-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-3",children:[e.jsx(N,{label:"Name",placeholder:"John Doe",value:s.name,onChange:a=>w(s.id,"name",a.target.value)}),e.jsx(N,{label:"Email",placeholder:"<EMAIL>",value:s.email,onChange:a=>w(s.id,"email",a.target.value)}),e.jsx(N,{label:"Role",placeholder:"Lead Developer",value:s.role,onChange:a=>w(s.id,"role",a.target.value)}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(N,{label:o==="percentage"?"Percentage":"Amount",placeholder:o==="percentage"?"25":"2500.00",endContent:o==="percentage"?"%":"$",value:o==="percentage"?s.percentage:s.amount,onChange:a=>w(s.id,o==="percentage"?"percentage":"amount",a.target.value),type:"number"}),m.length>1&&e.jsx(x,{color:"danger",variant:"light",size:"sm",onPress:()=>K(s.id),className:"mt-6",children:"❌"})]})]}),o==="percentage"&&p&&e.jsxs("div",{className:"mt-2 text-sm text-default-600",children:["Amount: ",j(parseFloat(p)*(parseFloat(s.percentage)||0)/100)]})]})},s.id))}),e.jsx(b,{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 mt-4",children:e.jsx(v,{className:"p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:"Distribution Summary"}),e.jsx("p",{className:"text-sm text-default-600",children:o==="percentage"?`Total: ${D()}% (${D()===100?"Complete":"Incomplete"})`:`Total: ${j(M())}`})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold",children:j(M())}),e.jsxs("div",{className:"text-sm text-default-600",children:[m.filter(s=>s.name&&s.email).length," contributors"]})]})]})})})]})]})}),e.jsxs(me,{children:[e.jsx(x,{variant:"light",onPress:()=>l(!1),children:"Cancel"}),e.jsx(x,{color:"success",onPress:V,isDisabled:!p||o==="percentage"&&D()!==100||m.filter(s=>s.name&&s.email).length===0,children:"Save Distribution"})]})]})})]})};export{ye as default};
