var N=(p,y,m)=>new Promise((h,g)=>{var v=l=>{try{j(m.next(l))}catch(t){g(t)}},a=l=>{try{j(m.throw(l))}catch(t){g(t)}},j=l=>l.done?h(l.value):Promise.resolve(l.value).then(v,a);j((m=m.apply(p,y)).next())});import{r as x,j as e,h as r,m as n,c,w as d,b as o,y as u,S as k,T as b}from"./chunk-DX4Z_LyS.js";import{U as R}from"../assets/main-CGUKzV0x.js";import{Q as f,D as C,e as S,E as M,aB as P,z as D,H as W,s as _,S as A,J as w,y as B,O,aF as U,aE as G}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";const Q=({className:p=""})=>{const{currentUser:y}=x.useContext(R),[m,h]=x.useState(!0),[g,v]=x.useState("overview"),[a,j]=x.useState({totalEarnings:{total:12450,thisMonth:3200,available:8200,pending:1050,growth:15.2,hourlyRate:42.5},orbWallet:{balance:15420,value:154.2,earned:2400,dailyPoints:340,conversionRate:23},goals:{monthlyTarget:4e3,currentProgress:3200,percentage:80},ranking:{global:47,orbEarned:340},activeVentures:[{id:1,name:"TaskMaster Pro",icon:"⚔️",earned:7200,share:15.5,progress:85,monthlyEarning:1200,nextMilestone:500,estimatedTime:"2 weeks",members:8,rating:4.8,missions:23},{id:2,name:"Creative Studio Platform",icon:"🎨",earned:3800,share:22.3,progress:50,monthlyEarning:800,nextMilestone:300,estimatedTime:"3 weeks",members:6,rating:4.6,missions:15},{id:3,name:"Testing Automation Tool",icon:"🧪",earned:1450,share:8.7,progress:60,monthlyEarning:200,nextMilestone:150,estimatedTime:"1 week",members:4,rating:4.4,missions:9}],breakdown:{byWorkType:{coding:{amount:8200,percentage:66},design:{amount:2800,percentage:22},testing:{amount:1e3,percentage:8},planning:{amount:450,percentage:4}},byPaymentType:{fixed:{amount:4800,percentage:39},revenue:{amount:6200,percentage:50},bonuses:{amount:1450,percentage:11}}},trends:{sixMonthData:[2800,3200,3600,3800,3400,3200],labels:["Aug","Sep","Oct","Nov","Dec","Jan"]},recentTransactions:[{id:1,date:"Jan 15",project:"TaskMaster Pro",type:"Revenue Share Payment",amount:1200,icon:"⚔️"},{id:2,date:"Jan 10",project:"Creative Studio",type:"Mission Completion",amount:800,icon:"🎨"},{id:3,date:"Jan 08",project:"Testing Tool",type:"Bug Bounty Reward",amount:150,icon:"🧪"}]});x.useEffect(()=>{l()},[y]);const l=()=>N(null,null,function*(){try{h(!0),yield new Promise(s=>setTimeout(s,800))}catch(s){}finally{h(!1)}}),t=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(s),T=s=>`${s>=0?"+":""}${s.toFixed(1)}%`,z=s=>s>=0?"text-green-600":"text-red-600",E=s=>s>=0?e.jsx(U,{size:16}):e.jsx(G,{size:16});return m?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading treasury overview..."})]})}):e.jsxs("div",{className:`enhanced-revenue-dashboard space-y-6 ${p}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[e.jsx(f,{className:"text-green-500",size:32}),"Treasury Overview"]}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive financial tracking and earnings management"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(r,{variant:"light",startContent:e.jsx(C,{size:18}),children:"Export"}),e.jsx(r,{variant:"light",startContent:e.jsx(S,{size:18}),children:"Settings"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(n.div,{className:"lg:col-span-2 lg:row-span-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsxs(c,{className:"h-full bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 border-2 border-green-200",children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{className:"text-green-500",size:24}),e.jsx("h3",{className:"text-lg font-semibold",children:"Total Earnings"})]})}),e.jsx(o,{className:"pt-0",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-3xl font-bold text-green-600 mb-2",children:t(a.totalEarnings.total)}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"This Month:"}),e.jsx("div",{className:"font-semibold",children:t(a.totalEarnings.thisMonth)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Available:"}),e.jsx("div",{className:"font-semibold",children:t(a.totalEarnings.available)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Pending:"}),e.jsx("div",{className:"font-semibold text-yellow-600",children:t(a.totalEarnings.pending)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Hourly Rate:"}),e.jsx("div",{className:"font-semibold",children:t(a.totalEarnings.hourlyRate)})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:`flex items-center gap-1 ${z(a.totalEarnings.growth)}`,children:[E(a.totalEarnings.growth),e.jsxs("span",{className:"font-medium",children:[T(a.totalEarnings.growth)," Growth"]})]}),e.jsx("span",{className:"text-gray-500 text-sm",children:"MoM"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(r,{color:"success",size:"sm",children:"Withdraw"}),e.jsx(r,{variant:"light",size:"sm",startContent:e.jsx(M,{size:16}),children:"History"})]})]})})]})}),e.jsx(n.div,{className:"lg:col-span-2 lg:row-span-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsxs(c,{className:"h-full bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-purple-900/20 dark:to-indigo-800/20 border-2 border-purple-200",children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{className:"text-purple-500",size:24}),e.jsx("h3",{className:"text-lg font-semibold",children:"ORB Wallet"})]})}),e.jsx(o,{className:"pt-0",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:[a.orbWallet.balance.toLocaleString()," ORB"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Value:"}),e.jsx("div",{className:"font-semibold",children:t(a.orbWallet.value)})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Earned:"}),e.jsxs("div",{className:"font-semibold text-green-600",children:["+",a.orbWallet.earned]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Daily:"}),e.jsxs("div",{className:"font-semibold",children:[a.orbWallet.dailyPoints," pts"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Conversion:"}),e.jsxs("div",{className:"font-semibold",children:[a.orbWallet.conversionRate,"%"]})]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(r,{color:"secondary",size:"sm",children:"Trade"}),e.jsx(r,{variant:"light",size:"sm",children:"Convert"})]})]})})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsxs(c,{children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(D,{className:"text-blue-500",size:20}),e.jsx("h3",{className:"font-semibold",children:"Monthly Goal"})]})}),e.jsx(o,{className:"pt-0",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:[a.goals.percentage,"%"]}),e.jsx(u,{value:a.goals.percentage,color:"primary",size:"lg",className:"mb-3"}),e.jsxs("div",{className:"text-sm text-gray-600",children:[t(a.goals.currentProgress)," / ",t(a.goals.monthlyTarget)]})]})})]})}),e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:e.jsxs(c,{children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(W,{className:"text-yellow-500",size:20}),e.jsx("h3",{className:"font-semibold",children:"Global Rank"})]})}),e.jsx(o,{className:"pt-0",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-yellow-600 mb-2",children:["#",a.ranking.global]}),e.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"Global"}),e.jsxs("div",{className:"text-lg font-semibold text-purple-600",children:[a.ranking.orbEarned," ORB"]}),e.jsx("div",{className:"text-xs text-gray-500",children:"Today"})]})})]})})]}),e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:e.jsxs(c,{children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"🚀"}),e.jsx("h3",{className:"text-lg font-semibold",children:"Active Projects"})]}),e.jsx(r,{variant:"light",size:"sm",children:"View All (5)"})]})}),e.jsx(o,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:a.activeVentures.map((s,i)=>e.jsxs(n.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.6+i*.1},className:"p-4 border rounded-lg hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-2xl",children:s.icon}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:s.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[e.jsxs("span",{children:[t(s.earned)," earned"]}),e.jsxs("span",{children:[s.share,"% share"]}),e.jsxs("span",{children:["+",t(s.monthlyEarning)," this month"]})]})]})]}),e.jsxs("div",{className:"text-right text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(_,{size:14}),e.jsxs("span",{children:[s.members," members"]}),e.jsx(A,{size:14,className:"text-yellow-500"}),e.jsxs("span",{children:[s.rating,"★"]})]}),e.jsxs("div",{className:"text-gray-600",children:[s.missions," missions completed"]})]})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex-1 mr-4",children:[e.jsx(u,{value:s.progress,color:"success",size:"sm",className:"mb-1"}),e.jsxs("div",{className:"text-xs text-gray-600",children:[s.progress,"% to tranche • Next milestone: ",t(s.nextMilestone)," • Est: ",s.estimatedTime]})]})})]},s.id))})})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:e.jsxs(c,{children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{className:"text-blue-500",size:20}),e.jsx("h3",{className:"font-semibold",children:"Revenue Breakdown"})]})}),e.jsx(o,{className:"pt-0",children:e.jsxs(k,{selectedKey:g,onSelectionChange:v,size:"sm",children:[e.jsx(b,{title:"By Work Type",children:e.jsx("div",{className:"space-y-3 mt-4",children:Object.entries(a.breakdown.byWorkType).map(([s,i])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"capitalize font-medium",children:s}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${i.percentage}%`}})}),e.jsx("span",{className:"text-sm font-medium w-16 text-right",children:t(i.amount)}),e.jsxs("span",{className:"text-xs text-gray-500 w-8",children:[i.percentage,"%"]})]})]},s))})},"workType"),e.jsx(b,{title:"By Payment Type",children:e.jsx("div",{className:"space-y-3 mt-4",children:Object.entries(a.breakdown.byPaymentType).map(([s,i])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"capitalize font-medium",children:s}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${i.percentage}%`}})}),e.jsx("span",{className:"text-sm font-medium w-16 text-right",children:t(i.amount)}),e.jsxs("span",{className:"text-xs text-gray-500 w-8",children:[i.percentage,"%"]})]})]},s))})},"paymentType")]})})]})}),e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:e.jsxs(c,{children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"text-purple-500",size:20}),e.jsx("h3",{className:"font-semibold",children:"Recent Transactions"})]}),e.jsx(r,{variant:"light",size:"sm",children:"View All"})]})}),e.jsx(o,{className:"pt-0",children:e.jsx("div",{className:"space-y-3",children:a.recentTransactions.map((s,i)=>e.jsxs(n.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.9+i*.1},className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-xl",children:s.icon}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-sm",children:s.project}),e.jsx("p",{className:"text-xs text-gray-600",children:s.type})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"font-semibold text-green-600",children:["+",t(s.amount)]}),e.jsx("div",{className:"text-xs text-gray-500",children:s.date})]})]},s.id))})})]})})]}),e.jsx(n.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1},children:e.jsxs(c,{children:[e.jsx(d,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(O,{className:"text-green-500",size:20}),e.jsx("h3",{className:"font-semibold",children:"6-Month Earnings Trend"})]})}),e.jsx(o,{className:"pt-0",children:e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx(w,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"Interactive earnings chart coming soon!"}),e.jsx("p",{className:"text-sm",children:"Track your earnings trends over time"})]})})]})})]})};export{Q as default};
