var E=Object.defineProperty,z=Object.defineProperties;var F=Object.getOwnPropertyDescriptors;var f=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var N=(r,a,i)=>a in r?E(r,a,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[a]=i,h=(r,a)=>{for(var i in a||(a={}))M.call(a,i)&&N(r,i,a[i]);if(f)for(var i of f(a))D.call(a,i)&&N(r,i,a[i]);return r},u=(r,a)=>z(r,F(a));var C=(r,a,i)=>new Promise((s,n)=>{var d=l=>{try{c(i.next(l))}catch(m){n(m)}},p=l=>{try{c(i.throw(l))}catch(m){n(m)}},c=l=>l.done?s(l.value):Promise.resolve(l.value).then(d,p);c((i=i.apply(r,a)).next())});import{r as w,j as e,m as x,c as j,b as y,h as _,p as S,F as R,u as U,v as W,t as k}from"./chunk-DX4Z_LyS.js";import{U as P,s as T}from"../assets/main-CGUKzV0x.js";import{c as v}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const Q=({canvasId:r,sectionId:a})=>{const{currentUser:i}=w.useContext(P),[s,n]=w.useState({name:"",description:"",studio_type:"emerging",is_business_entity:!1,is_public:!0,max_members:10}),[d,p]=w.useState(!1),[c,l]=w.useState(!0),m=[{key:"solo",label:"⭐ Solo Project",description:"Individual contributor working independently"},{key:"emerging",label:"🌱 Emerging Studio",description:"Small team building momentum"},{key:"established",label:"🏰 Established Studio",description:"Mature team with proven track record"}],o=(t,b)=>{n(g=>u(h({},g),{[t]:b}))},I=()=>C(null,null,function*(){if(!s.name.trim()){v.error("Team name is required");return}p(!0);try{const{data:t,error:b}=yield T.from("teams").insert({name:s.name.trim(),description:s.description.trim(),studio_type:s.studio_type,is_business_entity:s.is_business_entity,is_public:s.is_public,max_members:s.max_members,created_by:i.id}).select().single();if(b)throw b;const{error:g}=yield T.from("team_members").insert({team_id:t.id,user_id:i.id,role:"admin",is_admin:!0,joined_at:new Date().toISOString()});if(g)throw g;v.success("Studio created successfully!"),n({name:"",description:"",studio_type:"emerging",is_business_entity:!1,is_public:!0,max_members:10}),setTimeout(()=>{window.location.href=`/teams/${t.id}`},1500)}catch(t){v.error("Failed to create studio")}finally{p(!1)}});return c?e.jsx(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"container mx-auto p-6",children:e.jsx(j,{children:e.jsxs(y,{children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Immersive Studio Wizard"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Immersive wizard temporarily unavailable. Using traditional wizard."}),e.jsx(_,{onClick:()=>l(!1),children:"Use Traditional Wizard"})]})})}):e.jsxs(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"p-6 space-y-6",children:[e.jsx("div",{className:"text-center mb-4",children:e.jsx(_,{variant:"bordered",size:"sm",onClick:()=>l(!0),className:"text-white border-white/30 hover:bg-white/10",children:"🚀 Try Immersive Mode"})}),e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"🏰 Create New Studio"}),e.jsx("p",{className:"text-white/70",children:"Start your own team or studio and invite collaborators"})]}),e.jsx(j,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(y,{className:"p-8 space-y-6",children:[e.jsx(S,{label:"Studio Name",placeholder:"Enter your studio name",value:s.name,onChange:t=>o("name",t.target.value),variant:"bordered",size:"lg",classNames:{input:"text-white",label:"text-white/70",inputWrapper:"bg-white/10 border-white/20"},startContent:e.jsx("span",{className:"text-white/70",children:"🏰"})}),e.jsx(R,{label:"Description",placeholder:"Describe your studio's mission and goals",value:s.description,onChange:t=>o("description",t.target.value),variant:"bordered",minRows:3,classNames:{input:"text-white",label:"text-white/70",inputWrapper:"bg-white/10 border-white/20"}}),e.jsx(U,{label:"Studio Type",placeholder:"Select studio type",selectedKeys:[s.studio_type],onSelectionChange:t=>o("studio_type",Array.from(t)[0]),variant:"bordered",classNames:{trigger:"bg-white/10 border-white/20",label:"text-white/70",value:"text-white"},children:m.map(t=>e.jsx(W,{value:t.key,children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:t.label}),e.jsx("div",{className:"text-sm text-gray-500",children:t.description})]})},t.key))}),e.jsx(S,{label:"Maximum Members",type:"number",placeholder:"10",value:s.max_members.toString(),onChange:t=>o("max_members",parseInt(t.target.value)||10),variant:"bordered",classNames:{input:"text-white",label:"text-white/70",inputWrapper:"bg-white/10 border-white/20"},startContent:e.jsx("span",{className:"text-white/70",children:"👥"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-bold text-white",children:"Studio Settings"}),e.jsxs("div",{className:"flex items-center justify-between bg-white/5 rounded-lg p-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:"Public Studio"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Allow others to discover and request to join"})]}),e.jsx(k,{isSelected:s.is_public,onValueChange:t=>o("is_public",t),color:"primary"})]}),e.jsxs("div",{className:"flex items-center justify-between bg-white/5 rounded-lg p-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:"Business Entity"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Register as a formal business entity"})]}),e.jsx(k,{isSelected:s.is_business_entity,onValueChange:t=>o("is_business_entity",t),color:"success"})]})]}),s.is_business_entity&&e.jsx(j,{className:"bg-yellow-500/10 border-yellow-500/20",children:e.jsx(y,{className:"p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("span",{className:"text-yellow-400 text-xl",children:"⚠️"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-yellow-300 font-medium",children:"Business Entity Registration"}),e.jsx("p",{className:"text-yellow-200/80 text-sm mt-1",children:"Creating a business entity will require additional legal and tax information. You'll be guided through the business registration process after creating your studio."})]})]})})}),e.jsxs("div",{className:"flex gap-4 pt-4",children:[e.jsx(_,{color:"primary",size:"lg",className:"flex-1",isLoading:d,onClick:I,startContent:!d&&"🏰",children:d?"Creating Studio...":"Create Studio"}),e.jsx(_,{color:"secondary",variant:"bordered",size:"lg",onClick:()=>{n({name:"",description:"",studio_type:"emerging",is_business_entity:!1,is_public:!0,max_members:10})},children:"Reset"})]})]})}),e.jsx(j,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(y,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"🚀 Quick Templates"}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[e.jsxs(x.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white/5 rounded-lg p-4 cursor-pointer",onClick:()=>{n(u(h({},s),{name:"Creative Collective",description:"A collaborative space for creative professionals to work together on innovative projects.",studio_type:"emerging",max_members:15}))},children:[e.jsx("div",{className:"text-3xl mb-2",children:"🎨"}),e.jsx("h4",{className:"text-white font-medium",children:"Creative Collective"}),e.jsx("p",{className:"text-white/60 text-sm",children:"For artists and creators"})]}),e.jsxs(x.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white/5 rounded-lg p-4 cursor-pointer",onClick:()=>{n(u(h({},s),{name:"Tech Startup",description:"Building the next generation of technology solutions with a passionate team.",studio_type:"emerging",is_business_entity:!0,max_members:20}))},children:[e.jsx("div",{className:"text-3xl mb-2",children:"💻"}),e.jsx("h4",{className:"text-white font-medium",children:"Tech Startup"}),e.jsx("p",{className:"text-white/60 text-sm",children:"For technology projects"})]}),e.jsxs(x.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white/5 rounded-lg p-4 cursor-pointer",onClick:()=>{n(u(h({},s),{name:"Solo Project",description:"Independent work with occasional collaboration opportunities.",studio_type:"solo",max_members:5}))},children:[e.jsx("div",{className:"text-3xl mb-2",children:"⭐"}),e.jsx("h4",{className:"text-white font-medium",children:"Solo Project"}),e.jsx("p",{className:"text-white/60 text-sm",children:"For independent work"})]})]})]})})]})};export{Q as default};
