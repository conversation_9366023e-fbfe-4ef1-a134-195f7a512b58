import{r as l,j as e,m as o,c as i,b as a,h as t,i as c}from"./chunk-DX4Z_LyS.js";import{U as m}from"../assets/main-CGUKzV0x.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";import"./chunk-BiNxGM8y.js";const y=({canvasId:h,sectionId:x})=>{const{currentUser:p}=l.useContext(m),[u,j]=l.useState([]),r=[{id:"chat",title:"Team Chat",icon:"💬",description:"Real-time messaging with your studio members",status:"coming-soon",color:"primary"},{id:"video-calls",title:"Video Meetings",icon:"📹",description:"Schedule and join video conferences",status:"coming-soon",color:"secondary"},{id:"file-sharing",title:"File Sharing",icon:"📁",description:"Share documents and resources securely",status:"coming-soon",color:"success"},{id:"project-boards",title:"Project Boards",icon:"📋",description:"Kanban-style project management",status:"available",color:"warning",action:()=>window.location.href="/projects"},{id:"time-tracking",title:"Time Tracking",icon:"⏱️",description:"Track contributions and work hours",status:"available",color:"danger",action:()=>window.location.href="/track"},{id:"revenue-sharing",title:"Revenue Sharing",icon:"💰",description:"Manage payments and royalty distribution",status:"available",color:"success",action:()=>window.location.href="/earn"}],d=[{id:"github",title:"GitHub",icon:"🐙",description:"Connect your code repositories",status:"coming-soon"},{id:"slack",title:"Slack",icon:"💬",description:"Integrate with Slack workspaces",status:"coming-soon"},{id:"discord",title:"Discord",icon:"🎮",description:"Connect Discord servers",status:"coming-soon"},{id:"google-workspace",title:"Google Workspace",icon:"📊",description:"Sync with Google Drive and Docs",status:"coming-soon"}],n=s=>{switch(s){case"available":return e.jsx(c,{color:"success",variant:"flat",children:"Available"});case"coming-soon":return e.jsx(c,{color:"warning",variant:"flat",children:"Coming Soon"});case"beta":return e.jsx(c,{color:"primary",variant:"flat",children:"Beta"});default:return e.jsx(c,{color:"default",variant:"flat",children:"Unknown"})}};return e.jsxs(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"p-6 space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"🤝 Collaboration Tools"}),e.jsx("p",{className:"text-white/70",children:"Powerful tools to enhance team collaboration and productivity"})]}),e.jsx(i,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(a,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"🚀 Core Features"}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:r.map(s=>e.jsxs(o.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:`bg-white/5 rounded-lg p-4 cursor-pointer transition-all ${s.status==="available"?"hover:bg-white/10":"opacity-75"}`,onClick:s.action,children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsx("div",{className:"text-3xl",children:s.icon}),n(s.status)]}),e.jsx("h4",{className:"text-white font-medium mb-2",children:s.title}),e.jsx("p",{className:"text-white/60 text-sm mb-3",children:s.description}),s.status==="available"&&e.jsx(t,{size:"sm",color:s.color,variant:"flat",className:"w-full",onClick:s.action,children:"Open Tool"}),s.status==="coming-soon"&&e.jsx(t,{size:"sm",color:"default",variant:"bordered",className:"w-full",disabled:!0,children:"Coming Soon"})]},s.id))})]})}),e.jsx(i,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(a,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"🔗 Integrations"}),e.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-4",children:d.map(s=>e.jsxs(o.div,{whileHover:{scale:1.05},className:"bg-white/5 rounded-lg p-4 text-center opacity-75",children:[e.jsx("div",{className:"text-4xl mb-3",children:s.icon}),e.jsx("h4",{className:"text-white font-medium mb-2",children:s.title}),e.jsx("p",{className:"text-white/60 text-xs mb-3",children:s.description}),n(s.status)]},s.id))})]})}),e.jsx(i,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(a,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"⚡ Quick Actions"}),e.jsxs("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(t,{color:"primary",variant:"solid",size:"lg",startContent:"📋",onClick:()=>window.location.href="/projects",className:"h-16",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold",children:"Projects"}),e.jsx("div",{className:"text-xs opacity-80",children:"Manage work"})]})}),e.jsx(t,{color:"secondary",variant:"solid",size:"lg",startContent:"⏱️",onClick:()=>window.location.href="/track",className:"h-16",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold",children:"Track Time"}),e.jsx("div",{className:"text-xs opacity-80",children:"Log contributions"})]})}),e.jsx(t,{color:"success",variant:"solid",size:"lg",startContent:"💰",onClick:()=>window.location.href="/earn",className:"h-16",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold",children:"Revenue"}),e.jsx("div",{className:"text-xs opacity-80",children:"Share earnings"})]})}),e.jsx(t,{color:"warning",variant:"solid",size:"lg",startContent:"👥",onClick:()=>window.location.href="/teams",className:"h-16",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"font-bold",children:"Teams"}),e.jsx("div",{className:"text-xs opacity-80",children:"Manage studios"})]})})]})]})}),e.jsx(i,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(a,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"💡 Collaboration Tips"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start space-x-3 bg-white/5 rounded-lg p-4",children:[e.jsx("span",{className:"text-2xl",children:"🎯"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:"Set Clear Goals"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Define project objectives and individual responsibilities upfront."})]})]}),e.jsxs("div",{className:"flex items-start space-x-3 bg-white/5 rounded-lg p-4",children:[e.jsx("span",{className:"text-2xl",children:"📅"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:"Regular Check-ins"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Schedule weekly team meetings to track progress and address blockers."})]})]}),e.jsxs("div",{className:"flex items-start space-x-3 bg-white/5 rounded-lg p-4",children:[e.jsx("span",{className:"text-2xl",children:"📊"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:"Track Contributions"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Use time tracking to ensure fair revenue distribution based on actual work."})]})]}),e.jsxs("div",{className:"flex items-start space-x-3 bg-white/5 rounded-lg p-4",children:[e.jsx("span",{className:"text-2xl",children:"🤝"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:"Open Communication"}),e.jsx("p",{className:"text-white/60 text-sm",children:"Encourage transparent communication and constructive feedback."})]})]})]})]})})]})};export{y as default};
