var le=Object.defineProperty,ie=Object.defineProperties;var re=Object.getOwnPropertyDescriptors;var E=Object.getOwnPropertySymbols;var ce=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var H=(l,i,r)=>i in l?le(l,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):l[i]=r,M=(l,i)=>{for(var r in i||(i={}))ce.call(i,r)&&H(l,r,i[r]);if(E)for(var r of E(i))ne.call(i,r)&&H(l,r,i[r]);return l},T=(l,i)=>ie(l,re(i));var d=(l,i,r)=>new Promise((w,N)=>{var g=t=>{try{h(r.next(t))}catch(f){N(f)}},C=t=>{try{h(r.throw(t))}catch(f){N(f)}},h=t=>t.done?w(t.value):Promise.resolve(t.value).then(g,C);h((r=r.apply(l,i)).next())});import{r as n,j as e,m as _,c as o,b as x,S as de,T as k,w as u,e as S,y as B,h as m,C as P,l as q,n as K,o as V,u as $,v as b,E as Q}from"./chunk-DX4Z_LyS.js";import{s as j}from"../assets/main-CGUKzV0x.js";import{c as p}from"./chunk-D8IZ3rty.js";import{k as oe,ac as xe,e as me,p as he}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";const _e=()=>{const[l,i]=n.useState("health"),[r,w]=n.useState(!0),[N,g]=n.useState(!1),[C,h]=n.useState(!1),[t,f]=n.useState({status:"healthy",uptime:"99.9%",responseTime:"120ms",activeUsers:0,dbConnections:0,memoryUsage:0,cpuUsage:0,diskUsage:0,lastUpdated:new Date}),[ue,G]=n.useState([]),[v,J]=n.useState({totalTables:0,totalRecords:0,databaseSize:"0 MB",lastBackup:null,activeConnections:0,slowQueries:0}),[z,W]=n.useState([]),[je,X]=n.useState([]),[R,Y]=n.useState([]);n.useEffect(()=>{L();const s=setInterval(L,3e4);return()=>clearInterval(s)},[]);const L=()=>d(null,null,function*(){try{w(!0);const{data:s,error:a}=yield j.from("system_monitoring").select("*").order("created_at",{ascending:!1}).limit(100);if(a)throw a;G(s||[]);const c=s==null?void 0:s.reduce((y,O)=>(y[O.metric_name]=O.metric_value,y),{});c&&f(y=>T(M({},y),{responseTime:`${c.response_time||120}ms`,activeUsers:c.active_users||0,dbConnections:c.db_connections||0,memoryUsage:c.memory_usage||0,cpuUsage:c.cpu_usage||0,diskUsage:c.disk_usage||0,lastUpdated:new Date})),yield A(),yield F(),yield I(),yield Z()}catch(s){p.error("Failed to load system data")}finally{w(!1)}}),A=()=>d(null,null,function*(){try{const{data:s,error:a}=yield j.rpc("get_table_stats");!a&&s&&J(c=>T(M({},c),{totalTables:s.table_count||0,totalRecords:s.total_records||0,databaseSize:s.database_size||"0 MB"}))}catch(s){}}),F=()=>d(null,null,function*(){try{const{data:s,error:a}=yield j.from("system_backups").select("*").order("created_at",{ascending:!1}).limit(10);a||W(s||[])}catch(s){}}),I=()=>d(null,null,function*(){try{const{data:s,error:a}=yield j.from("maintenance_tasks").select("*").order("scheduled_at",{ascending:!1}).limit(20);a||X(s||[])}catch(s){}}),Z=()=>d(null,null,function*(){try{const{data:s,error:a}=yield j.from("system_alerts").select("*").eq("status","active").order("created_at",{ascending:!1});a||Y(s||[])}catch(s){}}),ee=(s="full")=>d(null,null,function*(){try{const{data:a,error:c}=yield j.from("system_backups").insert({backup_type:s,status:"in_progress",created_at:new Date().toISOString()}).select().single();if(c)throw c;p.success("Backup initiated successfully"),g(!1),F()}catch(a){p.error("Failed to create backup")}}),se=()=>d(null,null,function*(){try{const{data:s,error:a}=yield j.rpc("optimize_database");if(a)throw a;p.success("Database optimization completed"),A()}catch(s){p.error("Failed to optimize database")}}),te=s=>d(null,null,function*(){try{const{data:a,error:c}=yield j.from("maintenance_tasks").insert({task_type:s,status:"running",scheduled_at:new Date().toISOString()}).select().single();if(c)throw c;p.success(`${s} maintenance task started`),h(!1),I()}catch(a){p.error("Failed to start maintenance task")}}),U=s=>{switch(s){case"healthy":return"success";case"warning":return"warning";case"error":return"danger";case"completed":return"success";case"pending":return"warning";case"failed":return"danger";default:return"default"}},ae=s=>{switch(s){case"info":return"primary";case"warning":return"warning";case"error":return"danger";default:return"default"}},D=s=>s>80?"danger":s>60?"warning":"success";return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800",children:[e.jsx(_.div,{className:"relative z-10 pt-8 pb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsx("div",{className:"container mx-auto px-6",children:e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(_.div,{className:"text-6xl mb-4",animate:{scale:[1,1.1,1],rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatType:"reverse"},children:"🖥️"}),e.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:"System Management"}),e.jsx("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Monitor system health, manage database operations, and maintain platform stability."})]})})}),e.jsx("div",{className:"container mx-auto px-6 pb-12",children:e.jsxs(_.div,{className:"max-w-6xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:[e.jsx(o,{className:"mb-8 bg-white/5 border border-white/10",children:e.jsx(x,{className:"p-6",children:e.jsxs(de,{selectedKey:l,onSelectionChange:i,variant:"underlined",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-gradient-to-r from-gray-500 to-slate-500",tab:"max-w-fit px-0 h-12",tabContent:"group-data-[selected=true]:text-white text-white/70"},children:[e.jsx(k,{title:"💚 Health"},"health"),e.jsx(k,{title:"🗄️ Database"},"database"),e.jsx(k,{title:"🔄 Migrations"},"migrations"),e.jsx(k,{title:"📋 Logs"},"logs")]})})}),e.jsxs(_.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:[l==="health"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"System Status"}),e.jsx(S,{color:U(t.status),variant:"flat",children:t.status.toUpperCase()})]})}),e.jsx(x,{className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:t.uptime}),e.jsx("div",{className:"text-white/70 text-sm",children:"Uptime"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:t.responseTime}),e.jsx("div",{className:"text-white/70 text-sm",children:"Response Time"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-400",children:t.activeUsers}),e.jsx("div",{className:"text-white/70 text-sm",children:"Active Users"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-400",children:t.dbConnections}),e.jsx("div",{className:"text-white/70 text-sm",children:"DB Connections"})]})]})})]}),e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Resource Usage"})}),e.jsxs(x,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[e.jsx("span",{className:"text-white/70",children:"Memory Usage"}),e.jsxs("span",{className:"text-white",children:[t.memoryUsage,"%"]})]}),e.jsx(B,{value:t.memoryUsage,color:D(t.memoryUsage),className:"max-w-full"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[e.jsx("span",{className:"text-white/70",children:"CPU Usage"}),e.jsxs("span",{className:"text-white",children:[t.cpuUsage,"%"]})]}),e.jsx(B,{value:t.cpuUsage,color:D(t.cpuUsage),className:"max-w-full"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[e.jsx("span",{className:"text-white/70",children:"Disk Usage"}),e.jsxs("span",{className:"text-white",children:[t.diskUsage,"%"]})]}),e.jsx(B,{value:t.diskUsage,color:D(t.diskUsage),className:"max-w-full"})]})]})]})]}),l==="database"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Database Statistics"})}),e.jsx(x,{children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:v.totalTables}),e.jsx("div",{className:"text-white/70 text-sm",children:"Total Tables"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:v.totalRecords.toLocaleString()}),e.jsx("div",{className:"text-white/70 text-sm",children:"Total Records"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-400",children:v.databaseSize}),e.jsx("div",{className:"text-white/70 text-sm",children:"Database Size"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-400",children:v.activeConnections}),e.jsx("div",{className:"text-white/70 text-sm",children:"Active Connections"})]})]})})]}),e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Database Operations"})}),e.jsx(x,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(m,{className:"bg-blue-500 hover:bg-blue-600 text-white h-20 flex flex-col",onClick:()=>g(!0),children:[e.jsx(oe,{className:"h-6 w-6 mb-2"}),"Create Backup"]}),e.jsxs(m,{className:"bg-orange-500 hover:bg-orange-600 text-white h-20 flex flex-col",onClick:se,children:[e.jsx(xe,{className:"h-6 w-6 mb-2"}),"Optimize Database"]}),e.jsxs(m,{className:"bg-green-500 hover:bg-green-600 text-white h-20 flex flex-col",onClick:()=>h(!0),children:[e.jsx(me,{className:"h-6 w-6 mb-2"}),"Run Maintenance"]})]})})]}),e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Recent Backups"})}),e.jsx(x,{children:z.length>0?e.jsx("div",{className:"space-y-3",children:z.map((s,a)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"text-white font-medium",children:[s.backup_type||"Full"," Backup"]}),e.jsx("div",{className:"text-white/60 text-sm",children:new Date(s.created_at).toLocaleString()})]}),e.jsx(S,{size:"sm",color:U(s.status),variant:"flat",children:s.status})]},s.id||a))}):e.jsx("div",{className:"text-center py-8 text-white/60",children:"No backup history available"})})]}),R.length>0&&e.jsxs(o,{className:"bg-red-500/10 border border-red-500/30",children:[e.jsx(u,{className:"pb-3",children:e.jsx("h3",{className:"text-xl font-semibold text-red-400",children:"System Alerts"})}),e.jsx(x,{children:e.jsx("div",{className:"space-y-3",children:R.map((s,a)=>e.jsxs("div",{className:"flex items-start gap-3 p-3 bg-red-500/10 rounded-lg",children:[e.jsx(he,{className:"h-5 w-5 text-red-400 mt-0.5"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-red-400 font-medium",children:s.title}),e.jsx("div",{className:"text-red-300 text-sm",children:s.message}),e.jsx("div",{className:"text-red-400/60 text-xs mt-1",children:new Date(s.created_at).toLocaleString()})]})]},s.id||a))})})]})]}),l==="migrations"&&e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"Database Migrations"}),e.jsx(m,{className:"bg-green-500 hover:bg-green-600 text-white",children:"Run Pending"})]})}),e.jsx(x,{className:"space-y-4",children:migrations.map(s=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:s.name}),e.jsxs("div",{className:"text-white/60 text-sm",children:[s.date," • Duration: ",s.duration]})]}),e.jsx(S,{size:"sm",color:U(s.status),variant:"flat",children:s.status})]},s.id))})]}),l==="logs"&&e.jsxs(o,{className:"bg-white/5 border border-white/10",children:[e.jsx(u,{className:"pb-3",children:e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:"System Logs"}),e.jsx(m,{className:"bg-gray-500 hover:bg-gray-600 text-white",children:"Download Logs"})]})}),e.jsx(x,{className:"space-y-3",children:logs.map(s=>e.jsxs("div",{className:"flex items-start gap-3 p-3 bg-white/5 rounded-lg",children:[e.jsx(S,{size:"sm",color:ae(s.level),variant:"flat",children:s.level.toUpperCase()}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-white text-sm",children:s.message}),e.jsxs("div",{className:"text-white/60 text-xs",children:[s.timestamp," • ",s.source]})]})]},s.id))})]})]},l)]})}),e.jsx(P,{isOpen:N,onClose:()=>g(!1),children:e.jsxs(q,{children:[e.jsx(K,{children:e.jsx("h3",{children:"Create Database Backup"})}),e.jsx(V,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Backup Type"}),e.jsxs($,{placeholder:"Select backup type",defaultSelectedKeys:["full"],children:[e.jsx(b,{children:"Full Backup"},"full"),e.jsx(b,{children:"Incremental Backup"},"incremental"),e.jsx(b,{children:"Schema Only"},"schema")]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("p",{children:"• Full backup includes all data and schema"}),e.jsx("p",{children:"• Incremental backup includes only changes since last backup"}),e.jsx("p",{children:"• Schema backup includes only table structures"})]})]})}),e.jsxs(Q,{children:[e.jsx(m,{variant:"light",onClick:()=>g(!1),children:"Cancel"}),e.jsx(m,{color:"primary",onClick:()=>ee("full"),children:"Create Backup"})]})]})}),e.jsx(P,{isOpen:C,onClose:()=>h(!1),children:e.jsxs(q,{children:[e.jsx(K,{children:e.jsx("h3",{children:"Run Maintenance Task"})}),e.jsx(V,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Maintenance Type"}),e.jsxs($,{placeholder:"Select maintenance task",children:[e.jsx(b,{children:"Database Cleanup"},"cleanup"),e.jsx(b,{children:"Reindex Tables"},"reindex"),e.jsx(b,{children:"Vacuum Database"},"vacuum"),e.jsx(b,{children:"Analyze Statistics"},"analyze")]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("p",{children:"• Cleanup removes old logs and temporary data"}),e.jsx("p",{children:"• Reindex rebuilds database indexes for better performance"}),e.jsx("p",{children:"• Vacuum reclaims storage space"}),e.jsx("p",{children:"• Analyze updates query planner statistics"})]})]})}),e.jsxs(Q,{children:[e.jsx(m,{variant:"light",onClick:()=>h(!1),children:"Cancel"}),e.jsx(m,{color:"warning",onClick:()=>te("cleanup"),children:"Run Maintenance"})]})]})})]})};export{_e as default};
