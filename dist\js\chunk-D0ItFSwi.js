var u=(n,e,t)=>new Promise((s,r)=>{var i=l=>{try{a(t.next(l))}catch(c){r(c)}},o=l=>{try{a(t.throw(l))}catch(c){r(c)}},a=l=>l.done?s(l.value):Promise.resolve(l.value).then(i,o);a((t=t.apply(n,e)).next())});import{b as $t,g as Rt}from"./chunk-Cai8ouo_.js";import{_ as he}from"./chunk-DX4Z_LyS.js";const Ct=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=(...t)=>he(()=>u(null,null,function*(){const{default:s}=yield Promise.resolve().then(()=>se);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)};class Le extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class xt extends Le{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class It extends Le{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class Ut extends Le{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Oe;(function(n){n.Any="any",n.ApNortheast1="ap-northeast-1",n.ApNortheast2="ap-northeast-2",n.ApSouth1="ap-south-1",n.ApSoutheast1="ap-southeast-1",n.ApSoutheast2="ap-southeast-2",n.CaCentral1="ca-central-1",n.EuCentral1="eu-central-1",n.EuWest1="eu-west-1",n.EuWest2="eu-west-2",n.EuWest3="eu-west-3",n.SaEast1="sa-east-1",n.UsEast1="us-east-1",n.UsWest1="us-west-1",n.UsWest2="us-west-2"})(Oe||(Oe={}));var Lt=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};class Dt{constructor(e,{headers:t={},customFetch:s,region:r=Oe.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=Ct(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return Lt(this,void 0,void 0,function*(){try{const{headers:r,method:i,body:o}=t;let a={},{region:l}=t;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let c;o&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&(typeof Blob!="undefined"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",c=o):typeof o=="string"?(a["Content-Type"]="text/plain",c=o):typeof FormData!="undefined"&&o instanceof FormData?c=o:(a["Content-Type"]="application/json",c=JSON.stringify(o)));const h=yield this.fetch(`${this.url}/${e}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),r),body:c}).catch(p=>{throw new xt(p)}),d=h.headers.get("x-relay-error");if(d&&d==="true")throw new It(h);if(!h.ok)throw new Ut(h);let f=((s=h.headers.get("Content-Type"))!==null&&s!==void 0?s:"text/plain").split(";")[0].trim(),g;return f==="application/json"?g=yield h.json():f==="application/octet-stream"?g=yield h.blob():f==="text/event-stream"?g=h:f==="multipart/form-data"?g=yield h.formData():g=yield h.text(),{data:g,error:null}}catch(r){return{data:null,error:r}}})}}var E={},z={},J={},H={},K={},G={},qt=function(){if(typeof self!="undefined")return self;if(typeof window!="undefined")return window;if(typeof globalThis!="undefined")return globalThis;throw new Error("unable to locate global object")},te=qt();const Bt=te.fetch,ut=te.fetch.bind(te),dt=te.Headers,Nt=te.Request,Mt=te.Response,se=Object.freeze(Object.defineProperty({__proto__:null,Headers:dt,Request:Nt,Response:Mt,default:ut,fetch:Bt},Symbol.toStringTag,{value:"Module"})),Ft=$t(se);var de={},Me;function ft(){if(Me)return de;Me=1,Object.defineProperty(de,"__esModule",{value:!0});class n extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}}return de.default=n,de}var Fe;function gt(){if(Fe)return G;Fe=1;var n=G&&G.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(G,"__esModule",{value:!0});const e=n(Ft),t=n(ft());class s{constructor(i){this.shouldThrowOnError=!1,this.method=i.method,this.url=i.url,this.headers=i.headers,this.schema=i.schema,this.body=i.body,this.shouldThrowOnError=i.shouldThrowOnError,this.signal=i.signal,this.isMaybeSingle=i.isMaybeSingle,i.fetch?this.fetch=i.fetch:typeof fetch=="undefined"?this.fetch=e.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(i,o){return this.headers=Object.assign({},this.headers),this.headers[i]=o,this}then(i,o){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const a=this.fetch;let l=a(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(c=>u(this,null,function*(){var h,d,f;let g=null,p=null,m=null,_=c.status,T=c.statusText;if(c.ok){if(this.method!=="HEAD"){const O=yield c.text();O===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?p=O:p=JSON.parse(O))}const v=(h=this.headers.Prefer)===null||h===void 0?void 0:h.match(/count=(exact|planned|estimated)/),b=(d=c.headers.get("content-range"))===null||d===void 0?void 0:d.split("/");v&&b&&b.length>1&&(m=parseInt(b[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(p)&&(p.length>1?(g={code:"PGRST116",details:`Results contain ${p.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},p=null,m=null,_=406,T="Not Acceptable"):p.length===1?p=p[0]:p=null)}else{const v=yield c.text();try{g=JSON.parse(v),Array.isArray(g)&&c.status===404&&(p=[],g=null,_=200,T="OK")}catch(b){c.status===404&&v===""?(_=204,T="No Content"):g={message:v}}if(g&&this.isMaybeSingle&&(!((f=g==null?void 0:g.details)===null||f===void 0)&&f.includes("0 rows"))&&(g=null,_=200,T="OK"),g&&this.shouldThrowOnError)throw new t.default(g)}return{error:g,data:p,count:m,status:_,statusText:T}}));return this.shouldThrowOnError||(l=l.catch(c=>{var h,d,f;return{error:{message:`${(h=c==null?void 0:c.name)!==null&&h!==void 0?h:"FetchError"}: ${c==null?void 0:c.message}`,details:`${(d=c==null?void 0:c.stack)!==null&&d!==void 0?d:""}`,hint:"",code:`${(f=c==null?void 0:c.code)!==null&&f!==void 0?f:""}`},data:null,count:null,status:0,statusText:""}})),l.then(i,o)}returns(){return this}overrideTypes(){return this}}return G.default=s,G}var We;function pt(){if(We)return K;We=1;var n=K&&K.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(K,"__esModule",{value:!0});const e=n(gt());class t extends e.default{select(r){let i=!1;const o=(r!=null?r:"*").split("").map(a=>/\s/.test(a)&&!i?"":(a==='"'&&(i=!i),a)).join("");return this.url.searchParams.set("select",o),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(r,{ascending:i=!0,nullsFirst:o,foreignTable:a,referencedTable:l=a}={}){const c=l?`${l}.order`:"order",h=this.url.searchParams.get(c);return this.url.searchParams.set(c,`${h?`${h},`:""}${r}.${i?"asc":"desc"}${o===void 0?"":o?".nullsfirst":".nullslast"}`),this}limit(r,{foreignTable:i,referencedTable:o=i}={}){const a=typeof o=="undefined"?"limit":`${o}.limit`;return this.url.searchParams.set(a,`${r}`),this}range(r,i,{foreignTable:o,referencedTable:a=o}={}){const l=typeof a=="undefined"?"offset":`${a}.offset`,c=typeof a=="undefined"?"limit":`${a}.limit`;return this.url.searchParams.set(l,`${r}`),this.url.searchParams.set(c,`${i-r+1}`),this}abortSignal(r){return this.signal=r,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:r=!1,verbose:i=!1,settings:o=!1,buffers:a=!1,wal:l=!1,format:c="text"}={}){var h;const d=[r?"analyze":null,i?"verbose":null,o?"settings":null,a?"buffers":null,l?"wal":null].filter(Boolean).join("|"),f=(h=this.headers.Accept)!==null&&h!==void 0?h:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${c}; for="${f}"; options=${d};`,c==="json"?this:this}rollback(){var r;return((r=this.headers.Prefer)!==null&&r!==void 0?r:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return K.default=t,K}var ze;function De(){if(ze)return H;ze=1;var n=H&&H.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(H,"__esModule",{value:!0});const e=n(pt());class t extends e.default{eq(r,i){return this.url.searchParams.append(r,`eq.${i}`),this}neq(r,i){return this.url.searchParams.append(r,`neq.${i}`),this}gt(r,i){return this.url.searchParams.append(r,`gt.${i}`),this}gte(r,i){return this.url.searchParams.append(r,`gte.${i}`),this}lt(r,i){return this.url.searchParams.append(r,`lt.${i}`),this}lte(r,i){return this.url.searchParams.append(r,`lte.${i}`),this}like(r,i){return this.url.searchParams.append(r,`like.${i}`),this}likeAllOf(r,i){return this.url.searchParams.append(r,`like(all).{${i.join(",")}}`),this}likeAnyOf(r,i){return this.url.searchParams.append(r,`like(any).{${i.join(",")}}`),this}ilike(r,i){return this.url.searchParams.append(r,`ilike.${i}`),this}ilikeAllOf(r,i){return this.url.searchParams.append(r,`ilike(all).{${i.join(",")}}`),this}ilikeAnyOf(r,i){return this.url.searchParams.append(r,`ilike(any).{${i.join(",")}}`),this}is(r,i){return this.url.searchParams.append(r,`is.${i}`),this}in(r,i){const o=Array.from(new Set(i)).map(a=>typeof a=="string"&&new RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(r,`in.(${o})`),this}contains(r,i){return typeof i=="string"?this.url.searchParams.append(r,`cs.${i}`):Array.isArray(i)?this.url.searchParams.append(r,`cs.{${i.join(",")}}`):this.url.searchParams.append(r,`cs.${JSON.stringify(i)}`),this}containedBy(r,i){return typeof i=="string"?this.url.searchParams.append(r,`cd.${i}`):Array.isArray(i)?this.url.searchParams.append(r,`cd.{${i.join(",")}}`):this.url.searchParams.append(r,`cd.${JSON.stringify(i)}`),this}rangeGt(r,i){return this.url.searchParams.append(r,`sr.${i}`),this}rangeGte(r,i){return this.url.searchParams.append(r,`nxl.${i}`),this}rangeLt(r,i){return this.url.searchParams.append(r,`sl.${i}`),this}rangeLte(r,i){return this.url.searchParams.append(r,`nxr.${i}`),this}rangeAdjacent(r,i){return this.url.searchParams.append(r,`adj.${i}`),this}overlaps(r,i){return typeof i=="string"?this.url.searchParams.append(r,`ov.${i}`):this.url.searchParams.append(r,`ov.{${i.join(",")}}`),this}textSearch(r,i,{config:o,type:a}={}){let l="";a==="plain"?l="pl":a==="phrase"?l="ph":a==="websearch"&&(l="w");const c=o===void 0?"":`(${o})`;return this.url.searchParams.append(r,`${l}fts${c}.${i}`),this}match(r){return Object.entries(r).forEach(([i,o])=>{this.url.searchParams.append(i,`eq.${o}`)}),this}not(r,i,o){return this.url.searchParams.append(r,`not.${i}.${o}`),this}or(r,{foreignTable:i,referencedTable:o=i}={}){const a=o?`${o}.or`:"or";return this.url.searchParams.append(a,`(${r})`),this}filter(r,i,o){return this.url.searchParams.append(r,`${i}.${o}`),this}}return H.default=t,H}var Je;function _t(){if(Je)return J;Je=1;var n=J&&J.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(J,"__esModule",{value:!0});const e=n(De());class t{constructor(r,{headers:i={},schema:o,fetch:a}){this.url=r,this.headers=i,this.schema=o,this.fetch=a}select(r,{head:i=!1,count:o}={}){const a=i?"HEAD":"GET";let l=!1;const c=(r!=null?r:"*").split("").map(h=>/\s/.test(h)&&!l?"":(h==='"'&&(l=!l),h)).join("");return this.url.searchParams.set("select",c),o&&(this.headers.Prefer=`count=${o}`),new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(r,{count:i,defaultToNull:o=!0}={}){const a="POST",l=[];if(this.headers.Prefer&&l.push(this.headers.Prefer),i&&l.push(`count=${i}`),o||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(r)){const c=r.reduce((h,d)=>h.concat(Object.keys(d)),[]);if(c.length>0){const h=[...new Set(c)].map(d=>`"${d}"`);this.url.searchParams.set("columns",h.join(","))}}return new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}upsert(r,{onConflict:i,ignoreDuplicates:o=!1,count:a,defaultToNull:l=!0}={}){const c="POST",h=[`resolution=${o?"ignore":"merge"}-duplicates`];if(i!==void 0&&this.url.searchParams.set("on_conflict",i),this.headers.Prefer&&h.push(this.headers.Prefer),a&&h.push(`count=${a}`),l||h.push("missing=default"),this.headers.Prefer=h.join(","),Array.isArray(r)){const d=r.reduce((f,g)=>f.concat(Object.keys(g)),[]);if(d.length>0){const f=[...new Set(d)].map(g=>`"${g}"`);this.url.searchParams.set("columns",f.join(","))}}return new e.default({method:c,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}update(r,{count:i}={}){const o="PATCH",a=[];return this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),this.headers.Prefer=a.join(","),new e.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}delete({count:r}={}){const i="DELETE",o=[];return r&&o.push(`count=${r}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new e.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return J.default=t,J}var re={},ie={},He;function Wt(){return He||(He=1,Object.defineProperty(ie,"__esModule",{value:!0}),ie.version=void 0,ie.version="0.0.0-automated"),ie}var Ke;function zt(){if(Ke)return re;Ke=1,Object.defineProperty(re,"__esModule",{value:!0}),re.DEFAULT_HEADERS=void 0;const n=Wt();return re.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${n.version}`},re}var Ge;function Jt(){if(Ge)return z;Ge=1;var n=z&&z.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(z,"__esModule",{value:!0});const e=n(_t()),t=n(De()),s=zt();class r{constructor(o,{headers:a={},schema:l,fetch:c}={}){this.url=o,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),a),this.schemaName=l,this.fetch=c}from(o){const a=new URL(`${this.url}/${o}`);return new e.default(a,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(o){return new r(this.url,{headers:this.headers,schema:o,fetch:this.fetch})}rpc(o,a={},{head:l=!1,get:c=!1,count:h}={}){let d;const f=new URL(`${this.url}/rpc/${o}`);let g;l||c?(d=l?"HEAD":"GET",Object.entries(a).filter(([m,_])=>_!==void 0).map(([m,_])=>[m,Array.isArray(_)?`{${_.join(",")}}`:`${_}`]).forEach(([m,_])=>{f.searchParams.append(m,_)})):(d="POST",g=a);const p=Object.assign({},this.headers);return h&&(p.Prefer=`count=${h}`),new t.default({method:d,url:f,headers:p,schema:this.schemaName,body:g,fetch:this.fetch,allowEmpty:!1})}}return z.default=r,z}var Ve;function Ht(){if(Ve)return E;Ve=1;var n=E&&E.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(E,"__esModule",{value:!0}),E.PostgrestError=E.PostgrestBuilder=E.PostgrestTransformBuilder=E.PostgrestFilterBuilder=E.PostgrestQueryBuilder=E.PostgrestClient=void 0;const e=n(Jt());E.PostgrestClient=e.default;const t=n(_t());E.PostgrestQueryBuilder=t.default;const s=n(De());E.PostgrestFilterBuilder=s.default;const r=n(pt());E.PostgrestTransformBuilder=r.default;const i=n(gt());E.PostgrestBuilder=i.default;const o=n(ft());return E.PostgrestError=o.default,E.default={PostgrestClient:e.default,PostgrestQueryBuilder:t.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:r.default,PostgrestBuilder:i.default,PostgrestError:o.default},E}var Kt=Ht();const Gt=Rt(Kt),{PostgrestClient:Vt,PostgrestQueryBuilder:Ur,PostgrestFilterBuilder:Lr,PostgrestTransformBuilder:Dr,PostgrestBuilder:qr,PostgrestError:Br}=Gt;let Pe;typeof window=="undefined"?Pe=require("ws"):Pe=window.WebSocket;const Qt="2.11.10",Xt={"X-Client-Info":`realtime-js/${Qt}`},Yt="1.0.0",vt=1e4,Zt=1e3;var ee;(function(n){n[n.connecting=0]="connecting",n[n.open=1]="open",n[n.closing=2]="closing",n[n.closed=3]="closed"})(ee||(ee={}));var A;(function(n){n.closed="closed",n.errored="errored",n.joined="joined",n.joining="joining",n.leaving="leaving"})(A||(A={}));var x;(function(n){n.close="phx_close",n.error="phx_error",n.join="phx_join",n.reply="phx_reply",n.leave="phx_leave",n.access_token="access_token"})(x||(x={}));var Ae;(function(n){n.websocket="websocket"})(Ae||(Ae={}));var F;(function(n){n.Connecting="connecting",n.Open="open",n.Closing="closing",n.Closed="closed"})(F||(F={}));class es{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),i=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=s.decode(e.slice(o,o+r));o=o+r;const l=s.decode(e.slice(o,o+i));o=o+i;const c=JSON.parse(s.decode(e.slice(o,e.byteLength)));return{ref:null,topic:a,event:l,payload:c}}}class wt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var S;(function(n){n.abstime="abstime",n.bool="bool",n.date="date",n.daterange="daterange",n.float4="float4",n.float8="float8",n.int2="int2",n.int4="int4",n.int4range="int4range",n.int8="int8",n.int8range="int8range",n.json="json",n.jsonb="jsonb",n.money="money",n.numeric="numeric",n.oid="oid",n.reltime="reltime",n.text="text",n.time="time",n.timestamp="timestamp",n.timestamptz="timestamptz",n.timetz="timetz",n.tsrange="tsrange",n.tstzrange="tstzrange"})(S||(S={}));const Qe=(n,e,t={})=>{var s;const r=(s=t.skipTypes)!==null&&s!==void 0?s:[];return Object.keys(e).reduce((i,o)=>(i[o]=ts(o,n,e,r),i),{})},ts=(n,e,t,s)=>{const r=e.find(a=>a.name===n),i=r==null?void 0:r.type,o=t[n];return i&&!s.includes(i)?yt(i,o):$e(o)},yt=(n,e)=>{if(n.charAt(0)==="_"){const t=n.slice(1,n.length);return ns(e,t)}switch(n){case S.bool:return ss(e);case S.float4:case S.float8:case S.int2:case S.int4:case S.int8:case S.numeric:case S.oid:return rs(e);case S.json:case S.jsonb:return is(e);case S.timestamp:return os(e);case S.abstime:case S.date:case S.daterange:case S.int4range:case S.int8range:case S.money:case S.reltime:case S.text:case S.time:case S.timestamptz:case S.timetz:case S.tsrange:case S.tstzrange:return $e(e);default:return $e(e)}},$e=n=>n,ss=n=>{switch(n){case"t":return!0;case"f":return!1;default:return n}},rs=n=>{if(typeof n=="string"){const e=parseFloat(n);if(!Number.isNaN(e))return e}return n},is=n=>{if(typeof n=="string")try{return JSON.parse(n)}catch(e){return n}return n},ns=(n,e)=>{if(typeof n!="string")return n;const t=n.length-1,s=n[t];if(n[0]==="{"&&s==="}"){let i;const o=n.slice(1,t);try{i=JSON.parse("["+o+"]")}catch(a){i=o?o.split(","):[]}return i.map(a=>yt(e,a))}return n},os=n=>typeof n=="string"?n.replace(" ","T"):n,mt=n=>{let e=n;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class be{constructor(e,t,s={},r=vt){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t((s=this.receivedResp)===null||s===void 0?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(s=>s.status===e).forEach(s=>s.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Xe;(function(n){n.SYNC="sync",n.JOIN="join",n.LEAVE="leave"})(Xe||(Xe={}));class oe{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},r=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=oe.syncState(this.state,r,i,o),this.pendingDiffs.forEach(l=>{this.state=oe.syncDiff(this.state,l,i,o)}),this.pendingDiffs=[],a()}),this.channel._on(s.diff,{},r=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(r):(this.state=oe.syncDiff(this.state,r,i,o),a())}),this.onJoin((r,i,o)=>{this.channel._trigger("presence",{event:"join",key:r,currentPresences:i,newPresences:o})}),this.onLeave((r,i,o)=>{this.channel._trigger("presence",{event:"leave",key:r,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){const i=this.cloneDeep(e),o=this.transformState(t),a={},l={};return this.map(i,(c,h)=>{o[c]||(l[c]=h)}),this.map(o,(c,h)=>{const d=i[c];if(d){const f=h.map(_=>_.presence_ref),g=d.map(_=>_.presence_ref),p=h.filter(_=>g.indexOf(_.presence_ref)<0),m=d.filter(_=>f.indexOf(_.presence_ref)<0);p.length>0&&(a[c]=p),m.length>0&&(l[c]=m)}else a[c]=h}),this.syncDiff(i,{joins:a,leaves:l},s,r)}static syncDiff(e,t,s,r){const{joins:i,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(a,l)=>{var c;const h=(c=e[a])!==null&&c!==void 0?c:[];if(e[a]=this.cloneDeep(l),h.length>0){const d=e[a].map(g=>g.presence_ref),f=h.filter(g=>d.indexOf(g.presence_ref)<0);e[a].unshift(...f)}s(a,h,l)}),this.map(o,(a,l)=>{let c=e[a];if(!c)return;const h=l.map(d=>d.presence_ref);c=c.filter(d=>h.indexOf(d.presence_ref)<0),e[a]=c,r(a,c,l),c.length===0&&delete e[a]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,s)=>{const r=e[s];return"metas"in r?t[s]=r.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Ye;(function(n){n.ALL="*",n.INSERT="INSERT",n.UPDATE="UPDATE",n.DELETE="DELETE"})(Ye||(Ye={}));var Ze;(function(n){n.BROADCAST="broadcast",n.PRESENCE="presence",n.POSTGRES_CHANGES="postgres_changes",n.SYSTEM="system"})(Ze||(Ze={}));var U;(function(n){n.SUBSCRIBED="SUBSCRIBED",n.TIMED_OUT="TIMED_OUT",n.CLOSED="CLOSED",n.CHANNEL_ERROR="CHANNEL_ERROR"})(U||(U={}));class qe{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=A.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new be(this,x.join,this.params,this.timeout),this.rejoinTimer=new wt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=A.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(r=>r.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=A.closed,this.socket._remove(this)}),this._onError(r=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,r),this.state=A.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=A.errored,this.rejoinTimer.scheduleTimeout())}),this._on(x.reply,{},(r,i)=>{this._trigger(this._replyEventName(i),r)}),this.presence=new oe(this),this.broadcastEndpointURL=mt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:i,presence:o,private:a}}=this.params;this._onError(h=>e==null?void 0:e(U.CHANNEL_ERROR,h)),this._onClose(()=>e==null?void 0:e(U.CLOSED));const l={},c={broadcast:i,presence:o,postgres_changes:(r=(s=this.bindings.postgres_changes)===null||s===void 0?void 0:s.map(h=>h.filter))!==null&&r!==void 0?r:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",d=>u(this,[d],function*({postgres_changes:h}){var f;if(this.socket.setAuth(),h===void 0){e==null||e(U.SUBSCRIBED);return}else{const g=this.bindings.postgres_changes,p=(f=g==null?void 0:g.length)!==null&&f!==void 0?f:0,m=[];for(let _=0;_<p;_++){const T=g[_],{filter:{event:P,schema:v,table:b,filter:O}}=T,k=h&&h[_];if(k&&k.event===P&&k.schema===v&&k.table===b&&k.filter===O)m.push(Object.assign(Object.assign({},T),{id:k.id}));else{this.unsubscribe(),this.state=A.errored,e==null||e(U.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,e&&e(U.SUBSCRIBED);return}})).receive("error",h=>{this.state=A.errored,e==null||e(U.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(h).join(", ")||"error")))}).receive("timeout",()=>{e==null||e(U.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(s){return u(this,arguments,function*(e,t={}){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(){return u(this,arguments,function*(e={}){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,s){return this._on(e,t,s)}send(s){return u(this,arguments,function*(e,t={}){var r,i;if(!this._canPush()&&e.type==="broadcast"){const{event:o,payload:a}=e,c={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{const h=yield this._fetchWithTimeout(this.broadcastEndpointURL,c,(r=t.timeout)!==null&&r!==void 0?r:this.timeout);return yield(i=h.body)===null||i===void 0?void 0:i.cancel(),h.ok?"ok":"error"}catch(h){return h.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var a,l,c;const h=this._push(e.type,e,t.timeout||this.timeout);e.type==="broadcast"&&!(!((c=(l=(a=this.params)===null||a===void 0?void 0:a.config)===null||l===void 0?void 0:l.broadcast)===null||c===void 0)&&c.ack)&&o("ok"),h.receive("ok",()=>o("ok")),h.receive("error",()=>o("error")),h.receive("timeout",()=>o("timed out"))})})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=A.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(x.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(s=>{const r=new be(this,x.leave,{},e);r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}_fetchWithTimeout(e,t,s){return u(this,null,function*(){const r=new AbortController,i=setTimeout(()=>r.abort(),s),o=yield this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),o})}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new be(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;const o=e.toLocaleLowerCase(),{close:a,error:l,leave:c,join:h}=x;if(s&&[a,l,c,h].indexOf(o)>=0&&s!==this._joinRef())return;let f=this._onMessage(o,t,s);if(t&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(r=this.bindings.postgres_changes)===null||r===void 0||r.filter(g=>{var p,m,_;return((p=g.filter)===null||p===void 0?void 0:p.event)==="*"||((_=(m=g.filter)===null||m===void 0?void 0:m.event)===null||_===void 0?void 0:_.toLocaleLowerCase())===o}).map(g=>g.callback(f,s)):(i=this.bindings[o])===null||i===void 0||i.filter(g=>{var p,m,_,T,P,v;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in g){const b=g.id,O=(p=g.filter)===null||p===void 0?void 0:p.event;return b&&((m=t.ids)===null||m===void 0?void 0:m.includes(b))&&(O==="*"||(O==null?void 0:O.toLocaleLowerCase())===((_=t.data)===null||_===void 0?void 0:_.type.toLocaleLowerCase()))}else{const b=(P=(T=g==null?void 0:g.filter)===null||T===void 0?void 0:T.event)===null||P===void 0?void 0:P.toLocaleLowerCase();return b==="*"||b===((v=t==null?void 0:t.event)===null||v===void 0?void 0:v.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===o}).map(g=>{if(typeof f=="object"&&"ids"in f){const p=f.data,{schema:m,table:_,commit_timestamp:T,type:P,errors:v}=p;f=Object.assign(Object.assign({},{schema:m,table:_,commit_timestamp:T,eventType:P,new:{},old:{},errors:v}),this._getPayloadRecords(p))}g.callback(f,s)})}_isClosed(){return this.state===A.closed}_isJoined(){return this.state===A.joined}_isJoining(){return this.state===A.joining}_isLeaving(){return this.state===A.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(r=>{var i;return!(((i=r.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===s&&qe.isEqual(r.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(x.close,{},e)}_onError(e){this._on(x.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=A.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=Qe(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=Qe(e.columns,e.old_record)),t}}const et=()=>{},as=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class ls{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers=Xt,this.params={},this.timeout=vt,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=et,this.ref=0,this.logger=et,this.conn=null,this.sendBuffer=[],this.serializer=new es,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch=="undefined"?o=(...a)=>he(()=>u(this,null,function*(){const{default:l}=yield Promise.resolve().then(()=>se);return{default:l}}),void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${e}/${Ae.websocket}`,this.httpEndpoint=mt(e),t!=null&&t.transport?this.transport=t.transport:this.transport=null,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),(t!=null&&t.logLevel||t!=null&&t.log_level)&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=(s=t==null?void 0:t.params)===null||s===void 0?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(i,o)=>o(JSON.stringify(i)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new wt(()=>u(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(t==null?void 0:t.fetch),t!=null&&t.worker){if(typeof window!="undefined"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(t==null?void 0:t.worker)||!1,this.workerUrl=t==null?void 0:t.workerUrl}this.accessToken=(t==null?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=Pe),this.transport){typeof window!="undefined"&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new cs(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Yt}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t!=null?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(s=>s.teardown()))}getChannels(){return this.channels}removeChannel(e){return u(this,null,function*(){const t=yield e.unsubscribe();return this.channels=this.channels.filter(s=>s._joinRef!==e._joinRef),this.channels.length===0&&this.disconnect(),t})}removeAllChannels(){return u(this,null,function*(){const e=yield Promise.all(this.channels.map(t=>t.unsubscribe()));return this.channels=[],this.disconnect(),e})}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case ee.connecting:return F.Connecting;case ee.open:return F.Open;case ee.closing:return F.Closing;default:return F.Closed}}isConnected(){return this.connectionState()===F.Open}channel(e,t={config:{}}){const s=`realtime:${e}`,r=this.getChannels().find(i=>i.topic===s);if(r)return r;{const i=new qe(`realtime:${e}`,t,this);return this.channels.push(i),i}}push(e){const{topic:t,event:s,payload:r,ref:i}=e,o=()=>{this.encode(e,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?o():this.sendBuffer.push(o)}setAuth(e=null){return u(this,null,function*(){let t=e||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(s=>{t&&s.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),s.joinedOnce&&s._isJoined()&&s._push(x.access_token,{access_token:t})}))})}sendHeartbeat(){return u(this,null,function*(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(e=this.conn)===null||e===void 0||e.close(Zt,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),yield this.setAuth()})}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(s=>s.topic===e&&(s._isJoined()||s._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:s,event:r,payload:i,ref:o}=t;s==="phoenix"&&r==="phx_reply"&&this.heartbeatCallback(t.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${s} ${r} ${o&&"("+o+")"||""}`,i),Array.from(this.channels).filter(a=>a._isMember(s)).forEach(a=>a._trigger(r,i,o)),this.stateChangeCallbacks.message.forEach(a=>a(t))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{t.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(x.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{const s=new Blob([as],{type:"application/javascript"});t=URL.createObjectURL(s)}return t}}class cs{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=ee.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class Be extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function j(n){return typeof n=="object"&&n!==null&&"__isStorageError"in n}class hs extends Be{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Re extends Be{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var us=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const bt=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=(...t)=>he(()=>u(null,null,function*(){const{default:s}=yield Promise.resolve().then(()=>se);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},ds=()=>us(void 0,void 0,void 0,function*(){return typeof Response=="undefined"?(yield he(()=>Promise.resolve().then(()=>se),void 0)).Response:Response}),Ce=n=>{if(Array.isArray(n))return n.map(t=>Ce(t));if(typeof n=="function"||n!==Object(n))return n;const e={};return Object.entries(n).forEach(([t,s])=>{const r=t.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));e[r]=Ce(s)}),e};var W=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const ke=n=>n.msg||n.message||n.error_description||n.error||JSON.stringify(n),fs=(n,e,t)=>W(void 0,void 0,void 0,function*(){const s=yield ds();n instanceof s&&!(t!=null&&t.noResolveJson)?n.json().then(r=>{e(new hs(ke(r),n.status||500))}).catch(r=>{e(new Re(ke(r),r))}):e(new Re(ke(n),n))}),gs=(n,e,t,s)=>{const r={method:n,headers:(e==null?void 0:e.headers)||{}};return n==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),s&&(r.body=JSON.stringify(s)),Object.assign(Object.assign({},r),t))};function ue(n,e,t,s,r,i){return W(this,void 0,void 0,function*(){return new Promise((o,a)=>{n(t,gs(e,s,r,i)).then(l=>{if(!l.ok)throw l;return s!=null&&s.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>fs(l,a,s))})})}function we(n,e,t,s){return W(this,void 0,void 0,function*(){return ue(n,"GET",e,t,s)})}function D(n,e,t,s,r){return W(this,void 0,void 0,function*(){return ue(n,"POST",e,s,r,t)})}function ps(n,e,t,s,r){return W(this,void 0,void 0,function*(){return ue(n,"PUT",e,s,r,t)})}function _s(n,e,t,s){return W(this,void 0,void 0,function*(){return ue(n,"HEAD",e,Object.assign(Object.assign({},t),{noResolveJson:!0}),s)})}function kt(n,e,t,s,r){return W(this,void 0,void 0,function*(){return ue(n,"DELETE",e,s,r,t)})}var $=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const vs={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},tt={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ws{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=bt(r)}uploadOrUpdate(e,t,s,r){return $(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},tt),r);let a=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob!="undefined"&&s instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",s)):typeof FormData!="undefined"&&s instanceof FormData?(i=s,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=s,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),r!=null&&r.headers&&(a=Object.assign(Object.assign({},a),r.headers));const c=this._removeEmptyFolders(t),h=this._getFinalPath(c),d=yield this.fetch(`${this.url}/object/${h}`,Object.assign({method:e,body:i,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),f=yield d.json();return d.ok?{data:{path:c,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(i){if(j(i))return{data:null,error:i};throw i}})}upload(e,t,s){return $(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return $(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(e),o=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",t);try{let l;const c=Object.assign({upsert:tt.upsert},r),h=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob!="undefined"&&s instanceof Blob?(l=new FormData,l.append("cacheControl",c.cacheControl),l.append("",s)):typeof FormData!="undefined"&&s instanceof FormData?(l=s,l.append("cacheControl",c.cacheControl)):(l=s,h["cache-control"]=`max-age=${c.cacheControl}`,h["content-type"]=c.contentType);const d=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:h}),f=yield d.json();return d.ok?{data:{path:i,fullPath:f.Key},error:null}:{data:null,error:f}}catch(l){if(j(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(e,t){return $(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);t!=null&&t.upsert&&(r["x-upsert"]="true");const i=yield D(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),o=new URL(this.url+i.url),a=o.searchParams.get("token");if(!a)throw new Be("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:a},error:null}}catch(s){if(j(s))return{data:null,error:s};throw s}})}update(e,t,s){return $(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return $(this,void 0,void 0,function*(){try{return{data:yield D(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(j(r))return{data:null,error:r};throw r}})}copy(e,t,s){return $(this,void 0,void 0,function*(){try{return{data:{path:(yield D(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(j(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,s){return $(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield D(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},s!=null&&s.transform?{transform:s.transform}:{}),{headers:this.headers});const o=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(r){if(j(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,s){return $(this,void 0,void 0,function*(){try{const r=yield D(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return{data:r.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(r){if(j(r))return{data:null,error:r};throw r}})}download(e,t){return $(this,void 0,void 0,function*(){const r=typeof(t==null?void 0:t.transform)!="undefined"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),o=i?`?${i}`:"";try{const a=this._getFinalPath(e);return{data:yield(yield we(this.fetch,`${this.url}/${r}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(j(a))return{data:null,error:a};throw a}})}info(e){return $(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const s=yield we(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Ce(s),error:null}}catch(s){if(j(s))return{data:null,error:s};throw s}})}exists(e){return $(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield _s(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(j(s)&&s instanceof Re){const r=s.originalError;if([400,404].includes(r==null?void 0:r.status))return{data:!1,error:s}}throw s}})}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],i=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";i!==""&&r.push(i);const a=typeof(t==null?void 0:t.transform)!="undefined"?"render/image":"object",l=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});l!==""&&r.push(l);let c=r.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${s}${c}`)}}}remove(e){return $(this,void 0,void 0,function*(){try{return{data:yield kt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(j(t))return{data:null,error:t};throw t}})}list(e,t,s){return $(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},vs),t),{prefix:e||""});return{data:yield D(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(j(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return typeof Buffer!="undefined"?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const ys="2.7.1",ms={"X-Client-Info":`storage-js/${ys}`};var V=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,[])).next())})};class bs{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},ms),t),this.fetch=bt(s)}listBuckets(){return V(this,void 0,void 0,function*(){try{return{data:yield we(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(j(e))return{data:null,error:e};throw e}})}getBucket(e){return V(this,void 0,void 0,function*(){try{return{data:yield we(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(j(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return V(this,void 0,void 0,function*(){try{return{data:yield D(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(j(s))return{data:null,error:s};throw s}})}updateBucket(e,t){return V(this,void 0,void 0,function*(){try{return{data:yield ps(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(j(s))return{data:null,error:s};throw s}})}emptyBucket(e){return V(this,void 0,void 0,function*(){try{return{data:yield D(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(j(t))return{data:null,error:t};throw t}})}deleteBucket(e){return V(this,void 0,void 0,function*(){try{return{data:yield kt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(j(t))return{data:null,error:t};throw t}})}}class ks extends bs{constructor(e,t={},s){super(e,t,s)}from(e){return new ws(this.url,this.headers,e,this.fetch)}}const Ss="2.50.0";let ne="";typeof Deno!="undefined"?ne="deno":typeof document!="undefined"?ne="web":typeof navigator!="undefined"&&navigator.product==="ReactNative"?ne="react-native":ne="node";const Ts={"X-Client-Info":`supabase-js-${ne}/${Ss}`},Es={headers:Ts},js={schema:"public"},Os={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Ps={};var As=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,e||[])).next())})};const $s=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=ut:e=fetch,(...t)=>e(...t)},Rs=()=>typeof Headers=="undefined"?dt:Headers,Cs=(n,e,t)=>{const s=$s(t),r=Rs();return(i,o)=>As(void 0,void 0,void 0,function*(){var a;const l=(a=yield e())!==null&&a!==void 0?a:n;let c=new r(o==null?void 0:o.headers);return c.has("apikey")||c.set("apikey",n),c.has("Authorization")||c.set("Authorization",`Bearer ${l}`),s(i,Object.assign(Object.assign({},o),{headers:c}))})};var xs=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,[])).next())})};function Is(n){return n.endsWith("/")?n:n+"/"}function Us(n,e){var t,s;const{db:r,auth:i,realtime:o,global:a}=n,{db:l,auth:c,realtime:h,global:d}=e,f={db:Object.assign(Object.assign({},l),r),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},h),o),global:Object.assign(Object.assign(Object.assign({},d),a),{headers:Object.assign(Object.assign({},(t=d==null?void 0:d.headers)!==null&&t!==void 0?t:{}),(s=a==null?void 0:a.headers)!==null&&s!==void 0?s:{})}),accessToken:()=>xs(this,void 0,void 0,function*(){return""})};return n.accessToken?f.accessToken=n.accessToken:delete f.accessToken,f}const St="2.70.0",Z=30*1e3,xe=3,Se=xe*Z,Ls="http://localhost:9999",Ds="supabase.auth.token",qs={"X-Client-Info":`gotrue-js/${St}`},Ie="X-Supabase-Api-Version",Tt={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Bs=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,Ns=6e5;class Ne extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function w(n){return typeof n=="object"&&n!==null&&"__isAuthError"in n}class Ms extends Ne{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}function Fs(n){return w(n)&&n.name==="AuthApiError"}class Et extends Ne{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class B extends Ne{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class L extends B{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function Ws(n){return w(n)&&n.name==="AuthSessionMissingError"}class fe extends B{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class ge extends B{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class pe extends B{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function zs(n){return w(n)&&n.name==="AuthImplicitGrantRedirectError"}class st extends B{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ue extends B{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Te(n){return w(n)&&n.name==="AuthRetryableFetchError"}class rt extends B{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class ae extends B{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const ye="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),it=` 	
\r=`.split(""),Js=(()=>{const n=new Array(128);for(let e=0;e<n.length;e+=1)n[e]=-1;for(let e=0;e<it.length;e+=1)n[it[e].charCodeAt(0)]=-2;for(let e=0;e<ye.length;e+=1)n[ye[e].charCodeAt(0)]=e;return n})();function nt(n,e,t){if(n!==null)for(e.queue=e.queue<<8|n,e.queuedBits+=8;e.queuedBits>=6;){const s=e.queue>>e.queuedBits-6&63;t(ye[s]),e.queuedBits-=6}else if(e.queuedBits>0)for(e.queue=e.queue<<6-e.queuedBits,e.queuedBits=6;e.queuedBits>=6;){const s=e.queue>>e.queuedBits-6&63;t(ye[s]),e.queuedBits-=6}}function jt(n,e,t){const s=Js[n];if(s>-1)for(e.queue=e.queue<<6|s,e.queuedBits+=6;e.queuedBits>=8;)t(e.queue>>e.queuedBits-8&255),e.queuedBits-=8;else{if(s===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(n)}"`)}}function ot(n){const e=[],t=o=>{e.push(String.fromCodePoint(o))},s={utf8seq:0,codepoint:0},r={queue:0,queuedBits:0},i=o=>{Gs(o,s,t)};for(let o=0;o<n.length;o+=1)jt(n.charCodeAt(o),r,i);return e.join("")}function Hs(n,e){if(n<=127){e(n);return}else if(n<=2047){e(192|n>>6),e(128|n&63);return}else if(n<=65535){e(224|n>>12),e(128|n>>6&63),e(128|n&63);return}else if(n<=1114111){e(240|n>>18),e(128|n>>12&63),e(128|n>>6&63),e(128|n&63);return}throw new Error(`Unrecognized Unicode codepoint: ${n.toString(16)}`)}function Ks(n,e){for(let t=0;t<n.length;t+=1){let s=n.charCodeAt(t);if(s>55295&&s<=56319){const r=(s-55296)*1024&65535;s=(n.charCodeAt(t+1)-56320&65535|r)+65536,t+=1}Hs(s,e)}}function Gs(n,e,t){if(e.utf8seq===0){if(n<=127){t(n);return}for(let s=1;s<6;s+=1)if((n>>7-s&1)===0){e.utf8seq=s;break}if(e.utf8seq===2)e.codepoint=n&31;else if(e.utf8seq===3)e.codepoint=n&15;else if(e.utf8seq===4)e.codepoint=n&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(n<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|n&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}function Vs(n){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};for(let r=0;r<n.length;r+=1)jt(n.charCodeAt(r),t,s);return new Uint8Array(e)}function Qs(n){const e=[];return Ks(n,t=>e.push(t)),new Uint8Array(e)}function Xs(n){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};return n.forEach(r=>nt(r,t,s)),nt(null,t,s),e.join("")}function Ys(n){return Math.round(Date.now()/1e3)+n}function Zs(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){const e=Math.random()*16|0;return(n=="x"?e:e&3|8).toString(16)})}const C=()=>typeof window!="undefined"&&typeof document!="undefined",N={tested:!1,writable:!1},le=()=>{if(!C())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch(e){return!1}if(N.tested)return N.writable;const n=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(n,n),globalThis.localStorage.removeItem(n),N.tested=!0,N.writable=!0}catch(e){N.tested=!0,N.writable=!1}return N.writable};function er(n){const e={},t=new URL(n);if(t.hash&&t.hash[0]==="#")try{new URLSearchParams(t.hash.substring(1)).forEach((r,i)=>{e[i]=r})}catch(s){}return t.searchParams.forEach((s,r)=>{e[r]=s}),e}const Ot=n=>{let e;return n?e=n:typeof fetch=="undefined"?e=(...t)=>he(()=>u(null,null,function*(){const{default:s}=yield Promise.resolve().then(()=>se);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},tr=n=>typeof n=="object"&&n!==null&&"status"in n&&"ok"in n&&"json"in n&&typeof n.json=="function",Pt=(n,e,t)=>u(null,null,function*(){yield n.setItem(e,JSON.stringify(t))}),_e=(n,e)=>u(null,null,function*(){const t=yield n.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch(s){return t}}),ve=(n,e)=>u(null,null,function*(){yield n.removeItem(e)});class me{constructor(){this.promise=new me.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}me.promiseConstructor=Promise;function Ee(n){const e=n.split(".");if(e.length!==3)throw new ae("Invalid JWT structure");for(let s=0;s<e.length;s++)if(!Bs.test(e[s]))throw new ae("JWT not in base64url format");return{header:JSON.parse(ot(e[0])),payload:JSON.parse(ot(e[1])),signature:Vs(e[2]),raw:{header:e[0],payload:e[1]}}}function sr(n){return u(this,null,function*(){return yield new Promise(e=>{setTimeout(()=>e(null),n)})})}function rr(n,e){return new Promise((s,r)=>{u(null,null,function*(){for(let i=0;i<1/0;i++)try{const o=yield n(i);if(!e(i,null,o)){s(o);return}}catch(o){if(!e(i,o)){r(o);return}}})})}function ir(n){return("0"+n.toString(16)).substr(-2)}function nr(){const e=new Uint32Array(56);if(typeof crypto=="undefined"){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",s=t.length;let r="";for(let i=0;i<56;i++)r+=t.charAt(Math.floor(Math.random()*s));return r}return crypto.getRandomValues(e),Array.from(e,ir).join("")}function or(n){return u(this,null,function*(){const t=new TextEncoder().encode(n),s=yield crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map(i=>String.fromCharCode(i)).join("")})}function ar(n){return u(this,null,function*(){if(!(typeof crypto!="undefined"&&typeof crypto.subtle!="undefined"&&typeof TextEncoder!="undefined"))return n;const t=yield or(n);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function Q(n,e,t=!1){return u(this,null,function*(){const s=nr();let r=s;t&&(r+="/PASSWORD_RECOVERY"),yield Pt(n,`${e}-code-verifier`,r);const i=yield ar(s);return[i,s===i?"plain":"s256"]})}const lr=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function cr(n){const e=n.headers.get(Ie);if(!e||!e.match(lr))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch(t){return null}}function hr(n){if(!n)throw new Error("Missing exp claim");const e=Math.floor(Date.now()/1e3);if(n<=e)throw new Error("JWT has expired")}function ur(n){switch(n){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const dr=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function X(n){if(!dr.test(n))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var fr=function(n,e){var t={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&e.indexOf(s)<0&&(t[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(n);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(n,s[r])&&(t[s[r]]=n[s[r]]);return t};const M=n=>n.msg||n.message||n.error_description||n.error||JSON.stringify(n),gr=[502,503,504];function at(n){return u(this,null,function*(){var e;if(!tr(n))throw new Ue(M(n),0);if(gr.includes(n.status))throw new Ue(M(n),n.status);let t;try{t=yield n.json()}catch(i){throw new Et(M(i),i)}let s;const r=cr(n);if(r&&r.getTime()>=Tt["2024-01-01"].timestamp&&typeof t=="object"&&t&&typeof t.code=="string"?s=t.code:typeof t=="object"&&t&&typeof t.error_code=="string"&&(s=t.error_code),s){if(s==="weak_password")throw new rt(M(t),n.status,((e=t.weak_password)===null||e===void 0?void 0:e.reasons)||[]);if(s==="session_not_found")throw new L}else if(typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new rt(M(t),n.status,t.weak_password.reasons);throw new Ms(M(t),n.status||500,s)})}const pr=(n,e,t,s)=>{const r={method:n,headers:(e==null?void 0:e.headers)||{}};return n==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),r.body=JSON.stringify(s),Object.assign(Object.assign({},r),t))};function y(n,e,t,s){return u(this,null,function*(){var r;const i=Object.assign({},s==null?void 0:s.headers);i[Ie]||(i[Ie]=Tt["2024-01-01"].name),s!=null&&s.jwt&&(i.Authorization=`Bearer ${s.jwt}`);const o=(r=s==null?void 0:s.query)!==null&&r!==void 0?r:{};s!=null&&s.redirectTo&&(o.redirect_to=s.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=yield _r(n,e,t+a,{headers:i,noResolveJson:s==null?void 0:s.noResolveJson},{},s==null?void 0:s.body);return s!=null&&s.xform?s==null?void 0:s.xform(l):{data:Object.assign({},l),error:null}})}function _r(n,e,t,s,r,i){return u(this,null,function*(){const o=pr(e,s,r,i);let a;try{a=yield n(t,Object.assign({},o))}catch(l){throw new Ue(M(l),0)}if(a.ok||(yield at(a)),s!=null&&s.noResolveJson)return a;try{return yield a.json()}catch(l){yield at(l)}})}function I(n){var e;let t=null;mr(n)&&(t=Object.assign({},n),n.expires_at||(t.expires_at=Ys(n.expires_in)));const s=(e=n.user)!==null&&e!==void 0?e:n;return{data:{session:t,user:s},error:null}}function lt(n){const e=I(n);return!e.error&&n.weak_password&&typeof n.weak_password=="object"&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.message&&typeof n.weak_password.message=="string"&&n.weak_password.reasons.reduce((t,s)=>t&&typeof s=="string",!0)&&(e.data.weak_password=n.weak_password),e}function q(n){var e;return{data:{user:(e=n.user)!==null&&e!==void 0?e:n},error:null}}function vr(n){return{data:n,error:null}}function wr(n){const{action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:i}=n,o=fr(n,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:i},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function yr(n){return n}function mr(n){return n.access_token&&n.refresh_token&&n.expires_in}const je=["global","local","others"];var br=function(n,e){var t={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&e.indexOf(s)<0&&(t[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(n);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(n,s[r])&&(t[s[r]]=n[s[r]]);return t};class kr{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=Ot(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(s){return u(this,arguments,function*(e,t=je[0]){if(je.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${je.join(", ")}`);try{return yield y(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(w(r))return{data:null,error:r};throw r}})}inviteUserByEmail(s){return u(this,arguments,function*(e,t={}){try{return yield y(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:q})}catch(r){if(w(r))return{data:{user:null},error:r};throw r}})}generateLink(e){return u(this,null,function*(){try{const{options:t}=e,s=br(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=s==null?void 0:s.newEmail,delete r.newEmail),yield y(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:wr,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(w(t))return{data:{properties:null,user:null},error:t};throw t}})}createUser(e){return u(this,null,function*(){try{return yield y(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:q})}catch(t){if(w(t))return{data:{user:null},error:t};throw t}})}listUsers(e){return u(this,null,function*(){var t,s,r,i,o,a,l;try{const c={nextPage:null,lastPage:0,total:0},h=yield y(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(s=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&s!==void 0?s:"",per_page:(i=(r=e==null?void 0:e.perPage)===null||r===void 0?void 0:r.toString())!==null&&i!==void 0?i:""},xform:yr});if(h.error)throw h.error;const d=yield h.json(),f=(o=h.headers.get("x-total-count"))!==null&&o!==void 0?o:0,g=(l=(a=h.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return g.length>0&&(g.forEach(p=>{const m=parseInt(p.split(";")[0].split("=")[1].substring(0,1)),_=JSON.parse(p.split(";")[1].split("=")[1]);c[`${_}Page`]=m}),c.total=parseInt(f)),{data:Object.assign(Object.assign({},d),c),error:null}}catch(c){if(w(c))return{data:{users:[]},error:c};throw c}})}getUserById(e){return u(this,null,function*(){X(e);try{return yield y(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:q})}catch(t){if(w(t))return{data:{user:null},error:t};throw t}})}updateUserById(e,t){return u(this,null,function*(){X(e);try{return yield y(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:q})}catch(s){if(w(s))return{data:{user:null},error:s};throw s}})}deleteUser(e,t=!1){return u(this,null,function*(){X(e);try{return yield y(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:q})}catch(s){if(w(s))return{data:{user:null},error:s};throw s}})}_listFactors(e){return u(this,null,function*(){X(e.userId);try{const{data:t,error:s}=yield y(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:r=>({data:{factors:r},error:null})});return{data:t,error:s}}catch(t){if(w(t))return{data:null,error:t};throw t}})}_deleteFactor(e){return u(this,null,function*(){X(e.userId),X(e.id);try{return{data:yield y(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(w(t))return{data:null,error:t};throw t}})}}const Sr={getItem:n=>le()?globalThis.localStorage.getItem(n):null,setItem:(n,e)=>{le()&&globalThis.localStorage.setItem(n,e)},removeItem:n=>{le()&&globalThis.localStorage.removeItem(n)}};function ct(n={}){return{getItem:e=>n[e]||null,setItem:(e,t)=>{n[e]=t},removeItem:e=>{delete n[e]}}}function Tr(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(n){typeof self!="undefined"&&(self.globalThis=self)}}const Y={debug:!!(globalThis&&le()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class At extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class Er extends At{}function jr(n,e,t){return u(this,null,function*(){Y.debug;const s=new globalThis.AbortController;return e>0&&setTimeout(()=>{s.abort(),Y.debug},e),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(n,e===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},r=>u(null,null,function*(){if(r){Y.debug;try{return yield t()}finally{Y.debug}}else{if(e===0)throw Y.debug,new Er(`Acquiring an exclusive Navigator LockManager lock "${n}" immediately failed`);if(Y.debug)try{const i=yield globalThis.navigator.locks.query()}catch(i){}return yield t()}})))})}Tr();const Or={url:Ls,storageKey:Ds,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:qs,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function ht(n,e,t){return u(this,null,function*(){return yield t()})}class ce{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ce.nextInstanceID,ce.nextInstanceID+=1,this.instanceID>0&&C();const r=Object.assign(Object.assign({},Or),e);if(this.logDebugMessages=!!r.debug,typeof r.debug=="function"&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new kr({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=Ot(r.fetch),this.lock=r.lock||ht,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:C()&&(!((t=globalThis==null?void 0:globalThis.navigator)===null||t===void 0)&&t.locks)?this.lock=jr:this.lock=ht,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:le()?this.storage=Sr:(this.memoryStorage={},this.storage=ct(this.memoryStorage)):(this.memoryStorage={},this.storage=ct(this.memoryStorage)),C()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){}(s=this.broadcastChannel)===null||s===void 0||s.addEventListener("message",i=>u(this,null,function*(){this._debug("received broadcast notification from other tab or client",i),yield this._notifyAllSubscribers(i.data.event,i.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${St}) ${new Date().toISOString()}`,...e),this}initialize(){return u(this,null,function*(){return this.initializePromise?yield this.initializePromise:(this.initializePromise=u(this,null,function*(){return yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._initialize()}))}),yield this.initializePromise)})}_initialize(){return u(this,null,function*(){var e;try{const t=er(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":(yield this._isPKCECallback(t))&&(s="pkce"),C()&&this.detectSessionInUrl&&s!=="none"){const{data:r,error:i}=yield this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),zs(i)){const l=(e=i.details)===null||e===void 0?void 0:e.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return yield this._removeSession(),{error:i}}const{session:o,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),yield this._saveSession(o),setTimeout(()=>u(this,null,function*(){a==="recovery"?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",o):yield this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(t){return w(t)?{error:t}:{error:new Et("Unexpected error during initialization",t)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(e){return u(this,null,function*(){var t,s,r;try{const i=yield y(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(s=(t=e==null?void 0:e.options)===null||t===void 0?void 0:t.data)!==null&&s!==void 0?s:{},gotrue_meta_security:{captcha_token:(r=e==null?void 0:e.options)===null||r===void 0?void 0:r.captchaToken}},xform:I}),{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,c=o.user;return o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(w(i))return{data:{user:null,session:null},error:i};throw i}})}signUp(e){return u(this,null,function*(){var t,s,r;try{let i;if("email"in e){const{email:h,password:d,options:f}=e;let g=null,p=null;this.flowType==="pkce"&&([g,p]=yield Q(this.storage,this.storageKey)),i=yield y(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:h,password:d,data:(t=f==null?void 0:f.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:g,code_challenge_method:p},xform:I})}else if("phone"in e){const{phone:h,password:d,options:f}=e;i=yield y(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:h,password:d,data:(s=f==null?void 0:f.data)!==null&&s!==void 0?s:{},channel:(r=f==null?void 0:f.channel)!==null&&r!==void 0?r:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:I})}else throw new ge("You must provide either an email or phone number and a password");const{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,c=o.user;return o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(w(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithPassword(e){return u(this,null,function*(){try{let t;if("email"in e){const{email:i,password:o,options:a}=e;t=yield y(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:lt})}else if("phone"in e){const{phone:i,password:o,options:a}=e;t=yield y(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:lt})}else throw new ge("You must provide either an email or phone number and a password");const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:!s||!s.session||!s.user?{data:{user:null,session:null},error:new fe}:(s.session&&(yield this._saveSession(s.session),yield this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r})}catch(t){if(w(t))return{data:{user:null,session:null},error:t};throw t}})}signInWithOAuth(e){return u(this,null,function*(){var t,s,r,i;return yield this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(s=e.options)===null||s===void 0?void 0:s.scopes,queryParams:(r=e.options)===null||r===void 0?void 0:r.queryParams,skipBrowserRedirect:(i=e.options)===null||i===void 0?void 0:i.skipBrowserRedirect})})}exchangeCodeForSession(e){return u(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>u(this,null,function*(){return this._exchangeCodeForSession(e)}))})}signInWithWeb3(e){return u(this,null,function*(){const{chain:t}=e;if(t==="solana")return yield this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)})}signInWithSolana(e){return u(this,null,function*(){var t,s,r,i,o,a,l,c,h,d,f,g;let p,m;if("message"in e)p=e.message,m=e.signature;else{const{chain:_,wallet:T,statement:P,options:v}=e;let b;if(C())if(typeof T=="object")b=T;else{const k=window;if("solana"in k&&typeof k.solana=="object"&&("signIn"in k.solana&&typeof k.solana.signIn=="function"||"signMessage"in k.solana&&typeof k.solana.signMessage=="function"))b=k.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof T!="object"||!(v!=null&&v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");b=T}const O=new URL((t=v==null?void 0:v.url)!==null&&t!==void 0?t:window.location.href);if("signIn"in b&&b.signIn){const k=yield b.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},v==null?void 0:v.signInWithSolana),{version:"1",domain:O.host,uri:O.href}),P?{statement:P}:null));let R;if(Array.isArray(k)&&k[0]&&typeof k[0]=="object")R=k[0];else if(k&&typeof k=="object"&&"signedMessage"in k&&"signature"in k)R=k;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in R&&"signature"in R&&(typeof R.signedMessage=="string"||R.signedMessage instanceof Uint8Array)&&R.signature instanceof Uint8Array)p=typeof R.signedMessage=="string"?R.signedMessage:new TextDecoder().decode(R.signedMessage),m=R.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in b)||typeof b.signMessage!="function"||!("publicKey"in b)||typeof b!="object"||!b.publicKey||!("toBase58"in b.publicKey)||typeof b.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");p=[`${O.host} wants you to sign in with your Solana account:`,b.publicKey.toBase58(),...P?["",P,""]:[""],"Version: 1",`URI: ${O.href}`,`Issued At: ${(r=(s=v==null?void 0:v.signInWithSolana)===null||s===void 0?void 0:s.issuedAt)!==null&&r!==void 0?r:new Date().toISOString()}`,...!((i=v==null?void 0:v.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...!((o=v==null?void 0:v.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...!((a=v==null?void 0:v.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...!((l=v==null?void 0:v.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...!((c=v==null?void 0:v.signInWithSolana)===null||c===void 0)&&c.requestId?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...!((d=(h=v==null?void 0:v.signInWithSolana)===null||h===void 0?void 0:h.resources)===null||d===void 0)&&d.length?["Resources",...v.signInWithSolana.resources.map(R=>`- ${R}`)]:[]].join(`
`);const k=yield b.signMessage(new TextEncoder().encode(p),"utf8");if(!k||!(k instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");m=k}}try{const{data:_,error:T}=yield y(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:p,signature:Xs(m)},!((f=e.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(g=e.options)===null||g===void 0?void 0:g.captchaToken}}:null),xform:I});if(T)throw T;return!_||!_.session||!_.user?{data:{user:null,session:null},error:new fe}:(_.session&&(yield this._saveSession(_.session),yield this._notifyAllSubscribers("SIGNED_IN",_.session)),{data:Object.assign({},_),error:T})}catch(_){if(w(_))return{data:{user:null,session:null},error:_};throw _}})}_exchangeCodeForSession(e){return u(this,null,function*(){const t=yield _e(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(t!=null?t:"").split("/");try{const{data:i,error:o}=yield y(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:I});if(yield ve(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new fe}:(i.session&&(yield this._saveSession(i.session),yield this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:r!=null?r:null}),error:o})}catch(i){if(w(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}})}signInWithIdToken(e){return u(this,null,function*(){try{const{options:t,provider:s,token:r,access_token:i,nonce:o}=e,a=yield y(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:t==null?void 0:t.captchaToken}},xform:I}),{data:l,error:c}=a;return c?{data:{user:null,session:null},error:c}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new fe}:(l.session&&(yield this._saveSession(l.session),yield this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c})}catch(t){if(w(t))return{data:{user:null,session:null},error:t};throw t}})}signInWithOtp(e){return u(this,null,function*(){var t,s,r,i,o;try{if("email"in e){const{email:a,options:l}=e;let c=null,h=null;this.flowType==="pkce"&&([c,h]=yield Q(this.storage,this.storageKey));const{error:d}=yield y(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:c,code_challenge_method:h},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in e){const{phone:a,options:l}=e,{data:c,error:h}=yield y(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(r=l==null?void 0:l.data)!==null&&r!==void 0?r:{},create_user:(i=l==null?void 0:l.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:h}}throw new ge("You must provide either an email or phone number.")}catch(a){if(w(a))return{data:{user:null,session:null},error:a};throw a}})}verifyOtp(e){return u(this,null,function*(){var t,s;try{let r,i;"options"in e&&(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo,i=(s=e.options)===null||s===void 0?void 0:s.captchaToken);const{data:o,error:a}=yield y(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:I});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,c=o.user;return l!=null&&l.access_token&&(yield this._saveSession(l),yield this._notifyAllSubscribers(e.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(r){if(w(r))return{data:{user:null,session:null},error:r};throw r}})}signInWithSSO(e){return u(this,null,function*(){var t,s,r;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=yield Q(this.storage,this.storageKey)),yield y(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(s=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&s!==void 0?s:void 0}),!((r=e==null?void 0:e.options)===null||r===void 0)&&r.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:vr})}catch(i){if(w(i))return{data:null,error:i};throw i}})}reauthenticate(){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return u(this,null,function*(){try{return yield this._useSession(e=>u(this,null,function*(){const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new L;const{error:r}=yield y(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(w(e))return{data:{user:null,session:null},error:e};throw e}})}resend(e){return u(this,null,function*(){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:i}=e,{error:o}=yield y(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in e){const{phone:s,type:r,options:i}=e,{data:o,error:a}=yield y(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new ge("You must provide either an email or phone number and a type")}catch(t){if(w(t))return{data:{user:null,session:null},error:t};throw t}})}getSession(){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return this._useSession(t=>u(this,null,function*(){return t}))}))})}_acquireLock(e,t){return u(this,null,function*(){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const s=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=u(this,null,function*(){return yield s,yield t()});return this.pendingInLock.push(u(this,null,function*(){try{yield r}catch(i){}})),r}return yield this.lock(`lock:${this.storageKey}`,e,()=>u(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const s=t();for(this.pendingInLock.push(u(this,null,function*(){try{yield s}catch(r){}})),yield s;this.pendingInLock.length;){const r=[...this.pendingInLock];yield Promise.all(r),this.pendingInLock.splice(0,r.length)}return yield s}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(e){return u(this,null,function*(){this._debug("#_useSession","begin");try{const t=yield this.__loadSession();return yield e(t)}finally{this._debug("#_useSession","end")}})}__loadSession(){return u(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null;const t=yield _e(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),t!==null&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!e)return{data:{session:null},error:null};const s=e.expires_at?e.expires_at*1e3-Date.now()<Se:!1;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let o=this.suppressGetSessionWarning;e=new Proxy(e,{get:(l,c,h)=>(!o&&c==="user"&&(o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,c,h))})}return{data:{session:e},error:null}}const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(e){return u(this,null,function*(){return e?yield this._getUser(e):(yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._getUser()})))})}_getUser(e){return u(this,null,function*(){try{return e?yield y(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:q}):yield this._useSession(t=>u(this,null,function*(){var s,r,i;const{data:o,error:a}=t;if(a)throw a;return!(!((s=o.session)===null||s===void 0)&&s.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new L}:yield y(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(r=o.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0,xform:q})}))}catch(t){if(w(t))return Ws(t)&&(yield this._removeSession(),yield ve(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}})}updateUser(s){return u(this,arguments,function*(e,t={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._updateUser(e,t)}))})}_updateUser(s){return u(this,arguments,function*(e,t={}){try{return yield this._useSession(r=>u(this,null,function*(){const{data:i,error:o}=r;if(o)throw o;if(!i.session)throw new L;const a=i.session;let l=null,c=null;this.flowType==="pkce"&&e.email!=null&&([l,c]=yield Q(this.storage,this.storageKey));const{data:h,error:d}=yield y(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:t==null?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:l,code_challenge_method:c}),jwt:a.access_token,xform:q});if(d)throw d;return a.user=h.user,yield this._saveSession(a),yield this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}}))}catch(r){if(w(r))return{data:{user:null},error:r};throw r}})}setSession(e){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._setSession(e)}))})}_setSession(e){return u(this,null,function*(){try{if(!e.access_token||!e.refresh_token)throw new L;const t=Date.now()/1e3;let s=t,r=!0,i=null;const{payload:o}=Ee(e.access_token);if(o.exp&&(s=o.exp,r=s<=t),r){const{session:a,error:l}=yield this._callRefreshToken(e.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};i=a}else{const{data:a,error:l}=yield this._getUser(e.access_token);if(l)throw l;i={access_token:e.access_token,refresh_token:e.refresh_token,user:a.user,token_type:"bearer",expires_in:s-t,expires_at:s},yield this._saveSession(i),yield this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(t){if(w(t))return{data:{session:null,user:null},error:t};throw t}})}refreshSession(e){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._refreshSession(e)}))})}_refreshSession(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;if(!e){const{data:o,error:a}=t;if(a)throw a;e=(s=o.session)!==null&&s!==void 0?s:void 0}if(!(e!=null&&e.refresh_token))throw new L;const{session:r,error:i}=yield this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(w(t))return{data:{user:null,session:null},error:t};throw t}})}_getSessionFromURL(e,t){return u(this,null,function*(){try{if(!C())throw new pe("No browser detected.");if(e.error||e.error_description||e.error_code)throw new pe(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if(this.flowType==="pkce")throw new st("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new pe("Not a valid implicit grant flow url.");break;default:}if(t==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new st("No code detected.");const{data:P,error:v}=yield this._exchangeCodeForSession(e.code);if(v)throw v;const b=new URL(window.location.href);return b.searchParams.delete("code"),window.history.replaceState(window.history.state,"",b.toString()),{data:{session:P.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:i,refresh_token:o,expires_in:a,expires_at:l,token_type:c}=e;if(!i||!a||!o||!c)throw new pe("No session defined in URL");const h=Math.round(Date.now()/1e3),d=parseInt(a);let f=h+d;l&&(f=parseInt(l)),(f-h)*1e3<=Z;const p=f-d;h-p>=120||h-p<0;const{data:m,error:_}=yield this._getUser(i);if(_)throw _;const T={provider_token:s,provider_refresh_token:r,access_token:i,expires_in:d,expires_at:f,refresh_token:o,token_type:c,user:m.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:T,redirectType:e.type},error:null}}catch(s){if(w(s))return{data:{session:null,redirectType:null},error:s};throw s}})}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}_isPKCECallback(e){return u(this,null,function*(){const t=yield _e(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)})}signOut(){return u(this,arguments,function*(e={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._signOut(e)}))})}_signOut(){return u(this,arguments,function*({scope:e}={scope:"global"}){return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:i}=t;if(i)return{error:i};const o=(s=r.session)===null||s===void 0?void 0:s.access_token;if(o){const{error:a}=yield this.admin.signOut(o,e);if(a&&!(Fs(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return e!=="others"&&(yield this._removeSession(),yield ve(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(e){const t=Zs(),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),u(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){this._emitInitialSession(t)}))}),{data:{subscription:s}}}_emitInitialSession(e){return u(this,null,function*(){return yield this._useSession(t=>u(this,null,function*(){var s,r;try{const{data:{session:i},error:o}=t;if(o)throw o;yield(s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",i),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(i){yield(r=this.stateChangeEmitters.get(e))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",e,"error",i)}}))})}resetPasswordForEmail(s){return u(this,arguments,function*(e,t={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=yield Q(this.storage,this.storageKey,!0));try{return yield y(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(o){if(w(o))return{data:null,error:o};throw o}})}getUserIdentities(){return u(this,null,function*(){var e;try{const{data:t,error:s}=yield this.getUser();if(s)throw s;return{data:{identities:(e=t.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(t){if(w(t))return{data:null,error:t};throw t}})}linkIdentity(e){return u(this,null,function*(){var t;try{const{data:s,error:r}=yield this._useSession(i=>u(this,null,function*(){var o,a,l,c,h;const{data:d,error:f}=i;if(f)throw f;const g=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(o=e.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=e.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=e.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return yield y(this.fetch,"GET",g,{headers:this.headers,jwt:(h=(c=d.session)===null||c===void 0?void 0:c.access_token)!==null&&h!==void 0?h:void 0})}));if(r)throw r;return C()&&!(!((t=e.options)===null||t===void 0)&&t.skipBrowserRedirect)&&window.location.assign(s==null?void 0:s.url),{data:{provider:e.provider,url:s==null?void 0:s.url},error:null}}catch(s){if(w(s))return{data:{provider:e.provider,url:null},error:s};throw s}})}unlinkIdentity(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s,r;const{data:i,error:o}=t;if(o)throw o;return yield y(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(r=(s=i.session)===null||s===void 0?void 0:s.access_token)!==null&&r!==void 0?r:void 0})}))}catch(t){if(w(t))return{data:null,error:t};throw t}})}_refreshAccessToken(e){return u(this,null,function*(){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const s=Date.now();return yield rr(r=>u(this,null,function*(){return r>0&&(yield sr(200*Math.pow(2,r-1))),this._debug(t,"refreshing attempt",r),yield y(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:I})}),(r,i)=>{const o=200*Math.pow(2,r);return i&&Te(i)&&Date.now()+o-s<Z})}catch(s){if(this._debug(t,"error",s),w(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(t,"end")}})}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(e,t){return u(this,null,function*(){const s=yield this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),C()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}})}_recoverAndRefresh(){return u(this,null,function*(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=yield _e(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),s!==null&&(yield this._removeSession());return}const r=((e=s.expires_at)!==null&&e!==void 0?e:1/0)*1e3-Date.now()<Se;if(this._debug(t,`session has${r?"":" not"} expired with margin of ${Se}s`),r){if(this.autoRefreshToken&&s.refresh_token){const{error:i}=yield this._callRefreshToken(s.refresh_token);i&&(Te(i)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",i),yield this._removeSession()))}}else yield this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){this._debug(t,"error",s);return}finally{this._debug(t,"end")}})}_callRefreshToken(e){return u(this,null,function*(){var t,s;if(!e)throw new L;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new me;const{data:i,error:o}=yield this._refreshAccessToken(e);if(o)throw o;if(!i.session)throw new L;yield this._saveSession(i.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const a={session:i.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(i){if(this._debug(r,"error",i),w(i)){const o={session:null,error:i};return Te(i)||(yield this._removeSession()),(t=this.refreshingDeferred)===null||t===void 0||t.resolve(o),o}throw(s=this.refreshingDeferred)===null||s===void 0||s.reject(i),i}finally{this.refreshingDeferred=null,this._debug(r,"end")}})}_notifyAllSubscribers(e,t,s=!0){return u(this,null,function*(){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(a=>u(this,null,function*(){try{yield a.callback(e,t)}catch(l){i.push(l)}}));if(yield Promise.all(o),i.length>0){for(let a=0;a<i.length;a+=1);throw i[0]}}finally{this._debug(r,"end")}})}_saveSession(e){return u(this,null,function*(){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,yield Pt(this.storage,this.storageKey,e)})}_removeSession(){return u(this,null,function*(){this._debug("#_removeSession()"),yield ve(this.storage,this.storageKey),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&C()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){}}_startAutoRefresh(){return u(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),Z);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno!="undefined"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(()=>u(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return u(this,null,function*(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return u(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return u(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return u(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>u(this,null,function*(){try{const e=Date.now();try{return yield this._useSession(t=>u(this,null,function*(){const{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const r=Math.floor((s.expires_at*1e3-e)/Z);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts ${Z}ms, refresh threshold is ${xe} ticks`),r<=xe&&(yield this._callRefreshToken(s.refresh_token))}))}catch(t){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(e){if(e.isAcquireTimeout||e instanceof At)this._debug("auto refresh token tick lock not available");else throw e}})}_handleVisibilityChange(){return u(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!C()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>u(this,null,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(e){}})}_onVisibilityChanged(e){return u(this,null,function*(){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){if(document.visibilityState!=="visible"){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}yield this._recoverAndRefresh()})))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(e,t,s){return u(this,null,function*(){const r=[`provider=${encodeURIComponent(t)}`];if(s!=null&&s.redirectTo&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),s!=null&&s.scopes&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),this.flowType==="pkce"){const[i,o]=yield Q(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});r.push(a.toString())}if(s!=null&&s.queryParams){const i=new URLSearchParams(s.queryParams);r.push(i.toString())}return s!=null&&s.skipBrowserRedirect&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`})}_unenroll(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:i}=t;return i?{data:null,error:i}:yield y(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})}))}catch(t){if(w(t))return{data:null,error:t};throw t}})}_enroll(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s,r;const{data:i,error:o}=t;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},e.factorType==="phone"?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:c}=yield y(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(s=i==null?void 0:i.session)===null||s===void 0?void 0:s.access_token});return c?{data:null,error:c}:(e.factorType==="totp"&&(!((r=l==null?void 0:l.totp)===null||r===void 0)&&r.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})}))}catch(t){if(w(t))return{data:null,error:t};throw t}})}_verify(e){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:i}=t;if(i)return{data:null,error:i};const{data:o,error:a}=yield y(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token});return a?{data:null,error:a}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})}))}catch(t){if(w(t))return{data:null,error:t};throw t}}))})}_challenge(e){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:i}=t;return i?{data:null,error:i}:yield y(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})}))}catch(t){if(w(t))return{data:null,error:t};throw t}}))})}_challengeAndVerify(e){return u(this,null,function*(){const{data:t,error:s}=yield this._challenge({factorId:e.factorId});return s?{data:null,error:s}:yield this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})})}_listFactors(){return u(this,null,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const s=(e==null?void 0:e.factors)||[],r=s.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=s.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:s,totp:r,phone:i},error:null}})}_getAuthenticatorAssuranceLevel(){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){return yield this._useSession(e=>u(this,null,function*(){var t,s;const{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Ee(r.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((s=(t=r.user.factors)===null||t===void 0?void 0:t.filter(d=>d.status==="verified"))!==null&&s!==void 0?s:[]).length>0&&(l="aal2");const h=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:h},error:null}}))}))})}fetchJwk(s){return u(this,arguments,function*(e,t={keys:[]}){let r=t.keys.find(a=>a.kid===e);if(r||(r=this.jwks.keys.find(a=>a.kid===e),r&&this.jwks_cached_at+Ns>Date.now()))return r;const{data:i,error:o}=yield y(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;if(!i.keys||i.keys.length===0)throw new ae("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(a=>a.kid===e),!r)throw new ae("No matching signing key found in JWKS");return r})}getClaims(s){return u(this,arguments,function*(e,t={keys:[]}){try{let r=e;if(!r){const{data:p,error:m}=yield this.getSession();if(m||!p.session)return{data:null,error:m};r=p.session.access_token}const{header:i,payload:o,signature:a,raw:{header:l,payload:c}}=Ee(r);if(hr(o.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:p}=yield this.getUser(r);if(p)throw p;return{data:{claims:o,header:i,signature:a},error:null}}const h=ur(i.alg),d=yield this.fetchJwk(i.kid,t),f=yield crypto.subtle.importKey("jwk",d,h,!0,["verify"]);if(!(yield crypto.subtle.verify(h,f,a,Qs(`${l}.${c}`))))throw new ae("Invalid JWT signature");return{data:{claims:o,header:i,signature:a},error:null}}catch(r){if(w(r))return{data:null,error:r};throw r}})}}ce.nextInstanceID=0;const Pr=ce;class Ar extends Pr{constructor(e){super(e)}}var $r=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(o){o(i)})}return new(t||(t=Promise))(function(i,o){function a(h){try{c(s.next(h))}catch(d){o(d)}}function l(h){try{c(s.throw(h))}catch(d){o(d)}}function c(h){h.done?i(h.value):r(h.value).then(a,l)}c((s=s.apply(n,[])).next())})};class Rr{constructor(e,t,s){var r,i,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=Is(e),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,h={db:js,realtime:Ps,auth:Object.assign(Object.assign({},Os),{storageKey:c}),global:Es},d=Us(s!=null?s:{},h);this.storageKey=(r=d.auth.storageKey)!==null&&r!==void 0?r:"",this.headers=(i=d.global.headers)!==null&&i!==void 0?i:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(f,g)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(g)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=d.auth)!==null&&o!==void 0?o:{},this.headers,d.global.fetch),this.fetch=Cs(t,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new Vt(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new Dt(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new ks(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return $r(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return(t=(e=s.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:i,flowType:o,lock:a,debug:l},c,h){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Ar({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),c),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:o,lock:a,debug:l,fetch:h,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new ls(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,s)=>{this._handleTokenChanged(t,"CLIENT",s==null?void 0:s.access_token)})}_handleTokenChanged(e,t,s){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==s?this.changedAccessToken=s:e==="SIGNED_OUT"&&(this.realtime.setAuth(),t=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Nr=(n,e,t)=>new Rr(n,e,t);export{Nr as c};
