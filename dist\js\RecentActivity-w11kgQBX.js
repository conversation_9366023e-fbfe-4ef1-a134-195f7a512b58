var k=(r,d,i)=>new Promise((p,l)=>{var h=s=>{try{n(i.next(s))}catch(a){l(a)}},f=s=>{try{n(i.throw(s))}catch(a){l(a)}},n=s=>s.done?p(s.value):Promise.resolve(s.value).then(h,f);n((i=i.apply(r,d)).next())});import{r as x,j as t,m as j,c as A,w as E,b as R,e as S}from"./chunk-DX4Z_LyS.js";import{U,s as C}from"../assets/main-CGUKzV0x.js";import{j as $}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const B=()=>{const{currentUser:r}=x.useContext(U),[d,i]=x.useState([]),[p,l]=x.useState(!0);x.useEffect(()=>{k(null,null,function*(){var a,g,w,b,N,y,v,_;if(r)try{let c=[];try{const{data:e}=yield C.from("contributions").select(`
              id,
              title,
              status,
              created_at,
              projects (name, id)
            `).eq("user_id",r.id).order("created_at",{ascending:!1}).limit(5);c=e||[]}catch(e){(a=e.message)!=null&&a.includes("401")||(g=e.message)!=null&&g.includes("permission")||(w=e.message)!=null&&w.includes("relation")||(b=e.message)!=null&&b.includes("does not exist")}let o=[];try{const{data:e}=yield C.from("projects").select("id, name, updated_at, status").eq("created_by",r.id).order("updated_at",{ascending:!1}).limit(3);o=e||[]}catch(e){(N=e.message)!=null&&N.includes("401")||(y=e.message)!=null&&y.includes("permission")||(v=e.message)!=null&&v.includes("relation")||(_=e.message)!=null&&_.includes("does not exist")}const m=[];c==null||c.forEach(e=>{var u;m.push({id:`contribution-${e.id}`,type:"contribution",title:e.title,subtitle:`in ${((u=e.projects)==null?void 0:u.name)||"Unknown Project"}`,status:e.status,timestamp:e.created_at,icon:h(e.status),color:f(e.status)})}),o==null||o.forEach(e=>{m.push({id:`project-${e.id}`,type:"project",title:`Project: ${e.name}`,subtitle:"Updated",status:e.status,timestamp:e.updated_at,icon:"📁",color:"from-blue-500 to-cyan-500"})}),m.sort((e,u)=>new Date(u.timestamp)-new Date(e.timestamp)),i(m.slice(0,8))}catch(c){}finally{l(!1)}})},[r]);const h=s=>{switch(s){case"approved":return"✅";case"pending":return"⏳";case"rejected":return"❌";default:return"📝"}},f=s=>{switch(s){case"approved":return"from-green-500 to-emerald-500";case"pending":return"from-yellow-500 to-orange-500";case"rejected":return"from-red-500 to-pink-500";default:return"from-gray-500 to-slate-500"}},n=s=>{switch(s){case"approved":return"success";case"pending":return"warning";case"rejected":return"danger";default:return"default"}};return p?t.jsx("div",{className:"flex items-center justify-center h-full",children:t.jsx(j.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"})}):t.jsx("div",{className:"p-6",children:t.jsx(j.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:t.jsxs(A,{className:"bg-white/10 backdrop-blur-md border-white/20",children:[t.jsx(E,{children:t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"w-10 h-10 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center",children:t.jsx("span",{className:"text-xl",children:"📈"})}),t.jsxs("div",{children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"Recent Activity"}),t.jsx("p",{className:"text-white/60 text-sm",children:"Your latest project updates and contributions"})]})]})}),t.jsx(R,{children:d.length===0?t.jsxs("div",{className:"text-center py-8",children:[t.jsx("span",{className:"text-6xl mb-4 block",children:"🌟"}),t.jsx("h3",{className:"text-white text-lg font-medium mb-2",children:"No recent activity"}),t.jsx("p",{className:"text-white/60",children:"Start contributing to projects to see your activity here!"})]}):t.jsx("div",{className:"space-y-4",children:d.map((s,a)=>t.jsxs(j.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*a},className:"flex items-center gap-4 p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-200",children:[t.jsx("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${s.color} flex items-center justify-center flex-shrink-0`,children:t.jsx("span",{className:"text-xl",children:s.icon})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h4",{className:"text-white font-medium truncate",children:s.title}),t.jsx("p",{className:"text-white/60 text-sm truncate",children:s.subtitle}),t.jsx("p",{className:"text-white/40 text-xs mt-1",children:$(new Date(s.timestamp),{addSuffix:!0})})]}),t.jsx("div",{className:"flex-shrink-0",children:t.jsx(S,{color:n(s.status),variant:"flat",size:"sm",children:s.status})})]},s.id))})})]})})})};export{B as default};
