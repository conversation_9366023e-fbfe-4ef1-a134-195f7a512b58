var H=Object.defineProperty,J=Object.defineProperties;var Q=Object.getOwnPropertyDescriptors;var A=Object.getOwnPropertySymbols;var X=Object.prototype.hasOwnProperty,Y=Object.prototype.propertyIsEnumerable;var W=(i,n,l)=>n in i?H(i,n,{enumerable:!0,configurable:!0,writable:!0,value:l}):i[n]=l,x=(i,n)=>{for(var l in n||(n={}))X.call(n,l)&&W(i,l,n[l]);if(A)for(var l of A(n))Y.call(n,l)&&W(i,l,n[l]);return i},u=(i,n)=>J(i,Q(n));var w=(i,n,l)=>new Promise((_,r)=>{var N=o=>{try{m(l.next(o))}catch(f){r(f)}},t=o=>{try{m(l.throw(o))}catch(f){r(f)}},m=o=>o.done?_(o.value):Promise.resolve(o.value).then(N,t);m((l=l.apply(i,n)).next())});import{Z as ee,r as p,j as e,c as S,b as C,e as v,G as F,h as k,C as se,l as ae,n as re,o as te,p as ie,y as le,s as ne,u as ce,v as y,F as E,E as oe}from"./chunk-DX4Z_LyS.js";import{j as de,s as g}from"../assets/main-CGUKzV0x.js";import{a as me}from"./chunk-CqI-o4WX.js";import{n as he,E as xe}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";const Ne=()=>{var q,z,I;const{currentUser:i}=de(),{isOpen:n,onOpen:l,onClose:_}=ee(),[r,N]=p.useState(null),[t,m]=p.useState({decision:"",overall_score:"",strengths:"",weaknesses:"",recommendations:"",criteria_scores:{}}),[o,f]=p.useState([]),[R,$]=p.useState([]),[B,L]=p.useState(!0),[M,D]=p.useState(!1);p.useEffect(()=>{i&&(O(),G())},[i]);const O=()=>w(null,null,function*(){try{const{data:s,error:a}=yield g.from("reviewer_assignments").select(`
          *,
          application:application_id(
            *,
            user:user_id(full_name, avatar_url, email),
            reviews:vetting_reviews(*)
          )
        `).eq("reviewer_id",i.id).is("completed_at",null).order("assigned_at",{ascending:!0});if(a)throw a;f((s==null?void 0:s.map(c=>c.application))||[])}catch(s){}finally{L(!1)}}),G=()=>w(null,null,function*(){try{const{data:s,error:a}=yield g.from("vetting_criteria").select("*").eq("is_active",!0).order("weight",{ascending:!1});if(a)throw a;$(s||[])}catch(s){}}),K=s=>{N(s),m({decision:"",overall_score:"",strengths:"",weaknesses:"",recommendations:"",criteria_scores:{}}),l()},T=(s,a)=>{m(c=>u(x({},c),{criteria_scores:u(x({},c.criteria_scores),{[s]:parseFloat(a)})}))},b=()=>{const s=R.filter(d=>d.application_type===(r==null?void 0:r.application_type));if(s.length===0)return 0;let a=0,c=0;return s.forEach(d=>{const h=t.criteria_scores[d.id];h!==void 0&&(a+=h*d.weight,c+=d.weight)}),c>0?(a/c).toFixed(2):0},U=()=>w(null,null,function*(){if(!(!r||!t.decision)){D(!0);try{const s=b(),{data:a,error:c}=yield g.from("vetting_reviews").insert({application_id:r.id,reviewer_id:i.id,decision:t.decision,overall_score:s,criteria_scores:t.criteria_scores,strengths:t.strengths,weaknesses:t.weaknesses,recommendations:t.recommendations,review_completed_at:new Date().toISOString(),is_final_review:!0}).select().single();if(c)throw c;const{error:d}=yield g.from("reviewer_assignments").update({completed_at:new Date().toISOString()}).eq("application_id",r.id).eq("reviewer_id",i.id);if(d)throw d;let h="under_review";t.decision==="approve"?h="approved":t.decision==="reject"&&(h="rejected");const{error:j}=yield g.from("vetting_applications").update(x({status:h,reviewed_at:new Date().toISOString()},h==="approved"&&{approved_at:new Date().toISOString()})).eq("id",r.id);if(j)throw j;const{error:P}=yield g.from("vetting_workflow_logs").insert({application_id:r.id,user_id:i.id,action:"review_completed",from_status:r.status,to_status:h,details:{review_id:a.id,decision:t.decision}});if(P)throw P;yield O(),_()}catch(s){}finally{D(!1)}}}),V=s=>({pending:"warning",under_review:"primary",approved:"success",rejected:"danger"})[s]||"default",Z=s=>({developer:"💻",designer:"🎨",project_manager:"📋",qa_tester:"🔍",business_analyst:"📊"})[s]||"👤";return B?e.jsx("div",{className:"space-y-4",children:[...Array(3)].map((s,a)=>e.jsx(S,{className:"animate-pulse",children:e.jsx(C,{className:"p-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})]})})},a))}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Pending Reviews"}),e.jsx("p",{className:"text-gray-600",children:"Applications waiting for your review"})]}),e.jsxs(v,{color:"primary",variant:"flat",children:[o.length," Pending"]})]}),o.length===0?e.jsx(S,{children:e.jsxs(C,{className:"text-center py-12",children:[e.jsx(he,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"All caught up!"}),e.jsx("p",{className:"text-gray-600",children:"No applications pending your review at the moment."})]})}):e.jsx("div",{className:"space-y-4",children:o.map(s=>{var a,c,d;return e.jsx(S,{className:"hover:shadow-md transition-shadow",children:e.jsx(C,{className:"p-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start gap-4 flex-1",children:[e.jsx(F,{src:(a=s.user)==null?void 0:a.avatar_url,name:(c=s.user)==null?void 0:c.full_name,size:"lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("h3",{className:"text-lg font-semibold",children:(d=s.user)==null?void 0:d.full_name}),e.jsx("span",{className:"text-2xl",children:Z(s.application_type)}),e.jsx(v,{size:"sm",variant:"flat",children:s.application_type.replace("_"," ")})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Experience"}),e.jsxs("p",{className:"font-medium",children:[s.years_experience," years"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Rate"}),e.jsxs("p",{className:"font-medium",children:["$",s.hourly_rate,"/hour"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Submitted"}),e.jsx("p",{className:"font-medium",children:me(s.submitted_at)})]})]}),s.primary_skills&&s.primary_skills.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Primary Skills"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[s.primary_skills.slice(0,5).map((h,j)=>e.jsx(v,{size:"sm",variant:"flat",color:"primary",children:h},j)),s.primary_skills.length>5&&e.jsxs(v,{size:"sm",variant:"flat",children:["+",s.primary_skills.length-5," more"]})]})]}),s.motivation_statement&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"Motivation"}),e.jsx("p",{className:"text-sm line-clamp-2",children:s.motivation_statement})]})]})]}),e.jsxs("div",{className:"flex flex-col items-end gap-2",children:[e.jsx(v,{color:V(s.status),variant:"flat",children:s.status.replace("_"," ")}),e.jsx(k,{color:"primary",variant:"flat",startContent:e.jsx(xe,{className:"w-4 h-4"}),onClick:()=>K(s),children:"Review"})]})]})})},s.id)})}),e.jsx(se,{isOpen:n,onClose:_,size:"4xl",scrollBehavior:"inside",children:e.jsxs(ae,{children:[e.jsx(re,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(F,{src:(q=r==null?void 0:r.user)==null?void 0:q.avatar_url,name:(z=r==null?void 0:r.user)==null?void 0:z.full_name,size:"md"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold",children:["Review Application - ",(I=r==null?void 0:r.user)==null?void 0:I.full_name]}),e.jsx("p",{className:"text-sm text-gray-600",children:r==null?void 0:r.application_type.replace("_"," ")})]})]})}),e.jsx(te,{children:r&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Evaluation Criteria"}),e.jsx("div",{className:"space-y-4",children:R.filter(s=>s.application_type===r.application_type).map(s=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium",children:s.criterion_name}),e.jsx("p",{className:"text-sm text-gray-600",children:s.criterion_description})]}),e.jsxs(v,{size:"sm",variant:"flat",children:["Weight: ",s.weight,"x"]})]}),e.jsx(ie,{type:"number",placeholder:`Score (${s.min_score}-${s.max_score})`,min:s.min_score,max:s.max_score,step:"0.1",value:t.criteria_scores[s.id]||"",onChange:a=>T(s.id,a.target.value)})]},s.id))}),Object.keys(t.criteria_scores).length>0&&e.jsxs("div",{className:"mt-4 p-4 bg-blue-50 rounded-lg",children:[e.jsxs("p",{className:"font-medium",children:["Overall Score: ",b(),"/10"]}),e.jsx(le,{value:b()*10,className:"mt-2",color:"primary"})]})]}),e.jsx(ne,{}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Review Decision"}),e.jsxs(ce,{placeholder:"Select decision",selectedKeys:t.decision?[t.decision]:[],onSelectionChange:s=>m(a=>u(x({},a),{decision:Array.from(s)[0]})),children:[e.jsx(y,{children:"Approve"},"approve"),e.jsx(y,{children:"Reject"},"reject"),e.jsx(y,{children:"Request More Information"},"request_more_info"),e.jsx(y,{children:"Escalate to Senior Reviewer"},"escalate")]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(E,{label:"Strengths",placeholder:"What are the applicant's key strengths?",value:t.strengths,onChange:s=>m(a=>u(x({},a),{strengths:s.target.value}))}),e.jsx(E,{label:"Areas for Improvement",placeholder:"What areas could the applicant improve?",value:t.weaknesses,onChange:s=>m(a=>u(x({},a),{weaknesses:s.target.value}))}),e.jsx(E,{label:"Recommendations",placeholder:"Any recommendations for the applicant or next steps?",value:t.recommendations,onChange:s=>m(a=>u(x({},a),{recommendations:s.target.value}))})]})]})}),e.jsxs(oe,{children:[e.jsx(k,{variant:"light",onPress:_,children:"Cancel"}),e.jsx(k,{color:"primary",onPress:U,isLoading:M,isDisabled:!t.decision,children:"Submit Review"})]})]})})]})};export{Ne as default};
