var js=Object.defineProperty,vs=Object.defineProperties;var gs=Object.getOwnPropertyDescriptors;var ss=Object.getOwnPropertySymbols;var ys=Object.prototype.hasOwnProperty,bs=Object.prototype.propertyIsEnumerable;var ts=(s,t,i)=>t in s?js(s,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[t]=i,Y=(s,t)=>{for(var i in t||(t={}))ys.call(t,i)&&ts(s,i,t[i]);if(ss)for(var i of ss(t))bs.call(t,i)&&ts(s,i,t[i]);return s},J=(s,t)=>vs(s,gs(t));var ee=(s,t,i)=>new Promise((a,r)=>{var l=o=>{try{p(i.next(o))}catch(c){r(c)}},n=o=>{try{p(i.throw(o))}catch(c){r(c)}},p=o=>o.done?a(o.value):Promise.resolve(o.value).then(l,n);p((i=i.apply(s,t)).next())});import{a as G,d as Ns,r as S,j as e,c as g,w as D,e as E,b as w,m as M,h as $,S as Fe,T as V,y as B,C as te,l as ae,n as ie,o as re,p as Re,u as W,v,E as ne,q as cs,G as ws,t as as}from"./chunk-DX4Z_LyS.js";import{U as be,s as Ae}from"../assets/main-CGUKzV0x.js";import{c as U}from"./chunk-D8IZ3rty.js";import{S as is,a as Ps,f as Te,L as je,b as ks,A as Ss,i as Ne,c as As,d as os,E as Cs,g as we,e as Ds,h as Ms,C as Ts,j as se,k as Rs,u as zs,G as Es,l as Os,m as rs,n as _s,o as le,p as ze,B as Ee,X as Pe,Y as ye,q as $s,R as ds,r as Is,s as Oe,T as _e,t as $e,v as Ls}from"./chunk-Crqd8jVX.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";function ce(s){"@babel/helpers - typeof";return ce=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ce(s)}function Fs(s,t){if(!(s instanceof t))throw new TypeError("Cannot call a class as a function")}function Gs(s,t){for(var i=0;i<t.length;i++){var a=t[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(s,hs(a.key),a)}}function Bs(s,t,i){return t&&Gs(s.prototype,t),Object.defineProperty(s,"prototype",{writable:!1}),s}function Ks(s,t,i){return t=ke(t),Ws(s,ms()?Reflect.construct(t,i||[],ke(s).constructor):t.apply(s,i))}function Ws(s,t){if(t&&(ce(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Us(s)}function Us(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function ms(){try{var s=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ms=function(){return!!s})()}function ke(s){return ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(i){return i.__proto__||Object.getPrototypeOf(i)},ke(s)}function Qs(s,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&Ie(s,t)}function Ie(s,t){return Ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,r){return a.__proto__=r,a},Ie(s,t)}function xs(s,t,i){return t=hs(t),t in s?Object.defineProperty(s,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):s[t]=i,s}function hs(s){var t=Vs(s,"string");return ce(t)=="symbol"?t:t+""}function Vs(s,t){if(ce(s)!="object"||!s)return s;var i=s[Symbol.toPrimitive];if(i!==void 0){var a=i.call(s,t);if(ce(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(s)}var Ce=function(s){function t(){return Fs(this,t),Ks(this,t,arguments)}return Qs(t,s),Bs(t,[{key:"render",value:function(){return null}}])}(G.Component);xs(Ce,"displayName","ZAxis");xs(Ce,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var qs=["option","isActive"];function ve(){return ve=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(s[a]=i[a])}return s},ve.apply(this,arguments)}function Hs(s,t){if(s==null)return{};var i=Ys(s,t),a,r;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(s);for(r=0;r<l.length;r++)a=l[r],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(s,a)&&(i[a]=s[a])}return i}function Ys(s,t){if(s==null)return{};var i={};for(var a in s)if(Object.prototype.hasOwnProperty.call(s,a)){if(t.indexOf(a)>=0)continue;i[a]=s[a]}return i}function Js(s){var t=s.option,i=s.isActive,a=Hs(s,qs);return typeof t=="string"?G.createElement(is,ve({option:G.createElement(Ps,ve({type:t},a)),isActive:i,shapeType:"symbols"},a)):G.createElement(is,ve({option:t,isActive:i,shapeType:"symbols"},a))}function oe(s){"@babel/helpers - typeof";return oe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oe(s)}function ge(){return ge=Object.assign?Object.assign.bind():function(s){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(s[a]=i[a])}return s},ge.apply(this,arguments)}function ns(s,t){var i=Object.keys(s);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(s);t&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(s,r).enumerable})),i.push.apply(i,a)}return i}function H(s){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?ns(Object(i),!0).forEach(function(a){Z(s,a,i[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(i)):ns(Object(i)).forEach(function(a){Object.defineProperty(s,a,Object.getOwnPropertyDescriptor(i,a))})}return s}function Zs(s,t){if(!(s instanceof t))throw new TypeError("Cannot call a class as a function")}function ls(s,t){for(var i=0;i<t.length;i++){var a=t[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(s,us(a.key),a)}}function Xs(s,t,i){return t&&ls(s.prototype,t),i&&ls(s,i),Object.defineProperty(s,"prototype",{writable:!1}),s}function et(s,t,i){return t=Se(t),st(s,ps()?Reflect.construct(t,i||[],Se(s).constructor):t.apply(s,i))}function st(s,t){if(t&&(oe(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tt(s)}function tt(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function ps(){try{var s=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ps=function(){return!!s})()}function Se(s){return Se=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(i){return i.__proto__||Object.getPrototypeOf(i)},Se(s)}function at(s,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&Le(s,t)}function Le(s,t){return Le=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,r){return a.__proto__=r,a},Le(s,t)}function Z(s,t,i){return t=us(t),t in s?Object.defineProperty(s,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):s[t]=i,s}function us(s){var t=it(s,"string");return oe(t)=="symbol"?t:t+""}function it(s,t){if(oe(s)!="object"||!s)return s;var i=s[Symbol.toPrimitive];if(i!==void 0){var a=i.call(s,t);if(oe(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(s)}var De=function(s){function t(){var i;Zs(this,t);for(var a=arguments.length,r=new Array(a),l=0;l<a;l++)r[l]=arguments[l];return i=et(this,t,[].concat(r)),Z(i,"state",{isAnimationFinished:!1}),Z(i,"handleAnimationEnd",function(){i.setState({isAnimationFinished:!0})}),Z(i,"handleAnimationStart",function(){i.setState({isAnimationFinished:!1})}),Z(i,"id",zs("recharts-scatter-")),i}return at(t,s),Xs(t,[{key:"renderSymbolsStatically",value:function(a){var r=this,l=this.props,n=l.shape,p=l.activeShape,o=l.activeIndex,c=Te(this.props,!1);return a.map(function(x,d){var f=o===d,b=f?p:n,j=H(H({},c),x);return G.createElement(je,ge({className:"recharts-scatter-symbol",key:"symbol-".concat(x==null?void 0:x.cx,"-").concat(x==null?void 0:x.cy,"-").concat(x==null?void 0:x.size,"-").concat(d)},ks(r.props,x,d),{role:"img"}),G.createElement(Js,ge({option:b,isActive:f,key:"symbol-".concat(d)},j)))})}},{key:"renderSymbolsWithAnimation",value:function(){var a=this,r=this.props,l=r.points,n=r.isAnimationActive,p=r.animationBegin,o=r.animationDuration,c=r.animationEasing,x=r.animationId,d=this.state.prevPoints;return G.createElement(Ss,{begin:p,duration:o,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(x),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(f){var b=f.t,j=l.map(function(y,T){var h=d&&d[T];if(h){var k=Ne(h.cx,y.cx),A=Ne(h.cy,y.cy),L=Ne(h.size,y.size);return H(H({},y),{},{cx:k(b),cy:A(b),size:L(b)})}var C=Ne(0,y.size);return H(H({},y),{},{size:C(b)})});return G.createElement(je,null,a.renderSymbolsStatically(j))})}},{key:"renderSymbols",value:function(){var a=this.props,r=a.points,l=a.isAnimationActive,n=this.state.prevPoints;return l&&r&&r.length&&(!n||!As(n,r))?this.renderSymbolsWithAnimation():this.renderSymbolsStatically(r)}},{key:"renderErrorBar",value:function(){var a=this.props.isAnimationActive;if(a&&!this.state.isAnimationFinished)return null;var r=this.props,l=r.points,n=r.xAxis,p=r.yAxis,o=r.children,c=os(o,Cs);return c?c.map(function(x,d){var f=x.props,b=f.direction,j=f.dataKey;return G.cloneElement(x,{key:"".concat(b,"-").concat(j,"-").concat(l[d]),data:l,xAxis:n,yAxis:p,layout:b==="x"?"vertical":"horizontal",dataPointFormatter:function(T,h){return{x:T.cx,y:T.cy,value:b==="x"?+T.node.x:+T.node.y,errorVal:we(T,h)}}})}):null}},{key:"renderLine",value:function(){var a=this.props,r=a.points,l=a.line,n=a.lineType,p=a.lineJointType,o=Te(this.props,!1),c=Te(l,!1),x,d;if(n==="joint")x=r.map(function(A){return{x:A.cx,y:A.cy}});else if(n==="fitting"){var f=Ds(r),b=f.xmin,j=f.xmax,y=f.a,T=f.b,h=function(L){return y*L+T};x=[{x:b,y:h(b)},{x:j,y:h(j)}]}var k=H(H(H({},o),{},{fill:"none",stroke:o&&o.fill},c),{},{points:x});return G.isValidElement(l)?d=G.cloneElement(l,k):Ms(l)?d=l(k):d=G.createElement(Ts,ge({},k,{type:p})),G.createElement(je,{className:"recharts-scatter-line",key:"recharts-scatter-line"},d)}},{key:"render",value:function(){var a=this.props,r=a.hide,l=a.points,n=a.line,p=a.className,o=a.xAxis,c=a.yAxis,x=a.left,d=a.top,f=a.width,b=a.height,j=a.id,y=a.isAnimationActive;if(r||!l||!l.length)return null;var T=this.state.isAnimationFinished,h=Ns("recharts-scatter",p),k=o&&o.allowDataOverflow,A=c&&c.allowDataOverflow,L=k||A,C=se(j)?this.id:j;return G.createElement(je,{className:h,clipPath:L?"url(#clipPath-".concat(C,")"):null},k||A?G.createElement("defs",null,G.createElement("clipPath",{id:"clipPath-".concat(C)},G.createElement("rect",{x:k?x:x-f/2,y:A?d:d-b/2,width:k?f:f*2,height:A?b:b*2}))):null,n&&this.renderLine(),this.renderErrorBar(),G.createElement(je,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!y||T)&&Rs.renderCallByParent(this.props,l))}}],[{key:"getDerivedStateFromProps",value:function(a,r){return a.animationId!==r.prevAnimationId?{prevAnimationId:a.animationId,curPoints:a.points,prevPoints:r.curPoints}:a.points!==r.curPoints?{curPoints:a.points}:null}}])}(S.PureComponent);Z(De,"displayName","Scatter");Z(De,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!Es.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"});Z(De,"getComposedData",function(s){var t=s.xAxis,i=s.yAxis,a=s.zAxis,r=s.item,l=s.displayedData,n=s.xAxisTicks,p=s.yAxisTicks,o=s.offset,c=r.props.tooltipType,x=os(r.props.children,Os),d=se(t.dataKey)?r.props.dataKey:t.dataKey,f=se(i.dataKey)?r.props.dataKey:i.dataKey,b=a&&a.dataKey,j=a?a.range:Ce.defaultProps.range,y=j&&j[0],T=t.scale.bandwidth?t.scale.bandwidth():0,h=i.scale.bandwidth?i.scale.bandwidth():0,k=l.map(function(A,L){var C=we(A,d),u=we(A,f),P=!se(b)&&we(A,b)||"-",I=[{name:se(t.dataKey)?r.props.name:t.name||t.dataKey,unit:t.unit||"",value:C,payload:A,dataKey:d,type:c},{name:se(i.dataKey)?r.props.name:i.name||i.dataKey,unit:i.unit||"",value:u,payload:A,dataKey:f,type:c}];P!=="-"&&I.push({name:a.name||a.dataKey,unit:a.unit||"",value:P,payload:A,dataKey:b,type:c});var F=rs({axis:t,ticks:n,bandSize:T,entry:A,index:L,dataKey:d}),_=rs({axis:i,ticks:p,bandSize:h,entry:A,index:L,dataKey:f}),R=P!=="-"?a.scale(P):y,Q=Math.sqrt(Math.max(R,0)/Math.PI);return H(H({},A),{},{cx:F,cy:_,x:F-Q,y:_-Q,xAxis:t,yAxis:i,zAxis:a,width:2*Q,height:2*Q,size:R,node:{x:C,y:u,z:P},tooltipPayload:I,tooltipPosition:{x:F,y:_},payload:A},x&&x[L]&&x[L].props)});return H({points:k},o)});var rt=_s({chartName:"ComposedChart",GraphicalChild:[le,ze,Ee,De],axisComponents:[{axisType:"xAxis",AxisComp:Pe},{axisType:"yAxis",AxisComp:ye},{axisType:"zAxis",AxisComp:Ce}],formatAxisMap:$s});const nt=({data:s=[],type:t="area",height:i=300,showLegend:a=!0,className:r="",title:l="Revenue Trends"})=>{const n=[{month:"Jan",revenue:2400,expenses:1200,profit:1200},{month:"Feb",revenue:3200,expenses:1400,profit:1800},{month:"Mar",revenue:2800,expenses:1300,profit:1500},{month:"Apr",revenue:4100,expenses:1600,profit:2500},{month:"May",revenue:3600,expenses:1500,profit:2100},{month:"Jun",revenue:4800,expenses:1800,profit:3e3},{month:"Jul",revenue:5200,expenses:1900,profit:3300},{month:"Aug",revenue:4600,expenses:1700,profit:2900},{month:"Sep",revenue:5800,expenses:2100,profit:3700},{month:"Oct",revenue:6200,expenses:2200,profit:4e3},{month:"Nov",revenue:5900,expenses:2e3,profit:3900},{month:"Dec",revenue:7100,expenses:2400,profit:4700}],p=s.length>0?s:n,o=({active:x,payload:d,label:f})=>x&&d&&d.length?e.jsxs("div",{className:"bg-white dark:bg-slate-800 p-3 border border-default-200 rounded-lg shadow-lg",children:[e.jsx("p",{className:"font-semibold text-default-900 dark:text-default-100",children:f}),d.map((b,j)=>e.jsxs("p",{style:{color:b.color},className:"text-sm",children:[b.name,": $",b.value.toLocaleString()]},j))]}):null,c=()=>{const x={data:p,margin:{top:5,right:30,left:20,bottom:5}};return t==="area"?e.jsxs(Is,J(Y({},x),{children:[e.jsxs("defs",{children:[e.jsxs("linearGradient",{id:"revenueGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:"#10b981",stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:"#10b981",stopOpacity:.1})]}),e.jsxs("linearGradient",{id:"profitGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:"#3b82f6",stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:"#3b82f6",stopOpacity:.1})]})]}),e.jsx(Oe,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(Pe,{dataKey:"month",stroke:"#64748b",fontSize:12}),e.jsx(ye,{stroke:"#64748b",fontSize:12,tickFormatter:d=>`$${d.toLocaleString()}`}),e.jsx(_e,{content:e.jsx(o,{})}),a&&e.jsx($e,{}),e.jsx(ze,{type:"monotone",dataKey:"revenue",stroke:"#10b981",strokeWidth:2,fill:"url(#revenueGradient)",name:"Revenue"}),e.jsx(ze,{type:"monotone",dataKey:"profit",stroke:"#3b82f6",strokeWidth:2,fill:"url(#profitGradient)",name:"Profit"})]})):e.jsxs(Ls,J(Y({},x),{children:[e.jsx(Oe,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(Pe,{dataKey:"month",stroke:"#64748b",fontSize:12}),e.jsx(ye,{stroke:"#64748b",fontSize:12,tickFormatter:d=>`$${d.toLocaleString()}`}),e.jsx(_e,{content:e.jsx(o,{})}),a&&e.jsx($e,{}),e.jsx(le,{type:"monotone",dataKey:"revenue",stroke:"#10b981",strokeWidth:3,dot:{fill:"#10b981",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#10b981",strokeWidth:2},name:"Revenue"}),e.jsx(le,{type:"monotone",dataKey:"profit",stroke:"#3b82f6",strokeWidth:3,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},name:"Profit"}),e.jsx(le,{type:"monotone",dataKey:"expenses",stroke:"#ef4444",strokeWidth:2,dot:{fill:"#ef4444",strokeWidth:2,r:3},activeDot:{r:5,stroke:"#ef4444",strokeWidth:2},name:"Expenses",strokeDasharray:"5 5"})]}))};return e.jsxs(g,{className:`h-full ${r}`,children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"📈"}),e.jsx("h3",{className:"text-lg font-semibold",children:l})]}),e.jsx(E,{color:"primary",variant:"flat",size:"sm",children:"Interactive"})]})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{style:{width:"100%",height:i},children:e.jsx(ds,{children:c()})})})]})},lt=({data:s=[],height:t=300,showLegend:i=!0,className:a="",title:r="Growth Trends"})=>{const l=[{month:"Jan",users:120,missions:45,revenue:2400,userGrowth:8.2,missionGrowth:12.5,revenueGrowth:15.3},{month:"Feb",users:145,missions:52,revenue:3200,userGrowth:20.8,missionGrowth:15.6,revenueGrowth:33.3},{month:"Mar",users:168,missions:48,revenue:2800,userGrowth:15.9,missionGrowth:-7.7,revenueGrowth:-12.5},{month:"Apr",users:195,missions:67,revenue:4100,userGrowth:16.1,missionGrowth:39.6,revenueGrowth:46.4},{month:"May",users:220,missions:73,revenue:3600,userGrowth:12.8,missionGrowth:9,revenueGrowth:-12.2},{month:"Jun",users:258,missions:89,revenue:4800,userGrowth:17.3,missionGrowth:21.9,revenueGrowth:33.3}],n=s.length>0?s:l,p=({active:o,payload:c,label:x})=>o&&c&&c.length?e.jsxs("div",{className:"bg-white dark:bg-slate-800 p-4 border border-default-200 rounded-lg shadow-lg",children:[e.jsx("p",{className:"font-semibold text-default-900 dark:text-default-100 mb-2",children:x}),c.map((d,f)=>{const j=d.dataKey.includes("Growth")?`${d.value}%`:d.value.toLocaleString();return e.jsxs("p",{style:{color:d.color},className:"text-sm",children:[d.name,": ",j]},f)})]}):null;return e.jsxs(g,{className:`h-full ${a}`,children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"📊"}),e.jsx("h3",{className:"text-lg font-semibold",children:r})]}),e.jsx(E,{color:"success",variant:"flat",size:"sm",children:"Growth"})]})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{style:{width:"100%",height:t},children:e.jsx(ds,{children:e.jsxs(rt,{data:n,margin:{top:20,right:30,bottom:20,left:20},children:[e.jsx(Oe,{strokeDasharray:"3 3",stroke:"#e2e8f0"}),e.jsx(Pe,{dataKey:"month",stroke:"#64748b",fontSize:12}),e.jsx(ye,{yAxisId:"left",stroke:"#64748b",fontSize:12}),e.jsx(ye,{yAxisId:"right",orientation:"right",stroke:"#64748b",fontSize:12,tickFormatter:o=>`${o}%`}),e.jsx(_e,{content:e.jsx(p,{})}),i&&e.jsx($e,{}),e.jsx(Ee,{yAxisId:"left",dataKey:"users",fill:"#3b82f6",name:"Users",radius:[2,2,0,0]}),e.jsx(Ee,{yAxisId:"left",dataKey:"missions",fill:"#10b981",name:"Missions",radius:[2,2,0,0]}),e.jsx(le,{yAxisId:"right",type:"monotone",dataKey:"userGrowth",stroke:"#8b5cf6",strokeWidth:3,dot:{fill:"#8b5cf6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#8b5cf6",strokeWidth:2},name:"User Growth %"}),e.jsx(le,{yAxisId:"right",type:"monotone",dataKey:"revenueGrowth",stroke:"#f59e0b",strokeWidth:3,dot:{fill:"#f59e0b",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#f59e0b",strokeWidth:2},name:"Revenue Growth %"})]})})})})]})},ct=({data:s,period:t,className:i=""})=>{const{total:a=0,thisMonth:r=0,growth:l=0,platformFees:n=0,avgMonthly:p=0,hourlyRate:o=0}=s||{},c=b=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(b),d=(b=>b>0?{icon:"↗️",color:"success",text:`+${b}%`}:b<0?{icon:"↘️",color:"danger",text:`${b}%`}:{icon:"→",color:"default",text:"0%"})(l),f=a>0?(n/a*100).toFixed(1):0;return e.jsx("div",{className:`revenue-metrics ${i}`,children:e.jsxs(g,{className:"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 border-2 border-green-200 dark:border-green-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"💰"}),e.jsx("h3",{className:"text-lg font-semibold",children:"Revenue Metrics"})]}),e.jsx(E,{color:"success",variant:"flat",size:"sm",children:t.toUpperCase()})]})}),e.jsxs(w,{className:"pt-0",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-baseline gap-2 mb-1",children:[e.jsx("span",{className:"text-3xl font-bold text-green-600",children:c(a)}),e.jsx(E,{color:d.color,variant:"flat",size:"sm",startContent:e.jsx("span",{children:d.icon}),children:d.text})]}),e.jsx("p",{className:"text-sm text-default-600",children:"Total Revenue"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-default-700",children:"This Month"}),e.jsx("span",{className:"text-lg font-semibold text-green-600",children:c(r)})]}),e.jsx("div",{className:"w-full bg-default-200 rounded-full h-2",children:e.jsx(M.div,{className:"bg-green-500 h-2 rounded-full",initial:{width:0},animate:{width:`${Math.min(r/(a*.4)*100,100)}%`},transition:{duration:1,delay:.5}})})]}),e.jsxs("div",{className:"mb-4 p-3 bg-white/50 dark:bg-slate-800/50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:"💎"}),e.jsx("span",{className:"text-sm font-medium",children:"Platform Fees"})]}),e.jsxs("span",{className:"text-sm font-semibold",children:[c(n)," (",f,"%)"]})]}),e.jsx("div",{className:"text-xs text-default-500",children:"Commission on completed missions"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs("div",{className:"text-center p-2 bg-default-50 dark:bg-slate-800/30 rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-green-600",children:c(p)}),e.jsx("div",{className:"text-xs text-default-600",children:"Avg/Month"})]}),e.jsxs("div",{className:"text-center p-2 bg-default-50 dark:bg-slate-800/30 rounded-lg",children:[e.jsxs("div",{className:"text-lg font-bold text-green-600",children:["$",o]}),e.jsx("div",{className:"text-xs text-default-600",children:"Hourly Rate"})]})]}),e.jsx("div",{className:"mb-4",children:e.jsx(nt,{data:s.chartData,type:"area",height:150,showLegend:!1,className:"bg-white/50 dark:bg-slate-800/50 rounded-lg",title:""})}),e.jsx($,{variant:"flat",color:"success",size:"sm",className:"w-full",children:"View Details"})]})]})})},ot=({data:s,period:t,className:i=""})=>{const[a,r]=S.useState("revenue"),{revenueGrowth:l=[],userGrowth:n=[],missionGrowth:p=[]}=s||{},c={revenue:{data:l,label:"Revenue Growth",color:"text-green-600",bgColor:"bg-green-500",icon:"💰",format:j=>`$${(j/1e3).toFixed(0)}K`},users:{data:n,label:"User Growth",color:"text-blue-600",bgColor:"bg-blue-500",icon:"👥",format:j=>j.toString()},missions:{data:p,label:"Mission Growth",color:"text-purple-600",bgColor:"bg-purple-500",icon:"🎯",format:j=>j.toString()}}[a],x=Math.max(...c.data),d=Math.min(...c.data),f=c.data.length>1?((c.data[c.data.length-1]-c.data[0])/c.data[0]*100).toFixed(1):0;return(j=>{if(!j||j.length===0)return"";const y=300,T=120,h=20,k=(y-2*h)/(j.length-1),A=x-d;return j.map((L,C)=>{const u=h+C*k,P=T-h-(L-d)/A*(T-2*h);return`${u},${P}`}).join(" ")})(c.data),e.jsx("div",{className:`growth-trends ${i}`,children:e.jsxs(g,{className:"bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-800/20 border-2 border-indigo-200 dark:border-indigo-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"📈"}),e.jsx("h3",{className:"text-lg font-semibold",children:"Growth Trends"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx($,{size:"sm",variant:"flat",children:"Projections"}),e.jsx($,{size:"sm",variant:"flat",children:"Export"})]})]})}),e.jsxs(w,{className:"pt-0",children:[e.jsxs(Fe,{selectedKey:a,onSelectionChange:r,variant:"bordered",className:"mb-4",children:[e.jsx(V,{title:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"💰"}),e.jsx("span",{className:"hidden sm:inline",children:"Revenue"})]})},"revenue"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"👥"}),e.jsx("span",{className:"hidden sm:inline",children:"Users"})]})},"users"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"🎯"}),e.jsx("span",{className:"hidden sm:inline",children:"Missions"})]})},"missions")]}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:`text-2xl font-bold ${c.color}`,children:c.format(c.data[c.data.length-1]||0)}),e.jsx("div",{className:"text-sm text-default-600",children:c.label})]}),e.jsxs(E,{color:f>=0?"success":"danger",variant:"flat",startContent:e.jsx("span",{children:f>=0?"↗️":"↘️"}),children:[f>=0?"+":"",f,"%"]})]}),e.jsx("div",{className:"mb-4",children:e.jsx(lt,{data:s,height:200,showLegend:!1,className:"bg-white/50 dark:bg-slate-800/50 rounded-lg",title:""})}),e.jsxs("div",{className:"flex justify-between text-xs text-default-500 mb-4",children:[t==="7d"&&["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map((j,y)=>e.jsx("span",{children:j},y)),t==="30d"&&["Week 1","Week 2","Week 3","Week 4","Week 5","Week 6"].map((j,y)=>e.jsx("span",{children:j},y)),t==="6m"&&["Aug","Sep","Oct","Nov","Dec","Jan"].map((j,y)=>e.jsx("span",{children:j},y))]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-default-600",children:"Peak Value"}),e.jsx("span",{className:"font-medium",children:c.format(x)})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-default-600",children:"Growth Rate"}),e.jsxs("span",{className:`font-medium ${f>=0?"text-green-600":"text-red-600"}`,children:[f>=0?"+":"",f,"%"]})]})]})]})]})})},dt=({score:s=0,period:t,className:i=""})=>{const a=c=>c>=90?{tier:"Excellent",color:"success",icon:"🏆"}:c>=80?{tier:"Good",color:"primary",icon:"🎯"}:c>=70?{tier:"Average",color:"warning",icon:"📊"}:c>=60?{tier:"Below Average",color:"danger",icon:"📉"}:{tier:"Poor",color:"danger",icon:"⚠️"},r=c=>c>=90?"success":c>=80?"primary":c>=70?"warning":"danger",l=a(s),n=r(s),p=s>=85?"+2":s>=75?"+1":"0",o=p.startsWith("+")?"success":p.startsWith("-")?"danger":"default";return e.jsx("div",{className:`performance-score ${i}`,children:e.jsxs(g,{className:"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-800/20 border-2 border-blue-200 dark:border-blue-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xl",children:l.icon}),e.jsx("h3",{className:"text-sm font-semibold",children:"Score"})]}),e.jsx(E,{color:o,variant:"flat",size:"sm",children:p})]})}),e.jsxs(w,{className:"pt-0 flex flex-col justify-center",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsxs(M.div,{className:"text-4xl font-bold text-blue-600 mb-1",initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5},children:[s,"/100"]}),e.jsx(E,{color:l.color,variant:"flat",size:"sm",className:"mb-2",children:l.tier})]}),e.jsx("div",{className:"mb-4",children:e.jsx(B,{value:s,color:n,size:"lg",className:"w-full",showValueLabel:!1})}),e.jsxs("div",{className:"space-y-2 text-xs",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-default-600",children:"Quality"}),e.jsx("span",{className:"font-medium",children:"94%"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-default-600",children:"Speed"}),e.jsx("span",{className:"font-medium",children:"87%"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-default-600",children:"Reliability"}),e.jsx("span",{className:"font-medium",children:"91%"})]})]})]})]})})},mt=({data:s,period:t,className:i=""})=>{const{successRate:a=0,completedMissions:r=0,avgCompletionTime:l=0,qualityScore:n=0}=s||{},p=d=>d>=95?"success":d>=90?"primary":d>=85?"warning":"danger",o=d=>d>=95?"⚡":d>=90?"🎯":d>=85?"📊":"⚠️",c=p(a),x=o(a);return e.jsx("div",{className:`success-rate ${i}`,children:e.jsxs(g,{className:"bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-900/20 dark:to-green-800/20 border-2 border-emerald-200 dark:border-emerald-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xl",children:x}),e.jsx("h3",{className:"text-sm font-semibold",children:"Rate"})]}),e.jsx(E,{color:c,variant:"flat",size:"sm",children:"Success"})]})}),e.jsxs(w,{className:"pt-0 flex flex-col justify-center",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsxs(M.div,{className:"text-4xl font-bold text-emerald-600 mb-1",initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5},children:[a,"%"]}),e.jsx("div",{className:"text-sm text-default-600",children:"Success Rate"})]}),e.jsx("div",{className:"mb-4",children:e.jsx(B,{value:a,color:c,size:"md",className:"w-full",showValueLabel:!1})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-default-600",children:"Completed"}),e.jsxs("span",{className:"text-sm font-semibold text-emerald-600",children:[r," Done"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-default-600",children:"Avg Time"}),e.jsxs("span",{className:"text-sm font-semibold",children:[l,"d"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-default-600",children:"Quality"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-sm font-semibold",children:n}),e.jsx("span",{className:"text-xs text-yellow-500",children:"⭐"})]})]})]})]})]})})},xt=({period:s,className:t=""})=>{const i=[{id:1,name:"TaskMaster Pro",type:"project",avatar:null,earnings:18400,percentage:39,missions:23,rating:4.9,trend:"+12%",badge:"🥇"},{id:2,name:"Creative Studio",type:"project",avatar:null,earnings:8200,percentage:17,missions:15,rating:4.8,trend:"+8%",badge:"🥈"},{id:3,name:"Testing Tool",type:"project",avatar:null,earnings:6800,percentage:14,missions:12,rating:4.7,trend:"+5%",badge:"🥉"}],a=l=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(l),r=l=>l.startsWith("+")?"success":l.startsWith("-")?"danger":"default";return e.jsx("div",{className:`top-performers ${t}`,children:e.jsxs(g,{className:"bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-800/20 border-2 border-orange-200 dark:border-orange-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"🔥"}),e.jsx("h3",{className:"text-lg font-semibold",children:"Top Performers"})]}),e.jsx(E,{color:"warning",variant:"flat",size:"sm",children:s.toUpperCase()})]})}),e.jsxs(w,{className:"pt-0",children:[e.jsx("div",{className:"space-y-4",children:i.map((l,n)=>e.jsxs(M.div,{className:"flex items-center gap-3 p-3 bg-white/50 dark:bg-slate-800/50 rounded-lg",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:n*.1},children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("span",{className:"text-2xl",children:l.badge})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-sm truncate",children:l.name}),e.jsx(E,{color:r(l.trend),variant:"flat",size:"sm",children:l.trend})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-default-600 mb-2",children:[e.jsxs("span",{children:[a(l.earnings)," (",l.percentage,"%)"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:l.rating}),e.jsx("span",{className:"text-yellow-500",children:"⭐"})]})]}),e.jsx("div",{className:"w-full bg-default-200 rounded-full h-2",children:e.jsx(M.div,{className:"bg-orange-500 h-2 rounded-full",initial:{width:0},animate:{width:`${l.percentage*2.5}%`},transition:{duration:1,delay:n*.2}})}),e.jsxs("div",{className:"text-xs text-default-500 mt-1",children:[l.missions," missions completed"]})]})]},l.id))}),e.jsx("div",{className:"mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg",children:e.jsxs("div",{className:"grid grid-cols-2 gap-3 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-lg font-bold text-orange-600",children:a(i.reduce((l,n)=>l+n.earnings,0))}),e.jsx("div",{className:"text-xs text-default-600",children:"Combined Earnings"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg font-bold text-orange-600",children:i.reduce((l,n)=>l+n.missions,0)}),e.jsx("div",{className:"text-xs text-default-600",children:"Total Missions"})]})]})}),e.jsxs("div",{className:"mt-3 text-xs text-default-600",children:[e.jsxs("div",{className:"flex items-center gap-1 mb-1",children:[e.jsx("span",{children:"💡"}),e.jsx("span",{className:"font-medium",children:"Top performer earns 2.3x average"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"📈"}),e.jsx("span",{className:"font-medium",children:"All top 3 showing positive growth"})]})]})]})]})})},ht=({data:s,period:t,className:i=""})=>{const[a,r]=S.useState("efficiency"),n={efficiency:{icon:"🎯",title:"Efficiency Insights",insights:[{type:"improvement",icon:"📈",text:"Efficiency improved by +15% this month",confidence:92,action:"Continue current optimization strategies"},{type:"speed",icon:"⚡",text:"Average completion time: 8.2 days",confidence:88,action:"Target: Reduce to 7.5 days for +12% efficiency"}]},opportunities:{icon:"💡",title:"Growth Opportunities",insights:[{type:"skill-gap",icon:"🔥",text:"AI/ML skill gap limiting high-value bounties",confidence:85,action:"Launch AI learning path for +25% revenue potential"},{type:"market",icon:"📱",text:"Mobile usage declining -5% this month",confidence:78,action:"Implement mobile app improvements"}]},risks:{icon:"⚠️",title:"Risk Alerts",insights:[{type:"engagement",icon:"📉",text:"3 projects showing declining engagement",confidence:94,action:"Implement project health monitoring system"},{type:"capacity",icon:"⏰",text:"Peak capacity reached in development category",confidence:82,action:"Consider expanding development team"}]}}[a],p=c=>c>=90?"success":c>=80?"primary":c>=70?"warning":"danger",o=c=>({improvement:"success",speed:"primary","skill-gap":"warning",market:"secondary",engagement:"danger",capacity:"warning"})[c]||"default";return e.jsx("div",{className:`ai-insights ${i}`,children:e.jsxs(g,{className:"bg-gradient-to-br from-violet-50 to-purple-100 dark:from-violet-900/20 dark:to-purple-800/20 border-2 border-violet-200 dark:border-violet-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"💡"}),e.jsx("h3",{className:"text-lg font-semibold",children:"AI Insights"})]}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx($,{size:"sm",variant:a==="efficiency"?"solid":"flat",color:a==="efficiency"?"primary":"default",onClick:()=>r("efficiency"),children:"🎯"}),e.jsx($,{size:"sm",variant:a==="opportunities"?"solid":"flat",color:a==="opportunities"?"primary":"default",onClick:()=>r("opportunities"),children:"💡"}),e.jsx($,{size:"sm",variant:a==="risks"?"solid":"flat",color:a==="risks"?"primary":"default",onClick:()=>r("risks"),children:"⚠️"})]})]})}),e.jsxs(w,{className:"pt-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx("span",{className:"text-xl",children:n.icon}),e.jsx("h4",{className:"font-semibold",children:n.title})]}),e.jsx("div",{className:"space-y-3",children:n.insights.map((c,x)=>e.jsxs(M.div,{className:"p-3 bg-white/50 dark:bg-slate-800/50 rounded-lg",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:x*.1},children:[e.jsxs("div",{className:"flex items-start gap-2 mb-2",children:[e.jsx("span",{className:"text-lg flex-shrink-0",children:c.icon}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium mb-1",children:c.text}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(E,{color:p(c.confidence),variant:"flat",size:"sm",children:[c.confidence,"% confidence"]}),e.jsx(E,{color:o(c.type),variant:"bordered",size:"sm",children:c.type.replace("-"," ")})]})]})]}),e.jsxs("div",{className:"mt-2 p-2 bg-violet-50 dark:bg-violet-900/20 rounded text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1 mb-1",children:[e.jsx("span",{children:"🎯"}),e.jsx("span",{className:"font-medium",children:"Recommended Action:"})]}),e.jsx("p",{className:"text-violet-700 dark:text-violet-300",children:c.action})]})]},x))}),e.jsxs("div",{className:"mt-4 p-3 bg-gradient-to-r from-violet-100 to-purple-100 dark:from-violet-900/30 dark:to-purple-900/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("span",{className:"text-lg",children:"🤖"}),e.jsx("span",{className:"font-semibold text-sm",children:"AI Summary"})]}),e.jsxs("p",{className:"text-xs text-violet-700 dark:text-violet-300",children:[a==="efficiency"&&"Your platform efficiency is trending upward. Focus on reducing completion times to maximize growth potential.",a==="opportunities"&&"Two major growth opportunities identified. Addressing skill gaps could unlock significant revenue potential.",a==="risks"&&"Moderate risk levels detected. Proactive monitoring and intervention recommended for optimal performance."]})]}),e.jsxs("div",{className:"flex gap-2 mt-4",children:[e.jsx($,{size:"sm",variant:"flat",className:"flex-1",children:"View All"}),e.jsx($,{size:"sm",variant:"flat",className:"flex-1",children:"AI Recs"})]})]})]})})},pt=({onExport:s,onCreateReport:t,onSetAlert:i,className:a=""})=>{const[r,l]=S.useState(!1),[n,p]=S.useState(!1),[o,c]=S.useState(!1),x=[{id:"custom-report",icon:"📊",label:"Custom Report",description:"Create detailed analytics report",color:"primary",action:()=>l(!0)},{id:"forecasting",icon:"📈",label:"Forecasting",description:"AI-powered predictions",color:"secondary",action:()=>t==null?void 0:t("forecasting")},{id:"export-data",icon:"📤",label:"Export Data",description:"Download analytics data",color:"success",action:()=>p(!0)},{id:"settings",icon:"⚙️",label:"Settings",description:"Configure analytics",color:"default",action:()=>t==null?void 0:t("settings")},{id:"alerts",icon:"🔔",label:"Set Alerts",description:"Performance notifications",color:"warning",action:()=>c(!0)},{id:"schedule",icon:"📅",label:"Schedule",description:"Automated reports",color:"secondary",action:()=>t==null?void 0:t("schedule")}];return e.jsxs("div",{className:`quick-actions ${a}`,children:[e.jsxs(g,{className:"bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-cyan-900/20 dark:to-blue-800/20 border-2 border-cyan-200 dark:border-cyan-700 h-full",children:[e.jsx(D,{className:"pb-2",children:e.jsx("div",{className:"flex items-center justify-between w-full",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:"⚡"}),e.jsx("h3",{className:"text-lg font-semibold",children:"Quick Actions"})]})})}),e.jsxs(w,{className:"pt-0",children:[e.jsx("div",{className:"grid grid-cols-2 gap-3",children:x.map((d,f)=>e.jsx(M.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3,delay:f*.05},children:e.jsxs($,{variant:"flat",color:d.color,className:"h-auto p-3 flex flex-col items-center gap-2 w-full",onClick:d.action,children:[e.jsx("span",{className:"text-2xl",children:d.icon}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium",children:d.label}),e.jsx("div",{className:"text-xs opacity-70",children:d.description})]})]})},d.id))}),e.jsxs("div",{className:"mt-4 p-3 bg-cyan-50 dark:bg-cyan-900/20 rounded-lg",children:[e.jsx("h5",{className:"text-sm font-semibold mb-2",children:"Quick Stats"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Reports Generated:"}),e.jsx("span",{className:"font-medium",children:"23"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Data Exports:"}),e.jsx("span",{className:"font-medium",children:"8"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Active Alerts:"}),e.jsx("span",{className:"font-medium",children:"5"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Scheduled:"}),e.jsx("span",{className:"font-medium",children:"3"})]})]})]}),e.jsx($,{variant:"bordered",color:"primary",size:"sm",className:"w-full mt-3",onClick:()=>t==null?void 0:t("view-all"),children:"View All Actions"})]})]}),e.jsx(te,{isOpen:r,onClose:()=>l(!1),size:"md",children:e.jsxs(ae,{children:[e.jsx(ie,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📊 Create Custom Report"})}),e.jsx(re,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(Re,{label:"Report Name",placeholder:"Monthly Performance Review"}),e.jsxs(W,{label:"Report Type",placeholder:"Select report type",children:[e.jsx(v,{children:"Performance Analysis"},"performance"),e.jsx(v,{children:"Revenue Report"},"revenue"),e.jsx(v,{children:"User Engagement"},"user-engagement"),e.jsx(v,{children:"Custom Dashboard"},"custom")]}),e.jsxs(W,{label:"Time Period",placeholder:"Select time period",children:[e.jsx(v,{children:"Last 7 Days"},"7d"),e.jsx(v,{children:"Last 30 Days"},"30d"),e.jsx(v,{children:"Last 90 Days"},"90d"),e.jsx(v,{children:"Custom Range"},"custom")]})]})}),e.jsxs(ne,{children:[e.jsx($,{variant:"flat",onClick:()=>l(!1),children:"Cancel"}),e.jsx($,{color:"primary",onClick:()=>{t==null||t("custom"),l(!1)},children:"Create Report"})]})]})}),e.jsx(te,{isOpen:n,onClose:()=>p(!1),size:"md",children:e.jsxs(ae,{children:[e.jsx(ie,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📤 Export Analytics Data"})}),e.jsx(re,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs(W,{label:"Export Format",placeholder:"Select format",children:[e.jsx(v,{children:"PDF Report"},"pdf"),e.jsx(v,{children:"Excel Spreadsheet"},"excel"),e.jsx(v,{children:"CSV Data"},"csv"),e.jsx(v,{children:"JSON Data"},"json")]}),e.jsxs(W,{label:"Data Range",placeholder:"Select data range",children:[e.jsx(v,{children:"Current Dashboard"},"current"),e.jsx(v,{children:"All Analytics Data"},"all"),e.jsx(v,{children:"Revenue Data Only"},"revenue"),e.jsx(v,{children:"Performance Metrics"},"performance")]})]})}),e.jsxs(ne,{children:[e.jsx($,{variant:"flat",onClick:()=>p(!1),children:"Cancel"}),e.jsx($,{color:"success",onClick:()=>{s==null||s("excel"),p(!1)},children:"Export Data"})]})]})}),e.jsx(te,{isOpen:o,onClose:()=>c(!1),size:"md",children:e.jsxs(ae,{children:[e.jsx(ie,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"🔔 Set Performance Alert"})}),e.jsx(re,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs(W,{label:"Alert Type",placeholder:"Select alert type",children:[e.jsx(v,{children:"Revenue Threshold"},"revenue"),e.jsx(v,{children:"Performance Drop"},"performance"),e.jsx(v,{children:"Completion Rate"},"completion"),e.jsx(v,{children:"Custom Metric"},"custom")]}),e.jsx(Re,{label:"Threshold Value",placeholder:"e.g., 85% or $10,000"}),e.jsxs(W,{label:"Notification Method",placeholder:"How to notify",children:[e.jsx(v,{children:"Email"},"email"),e.jsx(v,{children:"Dashboard Alert"},"dashboard"),e.jsx(v,{children:"Email + Dashboard"},"both")]})]})}),e.jsxs(ne,{children:[e.jsx($,{variant:"flat",onClick:()=>c(!1),children:"Cancel"}),e.jsx($,{color:"warning",onClick:()=>{i==null||i(),c(!1)},children:"Set Alert"})]})]})})]})},ut=({isOpen:s,onClose:t,onSaveReport:i,className:a=""})=>{const[r,l]=S.useState(""),[n,p]=S.useState([]),[o,c]=S.useState({timeRange:"30d",format:"dashboard",schedule:"manual",recipients:[]}),x=[{id:"revenue-metrics",name:"Revenue Metrics",icon:"💰",description:"Total revenue, growth, and financial KPIs",category:"Financial",size:"2x2"},{id:"performance-score",name:"Performance Score",icon:"🎯",description:"Overall performance rating and trends",category:"Performance",size:"1x1"},{id:"success-rate",name:"Success Rate",icon:"⚡",description:"Mission completion and quality metrics",category:"Performance",size:"1x1"},{id:"growth-trends",name:"Growth Trends",icon:"📈",description:"Interactive charts showing growth over time",category:"Analytics",size:"2x2"},{id:"top-performers",name:"Top Performers",icon:"🔥",description:"Ranking of best performing users/projects",category:"Performance",size:"2x1"},{id:"ai-insights",name:"AI Insights",icon:"💡",description:"AI-powered recommendations and predictions",category:"Intelligence",size:"2x1"},{id:"detailed-breakdown",name:"Detailed Breakdown",icon:"📊",description:"Comprehensive data analysis by categories",category:"Analytics",size:"6x2"},{id:"user-engagement",name:"User Engagement",icon:"👥",description:"User activity and engagement metrics",category:"Users",size:"2x1"}],d=["All","Financial","Performance","Analytics","Intelligence","Users"],[f,b]=S.useState("All"),j=f==="All"?x:x.filter(h=>h.category===f),y=h=>{p(k=>k.includes(h)?k.filter(A=>A!==h):[...k,h])},T=()=>{if(!r.trim()||n.length===0)return;const h={name:r,widgets:n,settings:o,createdAt:new Date().toISOString()};i&&i(h),l(""),p([]),c({timeRange:"30d",format:"dashboard",schedule:"manual",recipients:[]}),t()};return e.jsx(te,{isOpen:s,onClose:t,size:"5xl",scrollBehavior:"inside",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(ae,{children:[e.jsx(ie,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-2xl",children:"📊"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Custom Report Builder"}),e.jsx("p",{className:"text-sm text-default-500",children:"Create personalized analytics reports"})]})]})}),e.jsx(re,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Report Configuration"})}),e.jsx(w,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(Re,{label:"Report Name",placeholder:"Monthly Performance Report",value:r,onChange:h=>l(h.target.value)}),e.jsxs(W,{label:"Time Range",selectedKeys:[o.timeRange],onSelectionChange:h=>c(k=>J(Y({},k),{timeRange:Array.from(h)[0]})),children:[e.jsx(v,{children:"Last 7 Days"},"7d"),e.jsx(v,{children:"Last 30 Days"},"30d"),e.jsx(v,{children:"Last 90 Days"},"90d"),e.jsx(v,{children:"Last Year"},"1y")]}),e.jsxs(W,{label:"Output Format",selectedKeys:[o.format],onSelectionChange:h=>c(k=>J(Y({},k),{format:Array.from(h)[0]})),children:[e.jsx(v,{children:"Interactive Dashboard"},"dashboard"),e.jsx(v,{children:"PDF Report"},"pdf"),e.jsx(v,{children:"Excel Spreadsheet"},"excel"),e.jsx(v,{children:"Email Summary"},"email")]}),e.jsxs(W,{label:"Schedule",selectedKeys:[o.schedule],onSelectionChange:h=>c(k=>J(Y({},k),{schedule:Array.from(h)[0]})),children:[e.jsx(v,{children:"Manual Generation"},"manual"),e.jsx(v,{children:"Daily"},"daily"),e.jsx(v,{children:"Weekly"},"weekly"),e.jsx(v,{children:"Monthly"},"monthly")]})]})})]}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Select Widgets"}),e.jsxs(E,{color:"primary",variant:"flat",children:[n.length," selected"]})]})}),e.jsxs(w,{children:[e.jsx("div",{className:"flex gap-2 mb-4 flex-wrap",children:d.map(h=>e.jsx($,{size:"sm",variant:f===h?"solid":"flat",color:f===h?"primary":"default",onPress:()=>b(h),children:h},h))}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:j.map((h,k)=>e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:k*.05},children:e.jsx(g,{isPressable:!0,onPress:()=>y(h.id),className:`cursor-pointer transition-all ${n.includes(h.id)?"border-2 border-primary bg-primary-50":"border border-default-200 hover:border-primary-300"}`,children:e.jsx(w,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("span",{className:"text-2xl",children:h.icon}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-sm",children:h.name}),e.jsx(E,{size:"sm",variant:"flat",color:"secondary",children:h.size})]}),e.jsx("p",{className:"text-xs text-default-600 mb-2",children:h.description}),e.jsx(E,{size:"sm",variant:"bordered",color:"default",children:h.category})]}),e.jsx(cs,{isSelected:n.includes(h.id),onChange:()=>y(h.id)})]})})})},h.id))})]})]}),n.length>0&&e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Report Preview"})}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm text-default-600",children:[e.jsx("strong",{children:"Report:"})," ",r||"Untitled Report"]}),e.jsxs("div",{className:"text-sm text-default-600",children:[e.jsx("strong",{children:"Widgets:"})," ",n.length," components"]}),e.jsxs("div",{className:"text-sm text-default-600",children:[e.jsx("strong",{children:"Time Range:"})," ",o.timeRange]}),e.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:n.map(h=>{const k=x.find(A=>A.id===h);return e.jsxs(E,{size:"sm",variant:"flat",color:"primary",children:[k==null?void 0:k.icon," ",k==null?void 0:k.name]},h)})})]})})]})]})}),e.jsxs(ne,{children:[e.jsx($,{variant:"light",onPress:t,children:"Cancel"}),e.jsx($,{color:"primary",onPress:T,isDisabled:!r.trim()||n.length===0,children:"Create Report"})]})]})})},ft=({className:s=""})=>{var j,y,T,h,k,A,L,C,u,P,I,F,_,R,Q,m,z,X,de,me,xe,he,pe,ue;const{currentUser:t}=S.useContext(be),[i,a]=S.useState(!0),[r,l]=S.useState("6m"),[n,p]=S.useState(!1),[o,c]=S.useState({}),x=()=>ee(null,null,function*(){try{a(!0);const{data:{session:O}}=yield Ae.auth.getSession();if(!(O==null?void 0:O.access_token)){U.error("Authentication required");return}c({overview:{totalRevenue:47200,totalExpenses:8400,netProfit:38800,profitMargin:82.2,growthRate:23.5,avgMonthlyRevenue:7867},revenueBreakdown:{projectRevenue:{amount:30680,percentage:65,growth:18},commissionFees:{amount:11800,percentage:25,growth:32},bonusPayments:{amount:4720,percentage:10,growth:15}},expenseBreakdown:{platformFees:{amount:2360,percentage:28,growth:12},tools:{amount:1680,percentage:20,growth:8},marketing:{amount:1260,percentage:15,growth:25},education:{amount:840,percentage:10,growth:45},other:{amount:2260,percentage:27,growth:5}},monthlyTrends:[{month:"Jul",revenue:6200,expenses:1200,profit:5e3},{month:"Aug",revenue:7100,expenses:1350,profit:5750},{month:"Sep",revenue:7800,expenses:1400,profit:6400},{month:"Oct",revenue:8500,expenses:1500,profit:7e3},{month:"Nov",revenue:8900,expenses:1600,profit:7300},{month:"Dec",revenue:8700,expenses:1350,profit:7350}],projections:{nextMonth:{revenue:9200,confidence:85},nextQuarter:{revenue:28500,confidence:78},nextYear:{revenue:112e3,confidence:65}},taxInfo:{estimatedTax:9700,quarterlyPayments:2425,deductions:3200,taxableIncome:35600}})}catch(O){U.error("Failed to load financial analytics")}finally{a(!1)}}),d=O=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(O||0),f=O=>`${O>0?"+":""}${O.toFixed(1)}%`,b=O=>O>15?"success":O>5?"primary":O>0?"warning":"danger";return S.useEffect(()=>{x()},[r]),i?e.jsx(g,{className:s,children:e.jsxs(w,{className:"p-6 text-center",children:[e.jsx("div",{className:"animate-spin text-2xl mb-2",children:"🔄"}),e.jsx("div",{children:"Loading financial analytics..."})]})}):e.jsxs("div",{className:`financial-analytics ${s}`,children:[e.jsx(g,{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 mb-6",children:e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"💰"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Financial Analytics"}),e.jsx("p",{className:"text-default-600",children:"Comprehensive financial performance and insights"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(W,{selectedKeys:[r],onSelectionChange:O=>l(Array.from(O)[0]),className:"w-40",size:"sm",children:[e.jsx(v,{children:"Last Month"},"1m"),e.jsx(v,{children:"Last 3 Months"},"3m"),e.jsx(v,{children:"Last 6 Months"},"6m"),e.jsx(v,{children:"Last Year"},"1y")]}),e.jsx($,{color:"primary",variant:"flat",size:"sm",onPress:()=>p(!0),children:"📊 Tax Report"})]})]})})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:[e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsx(g,{className:"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20",children:e.jsxs(w,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Total Revenue"}),e.jsx("div",{className:"text-2xl font-bold text-green-600",children:d((j=o.overview)==null?void 0:j.totalRevenue)})]}),e.jsx("div",{className:"text-3xl",children:"📈"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(E,{color:"success",size:"sm",variant:"flat",children:f((y=o.overview)==null?void 0:y.growthRate)}),e.jsx("span",{className:"text-sm text-default-600",children:"vs last period"})]})]})})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(g,{className:"bg-gradient-to-br from-red-50 to-pink-100 dark:from-red-900/20 dark:to-pink-800/20",children:e.jsxs(w,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Total Expenses"}),e.jsx("div",{className:"text-2xl font-bold text-red-600",children:d((T=o.overview)==null?void 0:T.totalExpenses)})]}),e.jsx("div",{className:"text-3xl",children:"📉"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(E,{color:"danger",size:"sm",variant:"flat",children:[(((h=o.overview)==null?void 0:h.totalExpenses)/((k=o.overview)==null?void 0:k.totalRevenue)*100).toFixed(1),"%"]}),e.jsx("span",{className:"text-sm text-default-600",children:"of revenue"})]})]})})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsx(g,{className:"bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-800/20",children:e.jsxs(w,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Net Profit"}),e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:d((A=o.overview)==null?void 0:A.netProfit)})]}),e.jsx("div",{className:"text-3xl",children:"💎"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(E,{color:"primary",size:"sm",variant:"flat",children:[(L=o.overview)==null?void 0:L.profitMargin.toFixed(1),"% margin"]}),e.jsx("span",{className:"text-sm text-default-600",children:"profit margin"})]})]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsx(M.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"💰 Revenue Breakdown"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:Object.entries(o.revenueBreakdown||{}).map(([O,K])=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium capitalize",children:O.replace(/([A-Z])/g," $1").trim()}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-semibold",children:d(K.amount)}),e.jsx(E,{color:b(K.growth),size:"sm",variant:"flat",children:f(K.growth)})]})]}),e.jsx(B,{value:K.percentage,color:"success",size:"sm"}),e.jsxs("div",{className:"text-xs text-default-500",children:[K.percentage,"% of total revenue"]})]},O))})})]})}),e.jsx(M.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.5},children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📊 Expense Breakdown"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:Object.entries(o.expenseBreakdown||{}).map(([O,K])=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium capitalize",children:O.replace(/([A-Z])/g," $1").trim()}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-semibold",children:d(K.amount)}),e.jsx(E,{color:b(K.growth),size:"sm",variant:"flat",children:f(K.growth)})]})]}),e.jsx(B,{value:K.percentage,color:"danger",size:"sm"}),e.jsxs("div",{className:"text-xs text-default-500",children:[K.percentage,"% of total expenses"]})]},O))})})]})})]}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"mb-6",children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📈 Monthly Financial Trends"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:(C=o.monthlyTrends)==null?void 0:C.map((O,K)=>e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 text-sm font-medium",children:O.month}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{children:"Revenue"}),e.jsx("span",{className:"font-semibold text-success",children:d(O.revenue)})]}),e.jsx(B,{value:O.revenue/1e4*100,color:"success",size:"sm"})]}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{children:"Expenses"}),e.jsx("span",{className:"font-semibold text-danger",children:d(O.expenses)})]}),e.jsx(B,{value:O.expenses/2e3*100,color:"danger",size:"sm"})]}),e.jsxs("div",{className:"w-24 text-right",children:[e.jsx("div",{className:"text-sm text-default-600",children:"Profit"}),e.jsx("div",{className:"font-semibold text-primary",children:d(O.profit)})]})]},O.month))})})]})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:e.jsxs(g,{className:"bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20",children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"🔮 Financial Projections"})}),e.jsx(w,{className:"pt-0",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm text-default-600 mb-1",children:"Next Month"}),e.jsx("div",{className:"text-xl font-bold text-primary",children:d((P=(u=o.projections)==null?void 0:u.nextMonth)==null?void 0:P.revenue)}),e.jsxs("div",{className:"text-xs text-default-500",children:[(F=(I=o.projections)==null?void 0:I.nextMonth)==null?void 0:F.confidence,"% confidence"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm text-default-600 mb-1",children:"Next Quarter"}),e.jsx("div",{className:"text-xl font-bold text-secondary",children:d((R=(_=o.projections)==null?void 0:_.nextQuarter)==null?void 0:R.revenue)}),e.jsxs("div",{className:"text-xs text-default-500",children:[(m=(Q=o.projections)==null?void 0:Q.nextQuarter)==null?void 0:m.confidence,"% confidence"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm text-default-600 mb-1",children:"Next Year"}),e.jsx("div",{className:"text-xl font-bold text-warning",children:d((X=(z=o.projections)==null?void 0:z.nextYear)==null?void 0:X.revenue)}),e.jsxs("div",{className:"text-xs text-default-500",children:[(me=(de=o.projections)==null?void 0:de.nextYear)==null?void 0:me.confidence,"% confidence"]})]})]})})]})}),e.jsx(te,{isOpen:n,onClose:()=>p(!1),size:"2xl",children:e.jsxs(ae,{children:[e.jsx(ie,{children:e.jsx("span",{className:"text-xl",children:"📊 Tax Report Summary"})}),e.jsx(re,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Taxable Income"}),e.jsx("div",{className:"text-lg font-semibold",children:d((xe=o.taxInfo)==null?void 0:xe.taxableIncome)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Estimated Tax"}),e.jsx("div",{className:"text-lg font-semibold text-warning",children:d((he=o.taxInfo)==null?void 0:he.estimatedTax)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Quarterly Payment"}),e.jsx("div",{className:"text-lg font-semibold",children:d((pe=o.taxInfo)==null?void 0:pe.quarterlyPayments)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Total Deductions"}),e.jsx("div",{className:"text-lg font-semibold text-success",children:d((ue=o.taxInfo)==null?void 0:ue.deductions)})]})]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-medium mb-2",children:"💡 Tax Optimization Tips"}),e.jsxs("ul",{className:"text-sm space-y-1",children:[e.jsx("li",{children:"• Consider increasing business expense deductions"}),e.jsx("li",{children:"• Track all professional development costs"}),e.jsx("li",{children:"• Document home office expenses if applicable"}),e.jsx("li",{children:"• Consult with a tax professional for optimization"})]})]})]})}),e.jsxs(ne,{children:[e.jsx($,{variant:"light",onPress:()=>p(!1),children:"Close"}),e.jsx($,{color:"primary",onPress:()=>U.success("Tax report exported!"),children:"Export Report"})]})]})})]})},jt=({className:s=""})=>{var b,j,y,T,h,k,A,L,C,u,P,I,F,_,R,Q,m,z,X,de,me,xe,he,pe,ue,O,K,Me,Ge,Be,Ke,We,Ue,Qe,Ve,qe,He,Ye,Je,Ze,Xe,es;const{currentUser:t}=S.useContext(be),[i,a]=S.useState(!0),[r,l]=S.useState("all"),[n,p]=S.useState({}),o=()=>ee(null,null,function*(){try{a(!0);const{data:{session:N}}=yield Ae.auth.getSession();if(!(N==null?void 0:N.access_token)){U.error("Authentication required");return}p({overview:{totalProjects:24,completedProjects:22,activeProjects:2,successRate:91.7,avgCompletionTime:18.5,onTimeDelivery:86.4,budgetEfficiency:94.2},recentProjects:[{id:"proj_001",name:"E-commerce Platform Redesign",status:"completed",completionDate:"2025-01-10",duration:45,plannedDuration:50,budget:15e3,actualCost:14200,efficiency:98,rating:4.9,team:[{id:"u1",name:"Sarah Chen",role:"Designer",avatar:null,contribution:35},{id:"u2",name:"Mike Johnson",role:"Developer",avatar:null,contribution:45},{id:"u3",name:"Alex Kim",role:"PM",avatar:null,contribution:20}],milestones:[{name:"Design Phase",planned:15,actual:12,status:"completed"},{name:"Development",planned:25,actual:28,status:"completed"},{name:"Testing",planned:7,actual:5,status:"completed"},{name:"Deployment",planned:3,actual:3,status:"completed"}]},{id:"proj_002",name:"Mobile App Development",status:"active",startDate:"2024-12-15",duration:32,plannedDuration:40,budget:22e3,currentCost:16800,efficiency:85,rating:null,team:[{id:"u4",name:"Lisa Wang",role:"Lead Dev",avatar:null,contribution:40},{id:"u5",name:"Tom Brown",role:"UI/UX",avatar:null,contribution:30},{id:"u6",name:"Emma Davis",role:"QA",avatar:null,contribution:30}],milestones:[{name:"Planning",planned:8,actual:7,status:"completed"},{name:"Development",planned:20,actual:18,status:"active"},{name:"Testing",planned:8,actual:0,status:"pending"},{name:"Launch",planned:4,actual:0,status:"pending"}]}],performanceMetrics:{timelinePerformance:{onTime:19,early:3,late:2,avgDelay:2.3},budgetPerformance:{underBudget:18,onBudget:4,overBudget:2,avgSavings:5.8},qualityMetrics:{avgRating:4.7,clientSatisfaction:94,reworkRate:8,defectRate:2.1}},insights:[{type:"success",title:"Excellent Timeline Management",description:"Your projects are consistently delivered on time with 86% on-time delivery rate.",impact:"high"},{type:"warning",title:"Development Phase Bottleneck",description:"Development phases tend to take 15% longer than planned. Consider better estimation.",impact:"medium"},{type:"info",title:"Strong Team Collaboration",description:"Team satisfaction scores are 20% above platform average.",impact:"medium"}]})}catch(N){U.error("Failed to load project insights")}finally{a(!1)}}),c=N=>N<7?`${N} days`:N<30?`${Math.round(N/7)} weeks`:`${Math.round(N/30)} months`,x=N=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(N||0),d=N=>({completed:"success",active:"primary",pending:"warning",delayed:"danger"})[N]||"default",f=N=>N>=95?"success":N>=85?"primary":N>=75?"warning":"danger";return S.useEffect(()=>{o()},[r]),i?e.jsx(g,{className:s,children:e.jsxs(w,{className:"p-6 text-center",children:[e.jsx("div",{className:"animate-spin text-2xl mb-2",children:"🔄"}),e.jsx("div",{children:"Loading project insights..."})]})}):e.jsxs("div",{className:`project-insights ${s}`,children:[e.jsx(g,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 mb-6",children:e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"🎯"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Project Insights"}),e.jsx("p",{className:"text-default-600",children:"Performance analysis and optimization recommendations"})]})]}),e.jsxs(W,{selectedKeys:[r],onSelectionChange:N=>l(Array.from(N)[0]),className:"w-48",size:"sm",children:[e.jsx(v,{children:"All Projects"},"all"),e.jsx(v,{children:"Completed Only"},"completed"),e.jsx(v,{children:"Active Only"},"active")]})]})})}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsx(g,{children:e.jsxs(w,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:(b=n.overview)==null?void 0:b.totalProjects}),e.jsx("div",{className:"text-sm text-default-600",children:"Total Projects"})]})})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(g,{children:e.jsxs(w,{className:"p-4 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-success",children:[(j=n.overview)==null?void 0:j.successRate,"%"]}),e.jsx("div",{className:"text-sm text-default-600",children:"Success Rate"})]})})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsx(g,{children:e.jsxs(w,{className:"p-4 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-warning",children:[(y=n.overview)==null?void 0:y.onTimeDelivery,"%"]}),e.jsx("div",{className:"text-sm text-default-600",children:"On-Time Delivery"})]})})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:e.jsx(g,{children:e.jsxs(w,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-secondary",children:c((T=n.overview)==null?void 0:T.avgCompletionTime)}),e.jsx("div",{className:"text-sm text-default-600",children:"Avg Duration"})]})})})]}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mb-6",children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📊 Recent Projects"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-6",children:(h=n.recentProjects)==null?void 0:h.map((N,fe)=>e.jsxs(M.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*fe},className:"border border-default-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-lg",children:N.name}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsx(E,{color:d(N.status),size:"sm",variant:"flat",children:N.status}),N.rating&&e.jsxs(E,{color:"warning",size:"sm",variant:"flat",children:["⭐ ",N.rating]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-default-600",children:"Efficiency"}),e.jsxs("div",{className:`text-lg font-bold text-${f(N.efficiency)}`,children:[N.efficiency,"%"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Duration"}),e.jsxs("div",{className:"font-medium",children:[c(N.duration)," / ",c(N.plannedDuration)]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Budget"}),e.jsxs("div",{className:"font-medium",children:[x(N.actualCost||N.currentCost)," / ",x(N.budget)]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Team Size"}),e.jsxs("div",{className:"font-medium",children:[N.team.length," members"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-default-600",children:"Progress"}),e.jsxs("div",{className:"font-medium",children:[N.status==="completed"?"100%":Math.round(N.duration/N.plannedDuration*100),"%"]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-sm font-medium mb-2",children:"Team Members"}),e.jsx("div",{className:"flex items-center gap-3",children:N.team.map(q=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ws,{src:q.avatar,name:q.name,size:"sm"}),e.jsxs("div",{className:"text-xs",children:[e.jsx("div",{className:"font-medium",children:q.name}),e.jsx("div",{className:"text-default-500",children:q.role})]})]},q.id))})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium mb-2",children:"Milestones"}),e.jsx("div",{className:"space-y-2",children:N.milestones.map((q,fs)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-32 text-sm",children:q.name}),e.jsx("div",{className:"flex-1",children:e.jsx(B,{value:q.status==="completed"?100:q.status==="active"?60:0,color:d(q.status),size:"sm"})}),e.jsxs("div",{className:"text-xs text-default-500 w-20",children:[q.actual||0,"/",q.planned," days"]})]},fs))})]})]},N.id))})})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6",children:[e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"⏰ Timeline Performance"})}),e.jsx(w,{className:"pt-0",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"On Time"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{value:((A=(k=n.performanceMetrics)==null?void 0:k.timelinePerformance)==null?void 0:A.onTime)/24*100,color:"success",size:"sm",className:"w-16"}),e.jsx("span",{className:"text-sm font-medium",children:(C=(L=n.performanceMetrics)==null?void 0:L.timelinePerformance)==null?void 0:C.onTime})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Early"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{value:((P=(u=n.performanceMetrics)==null?void 0:u.timelinePerformance)==null?void 0:P.early)/24*100,color:"primary",size:"sm",className:"w-16"}),e.jsx("span",{className:"text-sm font-medium",children:(F=(I=n.performanceMetrics)==null?void 0:I.timelinePerformance)==null?void 0:F.early})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Late"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{value:((R=(_=n.performanceMetrics)==null?void 0:_.timelinePerformance)==null?void 0:R.late)/24*100,color:"danger",size:"sm",className:"w-16"}),e.jsx("span",{className:"text-sm font-medium",children:(m=(Q=n.performanceMetrics)==null?void 0:Q.timelinePerformance)==null?void 0:m.late})]})]}),e.jsxs("div",{className:"text-xs text-default-500 mt-2",children:["Avg delay: ",(X=(z=n.performanceMetrics)==null?void 0:z.timelinePerformance)==null?void 0:X.avgDelay," days"]})]})})]})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"💰 Budget Performance"})}),e.jsx(w,{className:"pt-0",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Under Budget"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{value:((me=(de=n.performanceMetrics)==null?void 0:de.budgetPerformance)==null?void 0:me.underBudget)/24*100,color:"success",size:"sm",className:"w-16"}),e.jsx("span",{className:"text-sm font-medium",children:(he=(xe=n.performanceMetrics)==null?void 0:xe.budgetPerformance)==null?void 0:he.underBudget})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"On Budget"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{value:((ue=(pe=n.performanceMetrics)==null?void 0:pe.budgetPerformance)==null?void 0:ue.onBudget)/24*100,color:"primary",size:"sm",className:"w-16"}),e.jsx("span",{className:"text-sm font-medium",children:(K=(O=n.performanceMetrics)==null?void 0:O.budgetPerformance)==null?void 0:K.onBudget})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Over Budget"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{value:((Ge=(Me=n.performanceMetrics)==null?void 0:Me.budgetPerformance)==null?void 0:Ge.overBudget)/24*100,color:"danger",size:"sm",className:"w-16"}),e.jsx("span",{className:"text-sm font-medium",children:(Ke=(Be=n.performanceMetrics)==null?void 0:Be.budgetPerformance)==null?void 0:Ke.overBudget})]})]}),e.jsxs("div",{className:"text-xs text-default-500 mt-2",children:["Avg savings: ",(Ue=(We=n.performanceMetrics)==null?void 0:We.budgetPerformance)==null?void 0:Ue.avgSavings,"%"]})]})})]})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"⭐ Quality Metrics"})}),e.jsx(w,{className:"pt-0",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Avg Rating"}),e.jsxs("span",{className:"text-sm font-medium",children:[(Ve=(Qe=n.performanceMetrics)==null?void 0:Qe.qualityMetrics)==null?void 0:Ve.avgRating,"/5.0"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Client Satisfaction"}),e.jsxs("span",{className:"text-sm font-medium",children:[(He=(qe=n.performanceMetrics)==null?void 0:qe.qualityMetrics)==null?void 0:He.clientSatisfaction,"%"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Rework Rate"}),e.jsxs("span",{className:"text-sm font-medium",children:[(Je=(Ye=n.performanceMetrics)==null?void 0:Ye.qualityMetrics)==null?void 0:Je.reworkRate,"%"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Defect Rate"}),e.jsxs("span",{className:"text-sm font-medium",children:[(Xe=(Ze=n.performanceMetrics)==null?void 0:Ze.qualityMetrics)==null?void 0:Xe.defectRate,"%"]})]})]})})]})})]}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.9},children:e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"💡 Insights & Recommendations"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-3",children:(es=n.insights)==null?void 0:es.map((N,fe)=>e.jsx("div",{className:`p-3 rounded-lg border-l-4 ${N.type==="success"?"border-success bg-success-50 dark:bg-success-900/20":N.type==="warning"?"border-warning bg-warning-50 dark:bg-warning-900/20":"border-primary bg-primary-50 dark:bg-primary-900/20"}`,children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("span",{className:"text-lg",children:N.type==="success"?"✅":N.type==="warning"?"⚠️":"ℹ️"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:N.title}),e.jsx("p",{className:"text-sm text-default-600 mt-1",children:N.description}),e.jsxs(E,{size:"sm",variant:"flat",color:N.impact==="high"?"danger":N.impact==="medium"?"warning":"default",className:"mt-2",children:[N.impact," impact"]})]})]})},fe))})})]})})]})},vt=({className:s=""})=>{var y,T,h,k,A,L,C,u,P,I,F,_,R,Q;const{currentUser:t}=S.useContext(be),[i,a]=S.useState(!0),[r,l]=S.useState("3m"),[n,p]=S.useState("revenue"),[o,c]=S.useState({}),x=()=>ee(null,null,function*(){try{a(!0);const{data:{session:m}}=yield Ae.auth.getSession();if(!(m==null?void 0:m.access_token)){U.error("Authentication required");return}c({revenueForecast:{current:47200,predictions:[{period:"Next Month",amount:52e3,confidence:87,growth:10.2},{period:"Next Quarter",amount:165e3,confidence:82,growth:15.8},{period:"Next 6 Months",amount:34e4,confidence:75,growth:18.5},{period:"Next Year",amount:72e4,confidence:68,growth:22.3}],factors:[{name:"Seasonal Trends",impact:15,type:"positive"},{name:"Market Growth",impact:22,type:"positive"},{name:"Competition",impact:-8,type:"negative"},{name:"Skill Development",impact:12,type:"positive"}]},marketTrends:{opportunities:[{title:"AI/ML Development Surge",description:"Machine learning projects are increasing by 45% in your skill areas",potential:"High",timeframe:"2-3 months",confidence:85},{title:"Remote Work Tools Demand",description:"Growing demand for collaboration and productivity tools",potential:"Medium",timeframe:"1-2 months",confidence:78},{title:"E-commerce Platform Migration",description:"Businesses migrating to modern e-commerce solutions",potential:"High",timeframe:"3-6 months",confidence:82}],threats:[{title:"Increased Competition",description:"More developers entering your primary skill areas",severity:"Medium",timeframe:"6 months",mitigation:"Specialize in niche technologies"},{title:"Economic Uncertainty",description:"Potential reduction in project budgets",severity:"Low",timeframe:"3-4 months",mitigation:"Diversify client base"}]},performancePredictions:{skillDevelopment:[{skill:"React",currentLevel:8,predictedLevel:9,timeframe:"3 months",confidence:92},{skill:"Node.js",currentLevel:7,predictedLevel:8,timeframe:"4 months",confidence:88},{skill:"Python",currentLevel:6,predictedLevel:7,timeframe:"5 months",confidence:85},{skill:"AI/ML",currentLevel:4,predictedLevel:6,timeframe:"6 months",confidence:75}],projectSuccess:{nextProject:{successProbability:94,factors:["Strong skill match","Good client history"]},riskFactors:["Tight timeline","New technology stack"],recommendations:["Allocate extra time for learning","Consider team collaboration"]}},seasonalPatterns:{busyPeriods:[{period:"Q1",activity:85,reason:"New year project launches"},{period:"Q2",activity:92,reason:"Mid-year initiatives"},{period:"Q3",activity:78,reason:"Summer slowdown"},{period:"Q4",activity:95,reason:"Year-end deadlines"}],recommendations:["Plan major skill development during Q3 slower period","Prepare for increased demand in Q4","Consider vacation scheduling around Q2 peak"]},riskAssessment:{overall:"Low",factors:[{category:"Market Risk",level:"Low",score:25},{category:"Competition Risk",level:"Medium",score:45},{category:"Technology Risk",level:"Low",score:30},{category:"Client Risk",level:"Low",score:20}],mitigationStrategies:["Diversify skill portfolio to reduce technology risk","Build stronger client relationships for retention","Monitor competitor activities and pricing"]}})}catch(m){U.error("Failed to load predictive insights")}finally{a(!1)}}),d=m=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(m||0),f=m=>m>=85?"success":m>=70?"primary":m>=60?"warning":"danger",b=m=>({High:"success",Medium:"warning",Low:"default"})[m]||"default",j=m=>({Low:"success",Medium:"warning",High:"danger"})[m]||"default";return S.useEffect(()=>{x()},[r]),i?e.jsx(g,{className:s,children:e.jsxs(w,{className:"p-6 text-center",children:[e.jsx("div",{className:"animate-spin text-2xl mb-2",children:"🔄"}),e.jsx("div",{children:"Loading predictive insights..."})]})}):e.jsxs("div",{className:`predictive-insights ${s}`,children:[e.jsx(g,{className:"bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 mb-6",children:e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"🔮"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Predictive Insights"}),e.jsx("p",{className:"text-default-600",children:"AI-powered forecasting and trend analysis"})]})]}),e.jsxs(W,{selectedKeys:[r],onSelectionChange:m=>l(Array.from(m)[0]),className:"w-40",size:"sm",children:[e.jsx(v,{children:"1 Month"},"1m"),e.jsx(v,{children:"3 Months"},"3m"),e.jsx(v,{children:"6 Months"},"6m"),e.jsx(v,{children:"1 Year"},"1y")]})]})})}),e.jsx(g,{children:e.jsx(w,{className:"p-0",children:e.jsxs(Fe,{selectedKey:n,onSelectionChange:p,variant:"underlined",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-primary",tab:"max-w-fit px-4 h-12",tabContent:"group-data-[selected=true]:text-primary"},children:[e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"💰"}),e.jsx("span",{children:"Revenue Forecast"})]}),children:e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:(y=o.revenueForecast)==null?void 0:y.predictions.map((m,z)=>e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:z*.1},children:e.jsx(g,{className:"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20",children:e.jsx(w,{className:"p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm text-default-600 mb-1",children:m.period}),e.jsx("div",{className:"text-xl font-bold text-green-600 mb-2",children:d(m.amount)}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsxs(E,{color:"success",size:"sm",variant:"flat",children:["+",m.growth,"%"]}),e.jsxs(E,{color:f(m.confidence),size:"sm",variant:"flat",children:[m.confidence,"% confidence"]})]})]})})})},m.period))}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📊 Influencing Factors"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-3",children:(T=o.revenueForecast)==null?void 0:T.factors.map((m,z)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:m.name}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:`text-sm ${m.type==="positive"?"text-success":"text-danger"}`,children:[m.type==="positive"?"+":"",m.impact,"%"]}),e.jsx(E,{color:m.type==="positive"?"success":"danger",size:"sm",variant:"flat",children:m.type})]})]},z))})})]})]})},"revenue"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"📈"}),e.jsx("span",{children:"Market Trends"})]}),children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"🚀 Opportunities"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:(h=o.marketTrends)==null?void 0:h.opportunities.map((m,z)=>e.jsxs(M.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:z*.1},className:"border border-default-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsx("h4",{className:"font-semibold",children:m.title}),e.jsx(E,{color:b(m.potential),size:"sm",variant:"flat",children:m.potential})]}),e.jsx("p",{className:"text-sm text-default-600 mb-3",children:m.description}),e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsxs("span",{className:"text-default-500",children:["Timeframe: ",m.timeframe]}),e.jsxs("span",{className:"text-default-500",children:["Confidence: ",m.confidence,"%"]})]})]},z))})})]}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"⚠️ Threats"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:(k=o.marketTrends)==null?void 0:k.threats.map((m,z)=>e.jsxs(M.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:z*.1},className:"border border-default-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsx("h4",{className:"font-semibold",children:m.title}),e.jsx(E,{color:j(m.severity),size:"sm",variant:"flat",children:m.severity})]}),e.jsx("p",{className:"text-sm text-default-600 mb-3",children:m.description}),e.jsxs("div",{className:"text-xs text-default-500 mb-2",children:["Timeframe: ",m.timeframe]}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-2 rounded text-xs",children:[e.jsx("strong",{children:"Mitigation:"})," ",m.mitigation]})]},z))})})]})]})})},"market"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"🎯"}),e.jsx("span",{children:"Performance"})]}),children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📚 Skill Development Forecast"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:(A=o.performancePredictions)==null?void 0:A.skillDevelopment.map((m,z)=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:m.skill}),e.jsxs("div",{className:"text-sm",children:["Level ",m.currentLevel," → ",m.predictedLevel]})]}),e.jsx(B,{value:m.predictedLevel/10*100,color:"primary",size:"sm"}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-default-500",children:[e.jsx("span",{children:m.timeframe}),e.jsxs("span",{children:[m.confidence,"% confidence"]})]})]},m.skill))})})]}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"🎯 Project Success Prediction"})}),e.jsx(w,{className:"pt-0",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-3xl font-bold text-success mb-2",children:[(L=o.performancePredictions)==null?void 0:L.projectSuccess.nextProject.successProbability,"%"]}),e.jsx("div",{className:"text-sm text-default-600",children:"Success Probability"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Positive Factors"}),e.jsx("div",{className:"space-y-1",children:(C=o.performancePredictions)==null?void 0:C.projectSuccess.nextProject.factors.map((m,z)=>e.jsxs("div",{className:"text-sm text-success",children:["✓ ",m]},z))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Risk Factors"}),e.jsx("div",{className:"space-y-1",children:(u=o.performancePredictions)==null?void 0:u.projectSuccess.riskFactors.map((m,z)=>e.jsxs("div",{className:"text-sm text-warning",children:["⚠ ",m]},z))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Recommendations"}),e.jsx("div",{className:"space-y-1",children:(P=o.performancePredictions)==null?void 0:P.projectSuccess.recommendations.map((m,z)=>e.jsxs("div",{className:"text-sm text-primary",children:["💡 ",m]},z))})]})]})})]})]})})},"performance"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"📅"}),e.jsx("span",{children:"Seasonal Patterns"})]}),children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📊 Quarterly Activity Patterns"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-4",children:(I=o.seasonalPatterns)==null?void 0:I.busyPeriods.map((m,z)=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:m.period}),e.jsxs("span",{className:"text-sm",children:[m.activity,"% activity"]})]}),e.jsx(B,{value:m.activity,color:"secondary",size:"sm"}),e.jsx("div",{className:"text-xs text-default-500",children:m.reason})]},m.period))})})]}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"💡 Seasonal Recommendations"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-3",children:(F=o.seasonalPatterns)==null?void 0:F.recommendations.map((m,z)=>e.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:e.jsx("div",{className:"text-sm",children:m})},z))})})]})]})})},"seasonal"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"🛡️"}),e.jsx("span",{children:"Risk Assessment"})]}),children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"🛡️ Risk Assessment"})}),e.jsxs(w,{className:"pt-0",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsxs("div",{className:"text-3xl font-bold text-success mb-2",children:[(_=o.riskAssessment)==null?void 0:_.overall," Risk"]}),e.jsx("div",{className:"text-sm text-default-600",children:"Overall Risk Level"})]}),e.jsx("div",{className:"space-y-4",children:(R=o.riskAssessment)==null?void 0:R.factors.map((m,z)=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:m.category}),e.jsx(E,{color:j(m.level),size:"sm",variant:"flat",children:m.level})]}),e.jsx(B,{value:m.score,color:j(m.level),size:"sm"})]},m.category))})]})]}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"🛠️ Mitigation Strategies"})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{className:"space-y-3",children:(Q=o.riskAssessment)==null?void 0:Q.mitigationStrategies.map((m,z)=>e.jsx("div",{className:"p-3 border border-default-200 rounded-lg",children:e.jsx("div",{className:"text-sm",children:m})},z))})})]})]})})},"risk")]})})})]})},gt=({data:s,chartType:t="line",className:i=""})=>{var b,j;const[a,r]=S.useState(t),[l,n]=S.useState("6m"),[p,o]=S.useState({}),c=S.useRef(null),x={line:{icon:"📈",name:"Line Chart",description:"Trend analysis over time"},bar:{icon:"📊",name:"Bar Chart",description:"Comparative analysis"},pie:{icon:"🥧",name:"Pie Chart",description:"Proportion breakdown"},scatter:{icon:"🔵",name:"Scatter Plot",description:"Correlation analysis"},heatmap:{icon:"🔥",name:"Heatmap",description:"Intensity visualization"},treemap:{icon:"🌳",name:"Treemap",description:"Hierarchical data"}},d=()=>{o({line:{labels:["Jan","Feb","Mar","Apr","May","Jun"],datasets:[{label:"Revenue",data:[4200,5100,4800,6200,7100,8900],color:"#3b82f6",trend:"up"},{label:"Expenses",data:[1200,1350,1100,1400,1600,1800],color:"#ef4444",trend:"up"}]},bar:{categories:["React","Node.js","Python","Design","DevOps"],values:[85,78,65,72,58],colors:["#10b981","#3b82f6","#f59e0b","#8b5cf6","#ef4444"]},pie:{segments:[{label:"Project Revenue",value:65,color:"#10b981"},{label:"Commission Fees",value:25,color:"#3b82f6"},{label:"Bonus Payments",value:10,color:"#f59e0b"}]},scatter:{points:[{x:20,y:4200,label:"Project A"},{x:35,y:6800,label:"Project B"},{x:15,y:3200,label:"Project C"},{x:45,y:8900,label:"Project D"},{x:28,y:5600,label:"Project E"}]},heatmap:{data:[[0,0,85],[0,1,92],[0,2,78],[0,3,65],[1,0,78],[1,1,88],[1,2,95],[1,3,82],[2,0,65],[2,1,75],[2,2,88],[2,3,90],[3,0,92],[3,1,85],[3,2,78],[3,3,88]],xLabels:["Q1","Q2","Q3","Q4"],yLabels:["Revenue","Projects","Quality","Growth"]},treemap:{data:[{name:"Frontend",value:45,children:[{name:"React",value:25},{name:"Vue.js",value:15},{name:"Angular",value:5}]},{name:"Backend",value:35,children:[{name:"Node.js",value:20},{name:"Python",value:10},{name:"PHP",value:5}]},{name:"Design",value:20,children:[{name:"UI/UX",value:15},{name:"Graphics",value:5}]}]}})},f=()=>{const y=p[a];if(!y)return null;switch(a){case"line":return e.jsx(yt,{data:y});case"bar":return e.jsx(bt,{data:y});case"pie":return e.jsx(Nt,{data:y});case"scatter":return e.jsx(wt,{data:y});case"heatmap":return e.jsx(Pt,{data:y});case"treemap":return e.jsx(kt,{data:y});default:return e.jsx("div",{className:"text-center text-default-600",children:"Chart type not supported"})}};return S.useEffect(()=>{d()},[l]),e.jsxs("div",{className:`data-visualization ${i}`,children:[e.jsx(g,{className:"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 mb-6",children:e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"📊"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Data Visualization"}),e.jsx("p",{className:"text-default-600",children:"Interactive charts and advanced analytics"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(W,{selectedKeys:[l],onSelectionChange:y=>n(Array.from(y)[0]),className:"w-32",size:"sm",children:[e.jsx(v,{children:"1 Month"},"1m"),e.jsx(v,{children:"3 Months"},"3m"),e.jsx(v,{children:"6 Months"},"6m"),e.jsx(v,{children:"1 Year"},"1y")]}),e.jsx($,{color:"primary",variant:"flat",size:"sm",onPress:()=>U.success("Chart exported successfully!"),children:"📥 Export"})]})]})})}),e.jsx(g,{className:"mb-6",children:e.jsx(w,{className:"p-4",children:e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3",children:Object.entries(x).map(([y,T])=>e.jsx(M.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(g,{isPressable:!0,onPress:()=>r(y),className:`cursor-pointer transition-all ${a===y?"border-2 border-primary bg-primary-50 dark:bg-primary-900/20":"border border-default-200 hover:border-primary-300"}`,children:e.jsxs(w,{className:"p-3 text-center",children:[e.jsx("div",{className:"text-2xl mb-1",children:T.icon}),e.jsx("div",{className:"text-sm font-medium",children:T.name}),e.jsx("div",{className:"text-xs text-default-500",children:T.description})]})})},y))})})}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("h3",{className:"text-lg font-semibold",children:[(b=x[a])==null?void 0:b.icon," ",(j=x[a])==null?void 0:j.name]}),e.jsx(E,{color:"primary",variant:"flat",size:"sm",children:"Interactive"})]})}),e.jsx(w,{className:"pt-0",children:e.jsx("div",{ref:c,className:"w-full h-96",children:f()})})]})]})},yt=({data:s})=>{if(!(s!=null&&s.datasets))return null;const t=Math.max(...s.datasets.flatMap(i=>i.data));return e.jsxs("div",{className:"w-full h-full flex flex-col",children:[e.jsx("div",{className:"flex-1 relative",children:e.jsxs("svg",{className:"w-full h-full",viewBox:"0 0 600 300",children:[[0,1,2,3,4].map(i=>e.jsx("line",{x1:"50",y1:50+i*50,x2:"550",y2:50+i*50,stroke:"#e5e7eb",strokeWidth:"1"},i)),s.datasets.map((i,a)=>{const r=i.data.map((n,p)=>({x:50+p*100,y:250-n/t*200})),l=r.map((n,p)=>`${p===0?"M":"L"} ${n.x} ${n.y}`).join(" ");return e.jsxs("g",{children:[e.jsx("path",{d:l,fill:"none",stroke:i.color,strokeWidth:"3",className:"animate-pulse"}),r.map((n,p)=>e.jsx("circle",{cx:n.x,cy:n.y,r:"4",fill:i.color,className:"hover:r-6 transition-all cursor-pointer"},p))]},a)}),s.labels.map((i,a)=>e.jsx("text",{x:50+a*100,y:"280",textAnchor:"middle",className:"text-xs fill-current text-default-600",children:i},a))]})}),e.jsx("div",{className:"flex justify-center gap-4 mt-4",children:s.datasets.map((i,a)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:i.color}}),e.jsx("span",{className:"text-sm",children:i.label})]},a))})]})},bt=({data:s})=>{if(!(s!=null&&s.categories))return null;const t=Math.max(...s.values);return e.jsx("div",{className:"w-full h-full flex flex-col",children:e.jsx("div",{className:"flex-1 relative",children:e.jsx("svg",{className:"w-full h-full",viewBox:"0 0 600 300",children:s.categories.map((i,a)=>{const r=s.values[a]/t*200,l=50+a*90;return e.jsxs("g",{children:[e.jsx("rect",{x:l,y:250-r,width:"60",height:r,fill:s.colors[a],className:"hover:opacity-80 transition-opacity cursor-pointer"}),e.jsx("text",{x:l+30,y:"280",textAnchor:"middle",className:"text-xs fill-current text-default-600",children:i}),e.jsx("text",{x:l+30,y:245-r,textAnchor:"middle",className:"text-xs fill-current text-default-800 font-medium",children:s.values[a]})]},a)})})})})},Nt=({data:s})=>{if(!(s!=null&&s.segments))return null;let t=0;const i=80,a=150,r=150;return e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsxs("div",{className:"flex items-center gap-8",children:[e.jsx("svg",{width:"300",height:"300",viewBox:"0 0 300 300",children:s.segments.map((l,n)=>{const p=l.value/100*360,o=t,c=t+p,x=a+i*Math.cos(o*Math.PI/180),d=r+i*Math.sin(o*Math.PI/180),f=a+i*Math.cos(c*Math.PI/180),b=r+i*Math.sin(c*Math.PI/180),j=p>180?1:0,y=[`M ${a} ${r}`,`L ${x} ${d}`,`A ${i} ${i} 0 ${j} 1 ${f} ${b}`,"Z"].join(" ");return t+=p,e.jsx("path",{d:y,fill:l.color,className:"hover:opacity-80 transition-opacity cursor-pointer"},n)})}),e.jsx("div",{className:"space-y-2",children:s.segments.map((l,n)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:l.color}}),e.jsx("span",{className:"text-sm",children:l.label}),e.jsxs("span",{className:"text-sm font-medium",children:[l.value,"%"]})]},n))})]})})},wt=({data:s})=>{if(!(s!=null&&s.points))return null;const t=Math.max(...s.points.map(a=>a.x)),i=Math.max(...s.points.map(a=>a.y));return e.jsx("div",{className:"w-full h-full",children:e.jsxs("svg",{className:"w-full h-full",viewBox:"0 0 600 300",children:[[0,1,2,3,4].map(a=>e.jsxs("g",{children:[e.jsx("line",{x1:"50",y1:50+a*50,x2:"550",y2:50+a*50,stroke:"#e5e7eb",strokeWidth:"1"}),e.jsx("line",{x1:50+a*125,y1:"50",x2:50+a*125,y2:"250",stroke:"#e5e7eb",strokeWidth:"1"})]},a)),s.points.map((a,r)=>e.jsx("circle",{cx:50+a.x/t*500,cy:250-a.y/i*200,r:"6",fill:"#3b82f6",className:"hover:r-8 transition-all cursor-pointer"},r))]})})},Pt=({data:s})=>{if(!(s!=null&&s.data))return null;const t=Math.max(...s.data.map(i=>i[2]));return e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsxs("svg",{width:"400",height:"300",viewBox:"0 0 400 300",children:[s.data.map((i,a)=>{const[r,l,n]=i,o=`rgba(59, 130, 246, ${n/t})`;return e.jsx("rect",{x:50+r*80,y:50+l*50,width:"75",height:"45",fill:o,stroke:"#fff",strokeWidth:"2",className:"hover:opacity-80 transition-opacity cursor-pointer"},a)}),s.xLabels.map((i,a)=>e.jsx("text",{x:87+a*80,y:"40",textAnchor:"middle",className:"text-xs fill-current text-default-600",children:i},a)),s.yLabels.map((i,a)=>e.jsx("text",{x:"40",y:77+a*50,textAnchor:"end",className:"text-xs fill-current text-default-600",children:i},a))]})})},kt=({data:s})=>s!=null&&s.data?e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsx("div",{className:"grid grid-cols-2 gap-2 w-96 h-64",children:s.data.map((t,i)=>e.jsxs("div",{className:"bg-primary-100 dark:bg-primary-900/20 rounded-lg p-4 flex flex-col justify-center items-center hover:bg-primary-200 dark:hover:bg-primary-900/40 transition-colors cursor-pointer",style:{gridRowEnd:t.value>30?"span 2":"span 1",gridColumnEnd:t.value>40?"span 2":"span 1"},children:[e.jsx("div",{className:"font-semibold text-center",children:t.name}),e.jsxs("div",{className:"text-sm text-default-600",children:[t.value,"%"]})]},i))})}):null,St=({className:s=""})=>{const{currentUser:t}=S.useContext(be),[i,a]=S.useState(!1),[r,l]=S.useState(0),[n,p]=S.useState(!1),[o,c]=S.useState([]),[x,d]=S.useState({format:"pdf",dataTypes:["financial","projects"],dateRange:"6m",includeCharts:!0,includeRawData:!1,template:"standard",schedule:"manual"}),f={pdf:{icon:"📄",name:"PDF Report",description:"Professional formatted report",features:["Charts","Tables","Branding"]},excel:{icon:"📊",name:"Excel Workbook",description:"Spreadsheet with multiple sheets",features:["Raw Data","Formulas","Charts"]},csv:{icon:"📋",name:"CSV Data",description:"Comma-separated values",features:["Raw Data","Lightweight","Universal"]},json:{icon:"🔧",name:"JSON Data",description:"Structured data format",features:["API Ready","Structured","Programmatic"]},png:{icon:"🖼️",name:"PNG Images",description:"Chart images",features:["High Quality","Presentations","Sharing"]},svg:{icon:"🎨",name:"SVG Graphics",description:"Scalable vector graphics",features:["Scalable","Editable","Web Ready"]}},b={financial:{icon:"💰",name:"Financial Data",description:"Revenue, expenses, profits"},projects:{icon:"🎯",name:"Project Analytics",description:"Performance, timelines, success rates"},skills:{icon:"🎓",name:"Skill Development",description:"Progress, certifications, levels"},achievements:{icon:"🏆",name:"Achievements",description:"Badges, milestones, rewards"},studios:{icon:"🤝",name:"Studio Data",description:"Team performance, collaboration"}},j={standard:{name:"Standard Report",description:"Comprehensive overview with all metrics"},executive:{name:"Executive Summary",description:"High-level insights for leadership"},detailed:{name:"Detailed Analysis",description:"In-depth analysis with raw data"},financial:{name:"Financial Focus",description:"Revenue and financial performance only"},performance:{name:"Performance Review",description:"Project and skill performance metrics"}},y=[{id:"exp_001",name:"Q4 Financial Report",format:"pdf",size:"2.4 MB",created:new Date(Date.now()-2*24*60*60*1e3),status:"completed",downloadUrl:"#"},{id:"exp_002",name:"Project Analytics Data",format:"excel",size:"1.8 MB",created:new Date(Date.now()-5*24*60*60*1e3),status:"completed",downloadUrl:"#"},{id:"exp_003",name:"Skills Progress Charts",format:"png",size:"856 KB",created:new Date(Date.now()-7*24*60*60*1e3),status:"completed",downloadUrl:"#"}],T=()=>ee(null,null,function*(){p(!0),l(0);try{const u=[{step:"Collecting data...",progress:20},{step:"Processing analytics...",progress:40},{step:"Generating charts...",progress:60},{step:"Formatting report...",progress:80},{step:"Finalizing export...",progress:100}];for(const I of u)yield new Promise(F=>setTimeout(F,1e3)),l(I.progress),U.loading(I.step,{id:"export-progress"});const P={id:`exp_${Date.now()}`,name:`${j[x.template].name} - ${new Date().toLocaleDateString()}`,format:x.format,size:`${(Math.random()*3+.5).toFixed(1)} MB`,created:new Date,status:"completed",downloadUrl:"#"};c(I=>[P,...I]),U.success("Export completed successfully!",{id:"export-progress"}),a(!1)}catch(u){U.error("Export failed. Please try again.",{id:"export-progress"})}finally{p(!1),l(0)}}),h=(u,P)=>{d(I=>J(Y({},I),{[u]:P}))},k=u=>{d(P=>J(Y({},P),{dataTypes:P.dataTypes.includes(u)?P.dataTypes.filter(I=>I!==u):[...P.dataTypes,u]}))},A=u=>u,L=u=>new Date(u).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),C=u=>({completed:"success",processing:"primary",failed:"danger",pending:"warning"})[u]||"default";return e.jsxs("div",{className:`export-manager ${s}`,children:[e.jsx(g,{className:"bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 mb-6",children:e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"📥"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Export Manager"}),e.jsx("p",{className:"text-default-600",children:"Export and download your analytics data"})]})]}),e.jsx($,{color:"primary",variant:"shadow",onPress:()=>a(!0),children:"+ New Export"})]})})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:Object.entries(f).slice(0,3).map(([u,P])=>e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},whileHover:{scale:1.02},children:e.jsx(g,{isPressable:!0,onPress:()=>{h("format",u),a(!0)},className:"cursor-pointer hover:shadow-lg transition-all",children:e.jsxs(w,{className:"p-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("span",{className:"text-2xl",children:P.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:P.name}),e.jsx("p",{className:"text-sm text-default-600",children:P.description})]})]}),e.jsx("div",{className:"flex flex-wrap gap-1",children:P.features.map((I,F)=>e.jsx(E,{size:"sm",variant:"flat",color:"primary",children:I},F))})]})})},u))}),e.jsxs(g,{children:[e.jsx(D,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"📋 Export History"})}),e.jsx(w,{className:"pt-0",children:y.length>0?e.jsx("div",{className:"space-y-3",children:y.map((u,P)=>{var I;return e.jsxs(M.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:P*.1},className:"flex items-center justify-between p-3 border border-default-200 rounded-lg hover:bg-default-50 dark:hover:bg-default-900/20 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-xl",children:(I=f[u.format])==null?void 0:I.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:u.name}),e.jsxs("div",{className:"text-sm text-default-600",children:[L(u.created)," • ",A(u.size)]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(E,{color:C(u.status),size:"sm",variant:"flat",children:u.status}),e.jsx($,{size:"sm",variant:"bordered",onPress:()=>U.success("Download started!"),children:"Download"})]})]},u.id)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📥"}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No exports yet"}),e.jsx("p",{className:"text-default-600 mb-4",children:"Create your first export to get started"}),e.jsx($,{color:"primary",onPress:()=>a(!0),children:"Create Export"})]})})]}),e.jsx(te,{isOpen:i,onClose:()=>a(!1),size:"3xl",scrollBehavior:"inside",children:e.jsxs(ae,{children:[e.jsx(ie,{children:e.jsx("span",{className:"text-xl",children:"📥 Configure Export"})}),e.jsx(re,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-3",children:"Export Format"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:Object.entries(f).map(([u,P])=>e.jsx(g,{isPressable:!0,onPress:()=>h("format",u),className:`cursor-pointer transition-all ${x.format===u?"border-2 border-primary bg-primary-50 dark:bg-primary-900/20":"border border-default-200 hover:border-primary-300"}`,children:e.jsxs(w,{className:"p-3 text-center",children:[e.jsx("div",{className:"text-xl mb-1",children:P.icon}),e.jsx("div",{className:"text-sm font-medium",children:P.name})]})},u))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-3",children:"Data to Include"}),e.jsx("div",{className:"space-y-2",children:Object.entries(b).map(([u,P])=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(cs,{isSelected:x.dataTypes.includes(u),onValueChange:()=>k(u)}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:P.icon}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:P.name}),e.jsx("div",{className:"text-sm text-default-600",children:P.description})]})]})]},u))})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs(W,{label:"Date Range",selectedKeys:[x.dateRange],onSelectionChange:u=>h("dateRange",Array.from(u)[0]),children:[e.jsx(v,{children:"Last Month"},"1m"),e.jsx(v,{children:"Last 3 Months"},"3m"),e.jsx(v,{children:"Last 6 Months"},"6m"),e.jsx(v,{children:"Last Year"},"1y"),e.jsx(v,{children:"All Time"},"all")]}),e.jsx(W,{label:"Report Template",selectedKeys:[x.template],onSelectionChange:u=>h("template",Array.from(u)[0]),children:Object.entries(j).map(([u,P])=>e.jsx(v,{description:P.description,children:P.name},u))})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Include Charts"}),e.jsx("div",{className:"text-sm text-default-600",children:"Add visual charts to the export"})]}),e.jsx(as,{isSelected:x.includeCharts,onValueChange:u=>h("includeCharts",u)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Include Raw Data"}),e.jsx("div",{className:"text-sm text-default-600",children:"Add detailed raw data tables"})]}),e.jsx(as,{isSelected:x.includeRawData,onValueChange:u=>h("includeRawData",u)})]})]}),n&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Export Progress"}),e.jsxs("span",{children:[r,"%"]})]}),e.jsx(B,{value:r,color:"primary",size:"sm"})]})]})}),e.jsxs(ne,{children:[e.jsx($,{variant:"light",onPress:()=>a(!1),isDisabled:n,children:"Cancel"}),e.jsx($,{color:"primary",onPress:T,isLoading:n,isDisabled:n||x.dataTypes.length===0,children:n?"Exporting...":"Start Export"})]})]})})]})},_t=({className:s=""})=>{const{currentUser:t}=S.useContext(be),[i,a]=S.useState(!0),[r,l]=S.useState("30d"),[n,p]=S.useState(!1),[o,c]=S.useState([]),[x,d]=S.useState("overview"),[f,b]=S.useState({revenue:{total:47200,thisMonth:18400,growth:23,platformFees:2600,avgMonthly:2650,hourlyRate:47.5},performance:{score:85,successRate:94,completedMissions:89,avgCompletionTime:8.2,qualityScore:4.7},trends:{revenueGrowth:[2e4,25e3,32e3,38e3,42e3,47200],userGrowth:[120,135,148,152,156,162],missionGrowth:[65,72,78,83,87,89]}}),j=[{key:"7d",label:"Last 7 Days"},{key:"30d",label:"Last 30 Days"},{key:"90d",label:"Last 90 Days"},{key:"6m",label:"Last 6 Months"},{key:"1y",label:"Last Year"}],y=()=>ee(null,null,function*(){try{a(!0);const{data:{session:C}}=yield Ae.auth.getSession();if(!(C!=null&&C.access_token))throw new Error("Authentication required");const P={"7d":"last_7_days","30d":"last_30_days","90d":"last_3_months","6m":"last_6_months","1y":"last_12_months"}[r]||"last_30_days",F=yield(yield fetch(`/.netlify/functions/analytics-service/dashboard?period=${P}`,{headers:{Authorization:`Bearer ${C.access_token}`,"Content-Type":"application/json"}})).json();if(!F.success)throw new Error(F.error||"Failed to load analytics data");const{key_metrics:_,financial_summary:R,performance_metrics:Q,recent_events:m}=F.data,z={revenue:{total:(R==null?void 0:R.total_revenue)||0,thisMonth:(R==null?void 0:R.total_revenue)||0,growth:(R==null?void 0:R.revenue_growth_rate)||0,platformFees:(R==null?void 0:R.platform_fees)||0,avgMonthly:(R==null?void 0:R.total_revenue)||0,hourlyRate:47.5},performance:{score:(_==null?void 0:_.success_rate)||0,successRate:(_==null?void 0:_.success_rate)||0,completedMissions:(_==null?void 0:_.project_count)||0,avgCompletionTime:8.2,qualityScore:(_==null?void 0:_.avg_rating)||0},trends:{revenueGrowth:T((R==null?void 0:R.total_revenue)||0,r),userGrowth:[120,135,148,152,156,162],missionGrowth:T((_==null?void 0:_.project_count)||0,r)},rawData:{key_metrics:_,financial_summary:R,performance_metrics:Q,recent_events:m}};b(z)}catch(C){U.error("Failed to load analytics data"),b({revenue:{total:r==="7d"?12e3:r==="30d"?47200:142e3,thisMonth:r==="7d"?12e3:18400,growth:r==="7d"?15:r==="30d"?23:28,platformFees:r==="7d"?720:2600,avgMonthly:2650,hourlyRate:47.5},performance:{score:85,successRate:r==="7d"?96:94,completedMissions:r==="7d"?23:89,avgCompletionTime:8.2,qualityScore:4.7},trends:{revenueGrowth:r==="7d"?[8e3,9200,10500,11200,11800,12e3]:[2e4,25e3,32e3,38e3,42e3,47200],userGrowth:r==="7d"?[156,158,159,160,161,162]:[120,135,148,152,156,162],missionGrowth:r==="7d"?[84,85,86,87,88,89]:[65,72,78,83,87,89]}})}finally{a(!1)}}),T=(C,u)=>{const P=u==="7d"?7:u==="30d"?6:12,I=[],F=C*.7,_=(C-F)/(P-1);for(let R=0;R<P;R++)I.push(Math.round(F+_*R));return I},h=C=>{l(C)},k=C=>{c(u=>[...u,J(Y({},C),{id:Date.now()})]),U.success(`Custom report "${C.name}" created successfully!`)},A=C=>{U.success(`Exporting analytics data as ${C.toUpperCase()}`)},L=()=>{p(!0)};return S.useEffect(()=>{y()},[r]),i?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading analytics data..."})]})}):e.jsxs("div",{className:`analytics-dashboard ${s}`,children:[e.jsx(g,{className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 mb-6",children:e.jsx(D,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-3xl",children:"📊"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Analytics Command Center"}),e.jsx("p",{className:"text-default-600",children:"Comprehensive performance insights and data-driven decision making"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(W,{selectedKeys:[r],onSelectionChange:C=>h(Array.from(C)[0]),className:"w-40",size:"sm",children:j.map(C=>e.jsx(v,{children:C.label},C.key))}),e.jsx($,{color:"secondary",variant:"flat",size:"sm",onPress:L,children:"📊 Custom Report"}),e.jsx($,{color:"primary",variant:"flat",size:"sm",onPress:()=>A("pdf"),children:"📤 Export"})]})]})})}),e.jsx(g,{children:e.jsx(w,{className:"p-0",children:e.jsxs(Fe,{selectedKey:x,onSelectionChange:d,variant:"underlined",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-primary",tab:"max-w-fit px-4 h-12",tabContent:"group-data-[selected=true]:text-primary"},children:[e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"📊"}),e.jsx("span",{children:"Overview"})]}),children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6",children:[e.jsx(M.div,{className:"md:col-span-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},children:e.jsx(ct,{data:f.revenue,period:r})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},children:e.jsx(dt,{score:f.performance.score,period:r})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.3},children:e.jsx(mt,{data:f.performance,period:r})})]}),e.jsx(M.div,{className:"mb-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.4},children:e.jsx(ot,{data:f.trends,period:r})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.6},children:e.jsx(xt,{period:r})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.7},children:e.jsx(ht,{data:f,period:r})}),e.jsx(M.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.8},children:e.jsx(pt,{onExport:A,onCreateReport:L,onSetAlert:()=>U.success("Alert configured")})})]})]})},"overview"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"💰"}),e.jsx("span",{children:"Financial"})]}),children:e.jsx("div",{className:"p-6",children:e.jsx(ft,{})})},"financial"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"🎯"}),e.jsx("span",{children:"Projects"})]}),children:e.jsx("div",{className:"p-6",children:e.jsx(jt,{})})},"projects"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"🔮"}),e.jsx("span",{children:"Predictive"})]}),children:e.jsx("div",{className:"p-6",children:e.jsx(vt,{})})},"predictive"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"📈"}),e.jsx("span",{children:"Charts"})]}),children:e.jsx("div",{className:"p-6",children:e.jsx(gt,{})})},"visualization"),e.jsx(V,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:"📥"}),e.jsx("span",{children:"Export"})]}),children:e.jsx("div",{className:"p-6",children:e.jsx(St,{})})},"export")]})})}),e.jsx(ut,{isOpen:n,onClose:()=>p(!1),onSaveReport:k})]})};export{_t as default};
