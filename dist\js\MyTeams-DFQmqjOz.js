var L=Object.defineProperty,$=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var w=Object.getOwnPropertySymbols;var D=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var v=(r,a,t)=>a in r?L(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t,_=(r,a)=>{for(var t in a||(a={}))D.call(a,t)&&v(r,t,a[t]);if(w)for(var t of w(a))M.call(a,t)&&v(r,t,a[t]);return r},C=(r,a)=>$(r,A(a));var y=(r,a,t)=>new Promise((c,x)=>{var o=n=>{try{h(t.next(n))}catch(u){x(u)}},b=n=>{try{h(t.throw(n))}catch(u){x(u)}},h=n=>n.done?c(n.value):Promise.resolve(n.value).then(o,b);h((t=t.apply(r,a)).next())});import{r as j,j as e,c as f,b as g,m as k,h as l}from"./chunk-DX4Z_LyS.js";import{U as Y,s as N,L as R}from"../assets/main-CGUKzV0x.js";import{a as q}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";import"./chunk-BiNxGM8y.js";const Q=({canvasId:r,sectionId:a})=>{const{currentUser:t}=j.useContext(Y),c=q(),[x,o]=j.useState([]),[b,h]=j.useState(!0),[n,u]=j.useState([]);j.useEffect(()=>{t&&(T(),S())},[t]);const T=()=>y(null,null,function*(){try{const{data:s,error:i}=yield N.from("team_members").select("*").eq("user_id",t.id);if(i){o([]);return}if(!s||s.length===0){o([]);return}const d=s.map(m=>m.team_id),{data:p,error:U}=yield N.from("teams").select(`
          id,
          name,
          description,
          is_business_entity,
          studio_type,
          company_id,
          created_at
        `).in("id",d);if(U){o([]);return}const B=s.map(m=>C(_({},m),{teams:p.find(I=>I.id===m.team_id)})).filter(m=>m.teams);o(B)}catch(s){o([])}finally{h(!1)}}),S=()=>y(null,null,function*(){try{const{data:{session:s}}=yield N.auth.getSession(),i=s==null?void 0:s.access_token;if(i){const d=yield fetch("/.netlify/functions/companies",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(d.ok){const p=yield d.json();u(p.companies||[])}}}catch(s){}}),z=s=>{switch(s){case"established":return"🏰";case"emerging":return"🌱";case"solo":return"⭐";default:return"👥"}},E=s=>{switch(s){case"established":return"Established Studio";case"emerging":return"Emerging Studio";case"solo":return"Solo Project";default:return"Team"}};return b?e.jsx("div",{className:"p-6",children:e.jsx(f,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsx(g,{className:"p-8",children:e.jsx(R,{})})})}):e.jsxs(k.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"p-6 space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"👥 My Teams & Studios"}),e.jsx("p",{className:"text-white/70",children:"Manage your teams, studios, and business entities"})]}),e.jsx(f,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsx(g,{className:"p-6",children:e.jsxs("div",{className:"flex flex-wrap gap-4 justify-center",children:[e.jsx(l,{color:"primary",variant:"solid",size:"lg",startContent:"🏰",onClick:()=>c("/studios/create"),children:"Create Studio"}),e.jsx(l,{color:"secondary",variant:"solid",size:"lg",startContent:"🤝",onClick:()=>c("/studios"),children:"Join Studio"}),e.jsx(l,{color:"success",variant:"solid",size:"lg",startContent:"🏢",onClick:()=>c("/studios/create"),children:"Register Business"})]})})}),x.length>0?e.jsx("div",{className:"grid gap-4",children:x.map(s=>{const i=s.teams,d=n.find(p=>p.id===i.company_id);return e.jsx(k.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(f,{className:"bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/20 transition-all cursor-pointer",children:e.jsx(g,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-4xl",children:z(i.studio_type)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:i.name}),e.jsx("p",{className:"text-white/70 text-sm",children:E(i.studio_type)}),i.description&&e.jsx("p",{className:"text-white/60 text-sm mt-1",children:i.description}),d&&e.jsx("div",{className:"flex items-center mt-2",children:e.jsxs("span",{className:"text-green-400 text-sm",children:["🏢 ",d.legal_name]})})]})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.is_admin&&e.jsx("span",{className:"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded text-xs",children:"👑 Admin"}),i.is_business_entity&&e.jsx("span",{className:"bg-green-500/20 text-green-300 px-2 py-1 rounded text-xs",children:"🏢 Business"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(l,{size:"sm",color:"primary",variant:"flat",onClick:()=>c(`/teams/${i.id}`),children:"View"}),s.is_admin&&e.jsx(l,{size:"sm",color:"secondary",variant:"flat",onClick:()=>c(`/teams/${i.id}/manage`),children:"Manage"})]})]})]})})})},i.id)})}):e.jsx(f,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(g,{className:"p-8 text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🏰"}),e.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:"No Teams Yet"}),e.jsx("p",{className:"text-white/70 mb-6",children:"You haven't joined any teams or studios yet. Create your first studio or join an existing one to get started!"}),e.jsxs("div",{className:"flex gap-4 justify-center",children:[e.jsx(l,{color:"primary",size:"lg",startContent:"🏰",onClick:()=>c("/studios/create"),children:"Create Your Studio"}),e.jsx(l,{color:"secondary",variant:"bordered",size:"lg",startContent:"🔍",onClick:()=>c("/studios"),children:"Discover Studios"})]})]})}),n.length>0&&e.jsx(f,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(g,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"🏢 Business Entities"}),e.jsx("div",{className:"grid gap-3",children:n.map(s=>e.jsxs("div",{className:"flex items-center justify-between bg-white/5 rounded-lg p-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:s.legal_name}),e.jsxs("p",{className:"text-white/60 text-sm",children:[s.company_type.toUpperCase()," • ",s.tax_id]})]}),e.jsx(l,{size:"sm",color:"success",variant:"flat",onClick:()=>c(`/companies/${s.id}/manage`),children:"Manage"})]},s.id))})]})})]})};export{Q as default};
