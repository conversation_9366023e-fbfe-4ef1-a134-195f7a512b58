var U=Object.defineProperty,$=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var C=(n,a,r)=>a in n?U(n,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[a]=r,b=(n,a)=>{for(var r in a||(a={}))O.call(a,r)&&C(n,r,a[r]);if(R)for(var r of R(a))k.call(a,r)&&C(n,r,a[r]);return n},w=(n,a)=>$(n,L(a));var S=(n,a,r)=>new Promise((v,x)=>{var d=o=>{try{i(r.next(o))}catch(h){x(h)}},y=o=>{try{i(r.throw(o))}catch(h){x(h)}},i=o=>o.done?v(o.value):Promise.resolve(o.value).then(d,y);i((r=r.apply(n,a)).next())});import{r as j,j as e,c,b as l,u as E,v as A,S as G,T as g,m as u,w as p,y as B,i as J,h as K}from"./chunk-DX4Z_LyS.js";import{U as V}from"../assets/main-CGUKzV0x.js";import{J as z,z as _,O as M,g as D,Q as H,y as Q,aF as W,aE as Y,H as q,aG as X}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";const ce=({className:n="",period:a="6m"})=>{const{currentUser:r}=j.useContext(V),[v,x]=j.useState(!0),[d,y]=j.useState("overview"),[i,o]=j.useState({overview:{totalRevenue:47200,monthlyAverage:7867,growth:15.3,bestMonth:"December",bestMonthAmount:12400,projectedAnnual:94400},performance:{topVentures:[{name:"TaskMaster Pro",revenue:18600,growth:23.5,share:39.4},{name:"Creative Studio",revenue:14200,growth:12.8,share:30.1},{name:"Testing Tool",revenue:8900,growth:-5.2,share:18.9},{name:"Analytics Platform",revenue:5500,growth:45.7,share:11.6}],revenueStreams:{fixed:{amount:18400,percentage:39,growth:8.2},revenue:{amount:23600,percentage:50,growth:22.1},bonuses:{amount:5200,percentage:11,growth:15.8}},workTypes:{development:{amount:28300,percentage:60,hours:340},design:{amount:12400,percentage:26,hours:155},testing:{amount:4200,percentage:9,hours:84},consulting:{amount:2300,percentage:5,hours:23}}},trends:{monthlyData:[6200,7800,8400,9200,8600,7200],labels:["Aug","Sep","Oct","Nov","Dec","Jan"],predictions:[7800,8200,8600],predictionLabels:["Feb","Mar","Apr"]},insights:{recommendations:[{type:"opportunity",title:"Revenue Share Focus",description:"Revenue share projects show 22% higher growth than fixed-rate work",action:"Consider prioritizing revenue share opportunities",impact:"high"},{type:"warning",title:"Testing Tool Decline",description:"Testing Tool project revenue decreased by 5.2% this month",action:"Review project status and engagement level",impact:"medium"},{type:"success",title:"Analytics Platform Growth",description:"New Analytics Platform project showing 45.7% growth",action:"Consider increasing time allocation to this project",impact:"high"}],metrics:{efficiency:85,diversification:72,growth:88,stability:76}}});j.useEffect(()=>{h()},[a,r]);const h=()=>S(null,null,function*(){try{x(!0),yield new Promise(t=>setTimeout(t,800));const s=a==="3m"?.5:a==="1y"?2:1;o(t=>w(b({},t),{overview:w(b({},t.overview),{totalRevenue:Math.round(t.overview.totalRevenue*s),monthlyAverage:Math.round(t.overview.monthlyAverage*s)})}))}catch(s){}finally{x(!1)}}),m=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s),f=s=>`${s>=0?"+":""}${s.toFixed(1)}%`,N=s=>s>=0?"text-green-600":"text-red-600",T=s=>s>=0?e.jsx(W,{size:16}):e.jsx(Y,{size:16}),P=s=>{switch(s){case"opportunity":return e.jsx(M,{className:"text-green-500",size:20});case"warning":return e.jsx(X,{className:"text-yellow-500",size:20});case"success":return e.jsx(q,{className:"text-blue-500",size:20});default:return e.jsx(D,{className:"text-gray-500",size:20})}},I=s=>{switch(s){case"opportunity":return"border-green-200 bg-green-50";case"warning":return"border-yellow-200 bg-yellow-50";case"success":return"border-blue-200 bg-blue-50";default:return"border-gray-200 bg-gray-50"}},F=s=>s>=80?"success":s>=60?"warning":"danger";return v?e.jsx(c,{className:n,children:e.jsx(l,{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Loading revenue analytics..."})]})})})}):e.jsxs("div",{className:`revenue-analytics space-y-6 ${n}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[e.jsx(z,{className:"text-blue-500",size:28}),"Revenue Analytics"]}),e.jsx("p",{className:"text-gray-600",children:"Comprehensive revenue performance and insights"})]}),e.jsxs(E,{selectedKeys:[a],onSelectionChange:s=>setPeriod&&setPeriod(Array.from(s)[0]),className:"w-32",size:"sm",children:[e.jsx(A,{children:"3 Months"},"3m"),e.jsx(A,{children:"6 Months"},"6m"),e.jsx(A,{children:"1 Year"},"1y")]})]}),e.jsx(c,{children:e.jsx(l,{className:"p-0",children:e.jsxs(G,{selectedKey:d,onSelectionChange:y,variant:"underlined",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-primary",tab:"max-w-fit px-6 py-4 h-12",tabContent:"group-data-[selected=true]:text-primary"},children:[e.jsx(g,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{size:18}),e.jsx("span",{children:"Overview"})]})},"overview"),e.jsx(g,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{size:18}),e.jsx("span",{children:"Performance"})]})},"performance"),e.jsx(g,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{size:18}),e.jsx("span",{children:"Trends"})]})},"trends"),e.jsx(g,{title:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(D,{size:18}),e.jsx("span",{children:"Insights"})]})},"insights")]})})}),d==="overview"&&e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsx(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:e.jsx(c,{children:e.jsxs(l,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),e.jsx(H,{className:"text-green-500",size:20})]}),e.jsx("div",{className:"text-2xl font-bold text-green-600 mb-2",children:m(i.overview.totalRevenue)}),e.jsxs("div",{className:`flex items-center gap-1 text-sm ${N(i.overview.growth)}`,children:[T(i.overview.growth),e.jsxs("span",{children:[f(i.overview.growth)," from last period"]})]})]})})}),e.jsx(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(c,{children:e.jsxs(l,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600",children:"Monthly Average"}),e.jsx(Q,{className:"text-blue-500",size:20})]}),e.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:m(i.overview.monthlyAverage)}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Best: ",i.overview.bestMonth," (",m(i.overview.bestMonthAmount),")"]})]})})}),e.jsx(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsx(c,{children:e.jsxs(l,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-600",children:"Projected Annual"}),e.jsx(_,{className:"text-purple-500",size:20})]}),e.jsx("div",{className:"text-2xl font-bold text-purple-600 mb-2",children:m(i.overview.projectedAnnual)}),e.jsx("div",{className:"text-sm text-gray-600",children:"Based on current trends"})]})})})]})}),d==="performance"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs(c,{children:[e.jsx(p,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Top Performing Projects"})}),e.jsx(l,{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:i.performance.topVentures.map((s,t)=>e.jsxs(u.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:t*.1},className:"flex items-center justify-between p-4 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsxs("span",{className:"text-blue-600 font-bold",children:["#",t+1]})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold",children:s.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[e.jsxs("span",{children:["Revenue: ",m(s.revenue)]}),e.jsxs("span",{children:["Share: ",s.share,"%"]})]})]})]}),e.jsxs("div",{className:`flex items-center gap-1 ${N(s.growth)}`,children:[T(s.growth),e.jsx("span",{className:"font-medium",children:f(s.growth)})]})]},t))})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(c,{children:[e.jsx(p,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Revenue Streams"})}),e.jsx(l,{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:Object.entries(i.performance.revenueStreams).map(([s,t])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("span",{className:"capitalize font-medium",children:s}),e.jsxs("div",{className:"text-sm text-gray-600",children:[t.percentage,"% of total"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold",children:m(t.amount)}),e.jsx("div",{className:`text-sm ${N(t.growth)}`,children:f(t.growth)})]})]},s))})})]}),e.jsxs(c,{children:[e.jsx(p,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Work Type Analysis"})}),e.jsx(l,{className:"p-6",children:e.jsx("div",{className:"space-y-4",children:Object.entries(i.performance.workTypes).map(([s,t])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("span",{className:"capitalize font-medium",children:s}),e.jsxs("div",{className:"text-sm text-gray-600",children:[t.hours,"h • ",t.percentage,"%"]})]}),e.jsx("div",{className:"font-bold",children:m(t.amount)})]},s))})})]})]})]}),d==="trends"&&e.jsx("div",{className:"space-y-6",children:e.jsxs(c,{children:[e.jsx(p,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Revenue Trends"})}),e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx(z,{size:48,className:"mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"Interactive trend charts coming soon!"}),e.jsx("p",{className:"text-sm",children:"Revenue trends and predictions over time"})]})})]})}),d==="insights"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs(c,{children:[e.jsx(p,{children:e.jsx("h3",{className:"text-lg font-semibold",children:"Performance Metrics"})}),e.jsx(l,{className:"p-6",children:e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Object.entries(i.insights.metrics).map(([s,t])=>e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold mb-2",children:[t,"%"]}),e.jsx(B,{value:t,color:F(t),size:"sm",className:"mb-2"}),e.jsx("div",{className:"text-sm text-gray-600 capitalize",children:s})]},s))})})]}),e.jsx("div",{className:"space-y-4",children:i.insights.recommendations.map((s,t)=>e.jsx(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.1},children:e.jsx(c,{className:`border ${I(s.type)}`,children:e.jsx(l,{className:"p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[P(s.type),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("h4",{className:"font-semibold",children:s.title}),e.jsxs(J,{color:s.impact==="high"?"danger":s.impact==="medium"?"warning":"default",size:"sm",children:[s.impact," impact"]})]}),e.jsx("p",{className:"text-gray-600 mb-3",children:s.description}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-blue-600",children:s.action}),e.jsx(K,{size:"sm",variant:"light",color:"primary",children:"Learn More"})]})]})]})})})},t))})]})]})};export{ce as default};
