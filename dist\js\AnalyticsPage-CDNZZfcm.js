import{r as s,j as e}from"./chunk-DX4Z_LyS.js";import{U as r}from"../assets/main-CGUKzV0x.js";import l from"./AnalyticsDashboard-Bk14rg7g.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";import"./chunk-BiNxGM8y.js";import"./chunk-Crqd8jVX.js";const h=()=>{const{currentUser:t}=s.useContext(r);return t?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900",children:e.jsxs("div",{className:"flex min-h-screen",children:[e.jsxs("div",{className:"w-16 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-4 space-y-4",children:[e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Notifications",children:"🔔"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Messages",children:"📧"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Quick Tasks",children:"📋"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Social Network",children:"👥"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Settings",children:"⚙️"})]}),e.jsx("div",{className:"flex-1 p-6",children:e.jsx(l,{})}),e.jsxs("div",{className:"w-16 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-4 space-y-4",children:[e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Custom Reports",children:"📊"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Data Export",children:"📤"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Forecasting",children:"📈"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Alert Settings",children:"🔔"}),e.jsx("button",{className:"p-3 rounded-lg hover:bg-default-100 transition-colors",title:"Analytics Settings",children:"⚙️"})]})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Authentication Required"}),e.jsx("p",{className:"text-default-600",children:"Please log in to access the Analytics Dashboard"})]})})};export{h as default};
