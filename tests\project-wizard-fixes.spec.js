import { test, expect } from '@playwright/test';

test.describe('Project Wizard UX Fixes Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Studio Creation Page - Styling and Functionality', async ({ page }) => {
    console.log('Testing Studio Creation Page...');
    
    // Navigate to studio creation
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for visual verification
    await page.screenshot({ path: 'test-results/studio-creation-page.png', fullPage: true });
    
    // Verify form elements are properly styled and functional
    const nameInput = page.locator('input[placeholder="Enter your studio name"]').first();
    await expect(nameInput).toBeVisible();
    await nameInput.fill('Test Studio Name');

    const descriptionTextarea = page.locator('textarea').first();
    await expect(descriptionTextarea).toBeVisible();
    await descriptionTextarea.fill('Test studio description for verification');

    const industryInput = page.locator('input[placeholder*="Software Development"]').first();
    await expect(industryInput).toBeVisible();
    await industryInput.fill('Software Development');
    
    // Test dropdown selections - look for Select components
    const studioTypeSelect = page.locator('button[aria-haspopup="listbox"]').first();
    if (await studioTypeSelect.isVisible()) {
      await studioTypeSelect.click();
      await page.waitForTimeout(1000);
      console.log('✓ Studio type dropdown is functional');
    } else {
      console.log('⚠ Studio type dropdown not found, checking alternative selectors');
    }
    
    // Select an option
    const emergingOption = page.locator('li[data-key="emerging"]').first();
    if (await emergingOption.isVisible()) {
      await emergingOption.click();
    }
    
    console.log('✓ Studio creation form elements are functional');
  });

  test('Project Wizard - Model Configuration Fixes', async ({ page }) => {
    console.log('Testing Project Wizard Model Configuration...');
    
    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Fill basic project info to get to model configuration
    await page.fill('input[placeholder="Enter project name"]', 'Test Project for Model Config');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project description');
    
    // Navigate to model configuration step
    const nextButton = page.locator('button:has-text("Next")').first();
    if (await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(2000);
    }
    
    // Continue to royalty model step
    for (let i = 0; i < 3; i++) {
      // Wait for any toast notifications to disappear
      await page.waitForTimeout(1000);

      // Wait for toast to disappear if present
      const toast = page.locator('[role="status"], .go3958317564, .go2072408551').first();
      if (await toast.isVisible()) {
        await toast.waitFor({ state: 'hidden', timeout: 5000 });
      }

      const continueBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await continueBtn.isVisible()) {
        await continueBtn.click();
        await page.waitForTimeout(2000);
      }
    }
    
    // Test weight distribution sliders
    const taskSlider = page.locator('[data-slot="track"]').first();
    if (await taskSlider.isVisible()) {
      console.log('✓ Weight distribution sliders are visible');
      
      // Take screenshot of model configuration
      await page.screenshot({ path: 'test-results/model-configuration.png', fullPage: true });
    }
    
    // Test financial parameter inputs
    const revenueShareInput = page.locator('input[type="number"]').first();
    if (await revenueShareInput.isVisible()) {
      await revenueShareInput.clear();
      await revenueShareInput.fill('75');
      console.log('✓ Financial parameter inputs are functional');
    }
    
    // Verify algorithm information visibility
    const algorithmInfo = page.locator('text=Algorithm Information');
    if (await algorithmInfo.isVisible()) {
      console.log('✓ Algorithm information is visible');
    }
  });

  test('Revenue Tranches - Date Picker and Distribution Fixes', async ({ page }) => {
    console.log('Testing Revenue Tranches Configuration...');
    
    // Navigate to project creation and get to revenue tranches step
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Fill basic info and navigate to revenue tranches
    await page.fill('input[placeholder="Enter project name"]', 'Test Project for Revenue Tranches');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project description');
    
    // Navigate through wizard steps to reach revenue tranches
    for (let i = 0; i < 5; i++) {
      // Wait for any toast notifications to disappear
      await page.waitForTimeout(1000);

      // Wait for toast to disappear if present
      const toast = page.locator('[role="status"], .go3958317564, .go2072408551').first();
      if (await toast.isVisible()) {
        await toast.waitFor({ state: 'hidden', timeout: 5000 });
      }

      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click();
        await page.waitForTimeout(2000);
      }
    }
    
    // Test date picker functionality
    const dateInputs = page.locator('.react-datepicker__input-container input');
    const dateInputCount = await dateInputs.count();
    
    if (dateInputCount > 0) {
      console.log(`✓ Found ${dateInputCount} date picker inputs`);
      
      // Test first date picker
      const firstDateInput = dateInputs.first();
      await firstDateInput.click();
      await page.waitForTimeout(1000);
      
      // Check if date picker calendar is visible
      const datePicker = page.locator('.react-datepicker');
      if (await datePicker.isVisible()) {
        console.log('✓ Date picker calendar opens successfully');
        
        // Select a date
        const todayDate = page.locator('.react-datepicker__day--today').first();
        if (await todayDate.isVisible()) {
          await todayDate.click();
          console.log('✓ Date selection works');
        }
      }
    }
    
    // Test distribution thresholds for infinite values
    const thresholdInputs = page.locator('input[placeholder*="limit"], input[placeholder*="threshold"]');
    const thresholdCount = await thresholdInputs.count();
    
    if (thresholdCount > 0) {
      console.log(`✓ Found ${thresholdCount} distribution threshold inputs`);
      
      // Test input functionality
      const firstThreshold = thresholdInputs.first();
      await firstThreshold.fill('1000');
      
      const inputValue = await firstThreshold.inputValue();
      if (inputValue !== 'Infinity' && inputValue !== '∞') {
        console.log('✓ Distribution thresholds do not show infinite values');
      }
    }
    
    // Take screenshot of revenue tranches
    await page.screenshot({ path: 'test-results/revenue-tranches.png', fullPage: true });
  });

  test('Milestones - Date Picker Integration', async ({ page }) => {
    console.log('Testing Milestones Date Picker Integration...');
    
    // Navigate to project creation and get to milestones step
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Fill basic info and navigate to milestones
    await page.fill('input[placeholder="Enter project name"]', 'Test Project for Milestones');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project description');
    
    // Navigate through wizard steps to reach milestones
    for (let i = 0; i < 6; i++) {
      // Wait for any toast notifications to disappear
      await page.waitForTimeout(1000);

      // Wait for toast to disappear if present
      const toast = page.locator('[role="status"], .go3958317564, .go2072408551').first();
      if (await toast.isVisible()) {
        await toast.waitFor({ state: 'hidden', timeout: 5000 });
      }

      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click();
        await page.waitForTimeout(2000);
      }
    }
    
    // Test milestone creation with date picker
    const addMilestoneBtn = page.locator('button:has-text("Add Milestone"), button:has-text("Create Milestone")').first();
    if (await addMilestoneBtn.isVisible()) {
      await addMilestoneBtn.click();
      await page.waitForTimeout(1000);
      
      // Fill milestone details
      const milestoneNameInput = page.locator('input[placeholder*="milestone"], input[id*="milestone"]').first();
      if (await milestoneNameInput.isVisible()) {
        await milestoneNameInput.fill('Test Milestone');
      }
      
      // Test milestone date picker
      const milestoneDateInput = page.locator('.react-datepicker__input-container input').first();
      if (await milestoneDateInput.isVisible()) {
        await milestoneDateInput.click();
        await page.waitForTimeout(1000);
        
        const datePicker = page.locator('.react-datepicker');
        if (await datePicker.isVisible()) {
          console.log('✓ Milestone date picker opens successfully');
          
          // Select a future date
          const futureDate = page.locator('.react-datepicker__day:not(.react-datepicker__day--disabled)').first();
          if (await futureDate.isVisible()) {
            await futureDate.click();
            console.log('✓ Milestone date selection works');
          }
        }
      }
    }
    
    // Take screenshot of milestones
    await page.screenshot({ path: 'test-results/milestones.png', fullPage: true });
  });

  test('Overall UX Quality Assessment', async ({ page }) => {
    console.log('Performing Overall UX Quality Assessment...');
    
    // Test studio creation page
    await page.goto('https://royalty.technology/studios/create');
    await page.waitForLoadState('networkidle');
    
    let uxScore = 0;
    let totalChecks = 0;
    
    // Check for proper styling
    const styledElements = await page.locator('[class*="heroui"], [class*="nextui"]').count();
    if (styledElements > 0) {
      uxScore += 20;
      console.log('✓ HeroUI styling is applied');
    }
    totalChecks += 20;
    
    // Check for functional form elements
    const functionalInputs = await page.locator('input:not([disabled]), textarea:not([disabled])').count();
    if (functionalInputs >= 3) {
      uxScore += 20;
      console.log('✓ Form elements are functional');
    }
    totalChecks += 20;
    
    // Test project wizard
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Check for date picker CSS
    const datePickerStyles = await page.locator('.react-datepicker, .react-datepicker-wrapper').count();
    if (datePickerStyles > 0) {
      uxScore += 20;
      console.log('✓ Date picker styling is loaded');
    }
    totalChecks += 20;
    
    // Check for no error messages or broken elements
    const errorElements = await page.locator('text=Error, text=undefined, text=null, text=Infinity').count();
    if (errorElements === 0) {
      uxScore += 20;
      console.log('✓ No error messages or broken elements found');
    }
    totalChecks += 20;
    
    // Check for responsive design
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    
    const responsiveElements = await page.locator('[class*="responsive"], [class*="md:"], [class*="lg:"]').count();
    if (responsiveElements > 0) {
      uxScore += 20;
      console.log('✓ Responsive design elements present');
    }
    totalChecks += 20;
    
    const finalScore = Math.round((uxScore / totalChecks) * 100);
    console.log(`\n🎯 Overall UX Quality Score: ${finalScore}/100`);
    
    if (finalScore >= 80) {
      console.log('✅ EXCELLENT - Project wizard is production ready!');
    } else if (finalScore >= 60) {
      console.log('⚠️  GOOD - Minor improvements needed');
    } else {
      console.log('❌ NEEDS WORK - Significant issues remain');
    }
    
    // Take final screenshot
    await page.screenshot({ path: 'test-results/final-ux-assessment.png', fullPage: true });
  });
});
