var ye=Object.defineProperty,we=Object.defineProperties;var ve=Object.getOwnPropertyDescriptors;var te=Object.getOwnPropertySymbols;var xe=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable;var ue=(i,s,a)=>s in i?ye(i,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[s]=a,$=(i,s)=>{for(var a in s||(s={}))xe.call(s,a)&&ue(i,a,s[a]);if(te)for(var a of te(s))ge.call(s,a)&&ue(i,a,s[a]);return i},K=(i,s)=>we(i,ve(s));var ne=(i,s)=>{var a={};for(var r in i)xe.call(i,r)&&s.indexOf(r)<0&&(a[r]=i[r]);if(i!=null&&te)for(var r of te(i))s.indexOf(r)<0&&ge.call(i,r)&&(a[r]=i[r]);return a};var Q=(i,s,a)=>new Promise((r,t)=>{var m=p=>{try{o(a.next(p))}catch(x){t(x)}},g=p=>{try{o(a.throw(p))}catch(x){t(x)}},o=p=>p.done?r(p.value):Promise.resolve(p.value).then(m,g);o((a=a.apply(i,s)).next())});import{r as c,j as e,c as M,b as L,m as z,p as ae,F as je,u as le,v as W,h as q,e as X,y as G,A as de,C as ke,l as Ne,n as Se,o as Ce,E as _e,$ as Te,w as re,S as De,T as Pe}from"./chunk-DX4Z_LyS.js";import{U as ie,s as J,u as $e,C as Ae,A as pe,I as Ee}from"../assets/main-CGUKzV0x.js";import{c as A}from"./chunk-D8IZ3rty.js";import{a as Re}from"./chunk-BV1TipCO.js";import"./chunk-Cai8ouo_.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const Ie=({projectId:i=null,className:s=""})=>{const{currentUser:a}=c.useContext(ie),[r,t]=c.useState(!1),[m,g]=c.useState(null),[o,p]=c.useState(0),[x,y]=c.useState(""),[C,n]=c.useState(""),[l,j]=c.useState("3"),[S,h]=c.useState([]),[_,v]=c.useState(0);c.useEffect(()=>{let d=null;return r&&m?d=setInterval(()=>{p(Date.now()-m)},1e3):r||clearInterval(d),()=>clearInterval(d)},[r,m]),c.useEffect(()=>{a&&k()},[a]);const k=()=>Q(null,null,function*(){if(a)try{const d=new Date().toISOString().split("T")[0],{data:u,error:R}=yield J.from("contributions").select("*").eq("user_id",a.id).gte("created_at",`${d}T00:00:00`).lt("created_at",`${d}T23:59:59`).order("created_at",{ascending:!1});if(R)throw R;h(u||[]);const T=(u||[]).reduce((D,f)=>D+(f.hours_tracked||0),0);v(T)}catch(d){}}),P=()=>{if(!x.trim()){A.error("Please enter a task description");return}t(!0),g(Date.now()),p(0),A.success("Time tracking started")},N=()=>Q(null,null,function*(){if(!r||!m)return;const d=Date.now()-m,u=d/(1e3*60*60);t(!1),p(d),yield B(u)}),B=d=>Q(null,null,function*(){if(!a){A.error("You must be logged in to save contributions");return}try{const u={user_id:a.id,project_id:i,task_description:x,description:C||x,hours_tracked:d,difficulty_rating:parseInt(l),contribution_type:"time_tracking",status:"pending",created_at:new Date().toISOString()},{data:R,error:T}=yield J.from("contributions").insert([u]).select().single();if(T)throw T;A.success(`Saved ${F(o)} of work`),y(""),n(""),p(0),g(null),k()}catch(u){A.error("Failed to save contribution")}}),F=d=>{const u=Math.floor(d/1e3),R=Math.floor(u/3600),T=Math.floor(u%3600/60),D=u%60;return R>0?`${R}h ${T}m ${D}s`:T>0?`${T}m ${D}s`:`${D}s`},E=d=>({1:"success",2:"success",3:"warning",4:"danger",5:"danger"})[d]||"default";return e.jsxs("div",{className:`space-y-6 ${s}`,children:[e.jsx(M,{className:"bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(z.div,{className:"text-4xl font-mono font-bold text-white mb-2",animate:r?{scale:[1,1.05,1]}:{},transition:{duration:1,repeat:r?1/0:0},children:F(o)}),e.jsx("div",{className:"text-white/70",children:r?"Currently tracking":"Ready to track"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(ae,{label:"What are you working on?",placeholder:"e.g., Implementing user authentication",value:x,onChange:d=>y(d.target.value),disabled:r,className:"text-white"}),e.jsx(je,{label:"Additional details (optional)",placeholder:"Any additional context about this task...",value:C,onChange:d=>n(d.target.value),disabled:r,rows:2}),e.jsxs(le,{label:"Difficulty Level",selectedKeys:[l],onSelectionChange:d=>j(Array.from(d)[0]),disabled:r,children:[e.jsx(W,{value:"1",children:"1 - Very Easy"},"1"),e.jsx(W,{value:"2",children:"2 - Easy"},"2"),e.jsx(W,{value:"3",children:"3 - Medium"},"3"),e.jsx(W,{value:"4",children:"4 - Hard"},"4"),e.jsx(W,{value:"5",children:"5 - Very Hard"},"5")]}),e.jsx("div",{className:"flex gap-3 justify-center",children:r?e.jsx(q,{onClick:N,className:"bg-red-500 hover:bg-red-600 text-white px-8",size:"lg",children:"⏹️ Stop & Save"}):e.jsx(q,{onClick:P,className:"bg-green-500 hover:bg-green-600 text-white px-8",size:"lg",children:"▶️ Start Tracking"})})]})]})}),e.jsx(M,{className:"bg-white/5 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Today's Progress"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:F(_*60*60*1e3)}),e.jsx("div",{className:"text-white/70 text-sm",children:"Total Time"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:S.length}),e.jsx("div",{className:"text-white/70 text-sm",children:"Tasks Completed"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Recent Entries"}),S.length===0?e.jsx("div",{className:"text-white/50 text-center py-4",children:"No entries today. Start tracking to see your progress!"}):e.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:S.slice(0,5).map((d,u)=>e.jsxs(z.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:u*.1},className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-white text-sm font-medium",children:d.task_description}),e.jsx("div",{className:"text-white/60 text-xs",children:new Date(d.created_at).toLocaleTimeString()})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(X,{size:"sm",color:E(d.difficulty_rating),variant:"flat",children:["D",d.difficulty_rating]}),e.jsx("div",{className:"text-white/80 text-sm font-mono",children:F((d.hours_tracked||0)*60*60*1e3)})]})]},d.id))})]})]})})]})},ze=({projectId:i=null,onSubmit:s,className:a=""})=>{const{currentUser:r}=c.useContext(ie),[t,m]=c.useState({task_description:"",description:"",hours_tracked:"",difficulty_rating:"3",contribution_type:"development",tags:""}),[g,o]=c.useState(!1),p=[{key:"development",label:"💻 Development",description:"Code, programming, technical work"},{key:"design",label:"🎨 Design",description:"UI/UX, graphics, visual assets"},{key:"content",label:"📝 Content",description:"Writing, documentation, copy"},{key:"testing",label:"🧪 Testing",description:"QA, bug testing, user testing"},{key:"research",label:"🔬 Research",description:"Market research, user research"},{key:"management",label:"📋 Management",description:"Project management, coordination"},{key:"marketing",label:"📢 Marketing",description:"Promotion, social media, outreach"},{key:"other",label:"🔧 Other",description:"Other types of contributions"}],x=n=>Q(null,null,function*(){if(n.preventDefault(),!r){A.error("You must be logged in to submit contributions");return}if(!t.task_description.trim()){A.error("Task description is required");return}if(!t.hours_tracked||parseFloat(t.hours_tracked)<=0){A.error("Please enter the time spent (hours)");return}o(!0);try{const l={user_id:r.id,project_id:i,task_description:t.task_description.trim(),description:t.description.trim()||t.task_description.trim(),hours_tracked:parseFloat(t.hours_tracked),difficulty_rating:parseInt(t.difficulty_rating),contribution_type:t.contribution_type,status:"pending",tags:t.tags.trim()?t.tags.split(",").map(h=>h.trim()):[],created_at:new Date().toISOString()},{data:j,error:S}=yield J.from("contributions").insert([l]).select().single();if(S)throw S;A.success("Contribution submitted successfully!"),m({task_description:"",description:"",hours_tracked:"",difficulty_rating:"3",contribution_type:"development",tags:""}),s&&s(j)}catch(l){A.error("Failed to submit contribution")}finally{o(!1)}}),y=n=>({1:"success",2:"success",3:"warning",4:"danger",5:"danger"})[n]||"default",C=n=>p.find(l=>l.key===n)||p[0];return e.jsx(M,{className:`bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-white/10 ${a}`,children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Submit Contribution"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Log completed work and track your contributions to the project"})]}),e.jsxs("form",{onSubmit:x,className:"space-y-4",children:[e.jsx(ae,{label:"What did you work on?",placeholder:"e.g., Fixed user authentication bug",value:t.task_description,onChange:n=>m(K($({},t),{task_description:n.target.value})),required:!0,className:"text-white"}),e.jsx(je,{label:"Additional details (optional)",placeholder:"Provide more context about your contribution...",value:t.description,onChange:n=>m(K($({},t),{description:n.target.value})),rows:3}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(ae,{type:"number",label:"Time Spent (hours)",placeholder:"2.5",step:"0.25",min:"0.25",max:"24",value:t.hours_tracked,onChange:n=>m(K($({},t),{hours_tracked:n.target.value})),required:!0}),e.jsxs(le,{label:"Difficulty Level",selectedKeys:[t.difficulty_rating],onSelectionChange:n=>m(K($({},t),{difficulty_rating:Array.from(n)[0]})),children:[e.jsx(W,{value:"1",children:"1 - Very Easy"},"1"),e.jsx(W,{value:"2",children:"2 - Easy"},"2"),e.jsx(W,{value:"3",children:"3 - Medium"},"3"),e.jsx(W,{value:"4",children:"4 - Hard"},"4"),e.jsx(W,{value:"5",children:"5 - Very Hard"},"5")]})]}),e.jsx(le,{label:"Contribution Type",selectedKeys:[t.contribution_type],onSelectionChange:n=>m(K($({},t),{contribution_type:Array.from(n)[0]})),children:p.map(n=>e.jsx(W,{value:n.key,children:n.label},n.key))}),e.jsx(ae,{label:"Tags (optional)",placeholder:"frontend, bug-fix, authentication",value:t.tags,onChange:n=>m(K($({},t),{tags:n.target.value})),description:"Separate tags with commas"}),e.jsxs(z.div,{className:"p-4 bg-white/5 rounded-lg border border-white/10",initial:{opacity:0},animate:{opacity:1},children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Preview"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-white/70 text-sm",children:"Type:"}),e.jsx("span",{className:"text-white text-sm",children:C(t.contribution_type).label})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-white/70 text-sm",children:"Time:"}),e.jsxs("span",{className:"text-white text-sm",children:[t.hours_tracked||"0"," hours"]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-white/70 text-sm",children:"Difficulty:"}),e.jsxs(X,{size:"sm",color:y(t.difficulty_rating),variant:"flat",children:["Level ",t.difficulty_rating]})]}),t.tags&&e.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[e.jsx("span",{className:"text-white/70 text-sm",children:"Tags:"}),t.tags.split(",").map((n,l)=>e.jsx(X,{size:"sm",variant:"flat",className:"bg-blue-500/20 text-blue-200",children:n.trim()},l))]})]})]}),e.jsx(q,{type:"submit",className:"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white",size:"lg",isLoading:g,disabled:g,children:g?"Submitting...":"📤 Submit Contribution"})]})]})})},ce=({projectId:i=null,className:s="",refreshTrigger:a=0})=>{const{currentUser:r}=c.useContext(ie),[t,m]=c.useState({today:{hours:0,tasks:0},week:{hours:0,tasks:0},month:{hours:0,tasks:0},total:{hours:0,tasks:0}}),[g,o]=c.useState([]),[p,x]=c.useState(!0),y={dailyHours:4,weeklyHours:20,dailyTasks:3,weeklyTasks:15};c.useEffect(()=>{r&&C()},[r,i,a]);const C=()=>Q(null,null,function*(){if(r)try{x(!0);const h=new Date,_=new Date(h.getFullYear(),h.getMonth(),h.getDate()),v=new Date(_);v.setDate(_.getDate()-_.getDay());const k=new Date(h.getFullYear(),h.getMonth(),1);let P=J.from("contributions").select("*").eq("user_id",r.id).eq("status","approved");i&&(P=P.eq("project_id",i));const{data:N,error:B}=yield P;if(B)throw B;const F={today:n(N,_),week:n(N,v),month:n(N,k),total:n(N,new Date(0))};m(F);const E=N.sort((d,u)=>new Date(u.created_at)-new Date(d.created_at)).slice(0,10);o(E)}catch(h){}finally{x(!1)}}),n=(h,_)=>{const v=h.filter(k=>new Date(k.created_at)>=_);return{hours:v.reduce((k,P)=>k+(P.hours_tracked||0),0),tasks:v.length}},l=h=>h<1?`${Math.round(h*60)}m`:`${h.toFixed(1)}h`,j=(h,_)=>{const v=h/_*100;return v>=100?"success":v>=75?"warning":"primary"},S=h=>({development:"💻",design:"🎨",content:"📝",testing:"🧪",research:"🔬",management:"📋",marketing:"📢",time_tracking:"⏱️",other:"🔧"})[h]||"📝";return p?e.jsx("div",{className:`space-y-6 ${s}`,children:e.jsx(M,{className:"bg-white/5 border border-white/10",children:e.jsx(L,{className:"p-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-6 bg-white/10 rounded w-1/3"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"h-4 bg-white/10 rounded"}),e.jsx("div",{className:"h-4 bg-white/10 rounded w-2/3"})]})]})})})}):e.jsxs("div",{className:`space-y-6 ${s}`,children:[e.jsx(M,{className:"bg-gradient-to-br from-green-500/10 to-blue-500/10 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-6",children:"Your Progress"}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Today"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"text-white/70",children:"Hours"}),e.jsxs("span",{className:"text-white",children:[l(t.today.hours)," / ",l(y.dailyHours)]})]}),e.jsx(G,{value:t.today.hours/y.dailyHours*100,color:j(t.today.hours,y.dailyHours),className:"max-w-full"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"text-white/70",children:"Tasks"}),e.jsxs("span",{className:"text-white",children:[t.today.tasks," / ",y.dailyTasks]})]}),e.jsx(G,{value:t.today.tasks/y.dailyTasks*100,color:j(t.today.tasks,y.dailyTasks),className:"max-w-full"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"This Week"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"text-white/70",children:"Hours"}),e.jsxs("span",{className:"text-white",children:[l(t.week.hours)," / ",l(y.weeklyHours)]})]}),e.jsx(G,{value:t.week.hours/y.weeklyHours*100,color:j(t.week.hours,y.weeklyHours),className:"max-w-full"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"text-white/70",children:"Tasks"}),e.jsxs("span",{className:"text-white",children:[t.week.tasks," / ",y.weeklyTasks]})]}),e.jsx(G,{value:t.week.tasks/y.weeklyTasks*100,color:j(t.week.tasks,y.weeklyTasks),className:"max-w-full"})]})]})]})]}),e.jsx("div",{className:"mt-6 pt-6 border-t border-white/10",children:e.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:l(t.month.hours)}),e.jsx("div",{className:"text-white/70 text-sm",children:"This Month"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:t.total.tasks}),e.jsx("div",{className:"text-white/70 text-sm",children:"Total Tasks"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-purple-400",children:l(t.total.hours)}),e.jsx("div",{className:"text-white/70 text-sm",children:"Total Hours"})]})]})})]})}),e.jsx(M,{className:"bg-white/5 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Recent Activity"}),g.length===0?e.jsx("div",{className:"text-white/50 text-center py-8",children:"No contributions yet. Start tracking your work to see activity here!"}):e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:g.map((h,_)=>e.jsxs(z.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:_*.05},className:"flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"text-xl",children:S(h.contribution_type)}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white text-sm font-medium",children:h.task_description}),e.jsxs("div",{className:"text-white/60 text-xs",children:[new Date(h.created_at).toLocaleDateString()," • ",l(h.hours_tracked)]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(X,{size:"sm",color:h.status==="approved"?"success":"warning",variant:"flat",children:h.status})})]},h.id))})]})})]})},Me=({currentJourney:i="start",currentStep:s=1,totalSteps:a=3,nextSteps:r=[],className:t=""})=>{const m={start:{title:"Start Your Journey",icon:"🚀",color:"primary",steps:["Choose Your Path","Create Project","Set Up Team"]},track:{title:"Track Your Work",icon:"📊",color:"secondary",steps:["Log Contributions","Track Time","Monitor Progress"]},earn:{title:"Earn & Analyze",icon:"💰",color:"success",steps:["View Earnings","Analyze Performance","Claim Rewards"]}},g=m[i]||m.start,o=s/a*100;return e.jsx(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`w-full ${t}`,children:e.jsx(M,{className:"bg-gradient-to-r from-background/50 to-background/80 backdrop-blur-sm border border-divider/50",children:e.jsxs(L,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"text-2xl",children:g.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-foreground",children:g.title}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Step ",s," of ",a]})]}),e.jsx("div",{className:"ml-auto",children:e.jsxs(X,{color:g.color,variant:"flat",size:"sm",children:[Math.round(o),"% Complete"]})})]}),e.jsx(G,{value:o,color:g.color,className:"mb-4",size:"sm"}),e.jsx("div",{className:"flex items-center gap-2 mb-4",children:g.steps.map((p,x)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${x+1<s?"bg-success text-success-foreground":x+1===s?`bg-${g.color} text-${g.color}-foreground`:"bg-default-100 text-default-500"}`,children:x+1<s?"✓":x+1}),x<g.steps.length-1&&e.jsx("div",{className:`w-8 h-0.5 mx-1 transition-colors ${x+1<s?"bg-success":"bg-default-200"}`})]},x))}),e.jsx("div",{className:"grid grid-cols-3 gap-2 mb-4",children:g.steps.map((p,x)=>e.jsx("div",{className:`text-xs text-center transition-colors ${x+1===s?"text-foreground font-medium":x+1<s?"text-success":"text-muted-foreground"}`,children:p},x))}),r.length>0&&e.jsxs("div",{className:"border-t border-divider pt-4",children:[e.jsx("h4",{className:"text-sm font-medium text-foreground mb-2",children:"Next Steps:"}),e.jsx("ul",{className:"space-y-1",children:r.map((p,x)=>e.jsxs("li",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[e.jsx("div",{className:"w-1.5 h-1.5 rounded-full bg-primary"}),p]},x))})]})]})})})},Le=({context:i="general",showTour:s=!1,onTourComplete:a,className:r=""})=>{const[t,m]=c.useState(0),[g,o]=c.useState(s),[p,x]=c.useState(!1),y=P=>{const N={general:{title:"Getting Started",description:"Welcome to Royaltea! Here's how to get started with tracking your work and earning royalties.",tips:["Use the Start journey to create your first project","Track your contributions in the Track section","Monitor your earnings in the Earn section","Use keyboard shortcuts for faster navigation"]},track:{title:"Track Your Work",description:"Learn how to effectively track your contributions and manage your work.",tips:["Use the time tracker for accurate hour logging","Submit contributions with detailed descriptions","Set appropriate difficulty ratings for fair royalty calculations","Review your progress regularly to stay motivated"]},earn:{title:"Understanding Earnings",description:"Learn how royalties work and how to maximize your earnings.",tips:["Earnings are calculated based on contribution hours and difficulty","Funds are held in escrow until project milestones are met","Higher difficulty contributions earn more royalties","Consistent contributions build long-term value"]},project:{title:"Project Management",description:"Manage your projects effectively with these features.",tips:["Use milestones to track project progress","Drag and drop tasks to update their status","Invite collaborators to work together","Monitor project analytics for insights"]}};return N[P]||N.general},C=P=>({track:[{target:'[data-tour="time-tracker"]',title:"Time Tracker",content:"Start here to track time as you work. Click the play button to begin timing your session.",position:"bottom"},{target:'[data-tour="submit-form"]',title:"Submit Contributions",content:"Use this form to log completed work. Include detailed descriptions for better tracking.",position:"left"},{target:'[data-tour="progress-view"]',title:"Progress Overview",content:"Monitor your contribution statistics and track your productivity over time.",position:"top"},{target:'[data-tour="analytics"]',title:"Analytics Dashboard",content:"Analyze your work patterns and performance with interactive charts and insights.",position:"top"}],earn:[{target:'[data-tour="earnings-cards"]',title:"Earnings Overview",content:"These cards show your projected, escrow, released, and pending earnings.",position:"bottom"},{target:'[data-tour="earnings-breakdown"]',title:"Earnings Breakdown",content:"See how your earnings are distributed across different categories.",position:"right"},{target:'[data-tour="contribution-value"]',title:"Contribution Value",content:"Each contribution shows its calculated value based on hours and difficulty.",position:"left"}],project:[{target:'[data-tour="project-metrics"]',title:"Project Metrics",content:"Key project statistics including contributions, hours, and completion percentage.",position:"bottom"},{target:'[data-tour="milestone-tracker"]',title:"Milestone Tracking",content:"Create and track project milestones to measure progress.",position:"bottom"},{target:'[data-tour="task-board"]',title:"Task Management",content:"Drag and drop tasks between columns to update their status.",position:"top"}]})[P]||[],n=y(i),l=C(i),j=()=>{t<l.length-1?m(t+1):h()},S=()=>{t>0&&m(t-1)},h=()=>{o(!1),m(0),a==null||a()},_=()=>{o(!1),m(0)},v=P=>{const N=document.querySelector(P);N&&(N.scrollIntoView({behavior:"smooth",block:"center"}),N.classList.add("tour-highlight"),setTimeout(()=>{N.classList.remove("tour-highlight")},3e3))};c.useEffect(()=>{g&&l[t]&&v(l[t].target)},[t,g]);const k=F=>{var E=F,{children:P,content:N}=E,B=ne(E,["children","content"]);return e.jsx(Te,K($({content:N,placement:"top",className:"max-w-xs"},B),{children:P}))};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`fixed bottom-4 right-4 z-50 ${r}`,children:e.jsx(k,{content:"Get help and start a guided tour",children:e.jsx(q,{isIconOnly:!0,color:"primary",variant:"shadow",onClick:()=>x(!0),className:"w-12 h-12",children:"❓"})})}),e.jsx(de,{children:g&&l[t]&&e.jsx(z.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 bg-black/50 flex items-center justify-center",children:e.jsxs(z.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-background border border-divider rounded-lg p-6 max-w-md mx-4 shadow-xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:l[t].title}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[t+1," / ",l.length]})]}),e.jsx("p",{className:"text-muted-foreground mb-6",children:l[t].content}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(q,{variant:"light",onClick:_,size:"sm",children:"Skip Tour"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(q,{variant:"flat",onClick:S,disabled:t===0,size:"sm",children:"Previous"}),e.jsx(q,{color:"primary",onClick:j,size:"sm",children:t===l.length-1?"Finish":"Next"})]})]})]})})}),e.jsx(ke,{isOpen:p,onClose:()=>x(!1),size:"lg",children:e.jsxs(Ne,{children:[e.jsx(Se,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"❓"}),n.title]})}),e.jsx(Ce,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-muted-foreground",children:n.description}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-3",children:"Tips & Tricks:"}),e.jsx("ul",{className:"space-y-2",children:n.tips.map((P,N)=>e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-primary mt-1",children:"•"}),e.jsx("span",{className:"text-sm",children:P})]},N))})]}),l.length>0&&e.jsxs("div",{className:"border-t border-divider pt-4",children:[e.jsx("h4",{className:"font-medium mb-2",children:"Interactive Tour Available"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Take a guided tour to learn about the features on this page."}),e.jsx(q,{color:"primary",onClick:()=>{x(!1),o(!0),m(0)},size:"sm",children:"Start Tour"})]})]})}),e.jsx(_e,{children:e.jsx(q,{variant:"light",onClick:()=>x(!1),children:"Close"})})]})}),e.jsx("style",{jsx:!0,global:!0,children:`
        .tour-highlight {
          position: relative;
          z-index: 1000;
        }
        
        .tour-highlight::before {
          content: '';
          position: absolute;
          inset: -4px;
          border: 2px solid #3b82f6;
          border-radius: 8px;
          background: rgba(59, 130, 246, 0.1);
          animation: pulse 2s infinite;
          pointer-events: none;
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `})]})},He=({onNavigateToTab:i,onOpenSearch:s,onToggleFilters:a,onCreateNew:r,currentCanvas:t=null})=>{const m=Re();c.useCallback(o=>{if(o.target.tagName==="INPUT"||o.target.tagName==="TEXTAREA"||o.target.contentEditable==="true")return;const{key:p,ctrlKey:x,metaKey:y,altKey:C,shiftKey:n}=o,l=x||y;if(l)switch(p){case"1":o.preventDefault(),m("/start"),A.success("Navigated to Start");break;case"2":o.preventDefault(),m("/contributions"),A.success("Navigated to Track");break;case"3":o.preventDefault(),m("/earn"),A.success("Navigated to Earn");break;case"k":o.preventDefault(),s==null||s();break;case"f":o.preventDefault(),a==null||a();break;case"n":o.preventDefault(),r==null||r();break;case"/":o.preventDefault(),s==null||s();break}if(!l)switch(p){case"1":case"2":case"3":case"4":case"5":if(i){o.preventDefault();const j=parseInt(p)-1;i(j)}break;case"Escape":o.preventDefault(),document.dispatchEvent(new CustomEvent("closeModals"));break;case"?":o.preventDefault(),g();break}if(C&&!l)switch(p){case"t":o.preventDefault(),t==="contributions"&&(i==null||i(0),A.success("Switched to Time Tracker"));break;case"s":o.preventDefault(),t==="contributions"&&(i==null||i(1),A.success("Switched to Submit Work"));break;case"p":o.preventDefault(),t==="contributions"&&(i==null||i(2),A.success("Switched to Progress"));break;case"a":o.preventDefault(),t==="contributions"&&(i==null||i(3),A.success("Switched to Analytics"));break}},[m,i,s,a,r,t]);const g=()=>{const p=[{key:"Ctrl/Cmd + 1",description:"Navigate to Start"},{key:"Ctrl/Cmd + 2",description:"Navigate to Track"},{key:"Ctrl/Cmd + 3",description:"Navigate to Earn"},{key:"Ctrl/Cmd + K",description:"Open search"},{key:"Ctrl/Cmd + F",description:"Toggle filters"},{key:"Ctrl/Cmd + N",description:"Create new item"},{key:"1-5",description:"Switch between tabs"},{key:"Alt + T",description:"Time tracker (Track page)"},{key:"Alt + S",description:"Submit work (Track page)"},{key:"Alt + P",description:"Progress view (Track page)"},{key:"Alt + A",description:"Analytics view (Track page)"},{key:"Escape",description:"Close modals/dialogs"},{key:"?",description:"Show this help"}].map(x=>`${x.key}: ${x.description}`).join(`
`);A.success(`Keyboard Shortcuts:

${p}`,{duration:8e3,style:{whiteSpace:"pre-line",textAlign:"left",maxWidth:"400px"}})};return{showKeyboardShortcuts:g}};class Ue{constructor(){this.cache=new Map,this.timers=new Map}set(s,a,r=3e5){this.timers.has(s)&&clearTimeout(this.timers.get(s)),this.cache.set(s,{value:a,timestamp:Date.now(),ttl:r});const t=setTimeout(()=>{this.delete(s)},r);this.timers.set(s,t)}get(s){const a=this.cache.get(s);return a?Date.now()-a.timestamp>a.ttl?(this.delete(s),null):a.value:null}delete(s){this.cache.delete(s),this.timers.has(s)&&(clearTimeout(this.timers.get(s)),this.timers.delete(s))}clear(){this.cache.clear(),this.timers.forEach(s=>clearTimeout(s)),this.timers.clear()}has(s){return this.cache.has(s)&&this.get(s)!==null}size(){return this.cache.size}getStats(){const s=Date.now();let a=0,r=0;return this.cache.forEach(t=>{s-t.timestamp>t.ttl?a++:r++}),{valid:r,expired:a,total:this.cache.size}}}const V=new Ue,Ke=(i={})=>{const{defaultTTL:s=3e5,maxCacheSize:a=100,enablePersistence:r=!1,persistenceKey:t="royaltea_cache"}=i,[m,g]=c.useState({valid:0,expired:0,total:0}),o=c.useCallback(()=>{g(V.getStats())},[]),p=c.useCallback((l,j,S=s)=>{if(V.size()>=a){const h=Array.from(V.cache.entries());h.sort((v,k)=>v[1].timestamp-k[1].timestamp);const _=Math.floor(h.length*.2);for(let v=0;v<_;v++)V.delete(h[v][0])}if(V.set(l,j,S),o(),r)try{const h=JSON.parse(localStorage.getItem(t)||"{}");h[l]={value:j,timestamp:Date.now(),ttl:S},localStorage.setItem(t,JSON.stringify(h))}catch(h){}},[s,a,r,t,o]),x=c.useCallback(l=>{let j=V.get(l);if(!j&&r)try{const h=JSON.parse(localStorage.getItem(t)||"{}")[l];h&&Date.now()-h.timestamp<h.ttl&&(j=h.value,V.set(l,j,h.ttl-(Date.now()-h.timestamp)))}catch(S){}return o(),j},[r,t,o]),y=c.useCallback(l=>{if(V.delete(l),r)try{const j=JSON.parse(localStorage.getItem(t)||"{}");delete j[l],localStorage.setItem(t,JSON.stringify(j))}catch(j){}o()},[r,t,o]),C=c.useCallback(()=>{if(V.clear(),r)try{localStorage.removeItem(t)}catch(l){}o()},[r,t,o]),n=c.useCallback(l=>V.has(l),[]);return c.useEffect(()=>{o()},[o]),{setCache:p,getCache:x,deleteCache:y,clearCache:C,hasCache:n,cacheStats:m}},qe=(i={})=>{const{table:s,select:a="*",filters:r={},orderBy:t={column:"created_at",ascending:!1},pageSize:m=20,enabled:g=!0,cacheKey:o,cacheTTL:p=3e5,enableRealtime:x=!1}=i,{getCache:y,setCache:C}=Ke(),[n,l]=c.useState({data:[],loading:!1,error:null,hasNextPage:!0,hasPreviousPage:!1,currentPage:1,totalCount:null,totalPages:null}),j=c.useRef(),S=c.useRef(),h=c.useCallback(d=>{const u=JSON.stringify(r),R=`${t.column}_${t.ascending}`;return o||`${s}_${u}_${R}_page_${d}_size_${m}`},[s,r,t,m,o]),_=c.useCallback((d=1)=>{let u=J.from(s).select(a,{count:"exact"});Object.entries(r).forEach(([D,f])=>{if(f!=null&&f!=="")if(typeof f=="object"&&f.operator)switch(f.operator){case"gte":u=u.gte(D,f.value);break;case"lte":u=u.lte(D,f.value);break;case"like":u=u.ilike(D,`%${f.value}%`);break;case"in":u=u.in(D,f.value);break;case"neq":u=u.neq(D,f.value);break;default:u=u.eq(D,f.value)}else u=u.eq(D,f)}),u=u.order(t.column,{ascending:t.ascending});const R=(d-1)*m,T=R+m-1;return u=u.range(R,T),u},[s,a,r,t,m]),v=c.useCallback((d=1,u=!0)=>Q(null,null,function*(){if(!g)return;const R=h(d);if(u){const T=y(R);if(T)return l(D=>K($($({},D),T),{currentPage:d})),T}j.current&&j.current.abort(),j.current=new AbortController;try{l(U=>K($({},U),{loading:!0,error:null}));const T=_(d),{data:D,error:f,count:w}=yield T;if(f)throw f;const H=Math.ceil(w/m),Z=d<H,ee=d>1,I={data:D||[],hasNextPage:Z,hasPreviousPage:ee,totalCount:w,totalPages:H,loading:!1,error:null};return l(U=>K($($({},U),I),{currentPage:d})),C(R,I,p),I}catch(T){T.name!=="AbortError"&&l(D=>K($({},D),{loading:!1,error:T}))}}),[g,h,y,C,p,_,m]),k=c.useCallback(d=>{d>=1&&d<=n.totalPages&&v(d)},[v,n.totalPages]),P=c.useCallback(()=>{n.hasNextPage&&k(n.currentPage+1)},[k,n.hasNextPage,n.currentPage]),N=c.useCallback(()=>{n.hasPreviousPage&&k(n.currentPage-1)},[k,n.hasPreviousPage,n.currentPage]),B=c.useCallback(()=>{k(1)},[k]),F=c.useCallback(()=>{n.totalPages&&k(n.totalPages)},[k,n.totalPages]),E=c.useCallback(()=>{v(n.currentPage,!1)},[v,n.currentPage]);return c.useEffect(()=>{if(!(!x||!s))try{const d=J.channel(`${s}_changes`).on("postgres_changes",{event:"*",schema:"public",table:s},u=>{E()}).subscribe(u=>{});return S.current=d,()=>{if(S.current)try{J.removeChannel(S.current)}catch(u){}}}catch(d){}},[x,s,E]),c.useEffect(()=>{v(1)},[v]),c.useEffect(()=>()=>{j.current&&j.current.abort(),S.current&&J.removeChannel(S.current)},[]),K($({},n),{goToPage:k,nextPage:P,previousPage:N,firstPage:B,lastPage:F,refresh:E,pageSize:m})},Fe=(i,s={})=>qe({table:"contributions",select:`
      *,
      projects (
        id,
        name,
        project_type
      )
    `,filters:$({user_id:i},s.filters),orderBy:s.orderBy||{column:"created_at",ascending:!1},pageSize:s.pageSize||20,enabled:!!i,cacheKey:`contributions_${i}`,enableRealtime:s.enableRealtime||!1}),Oe={animate:{opacity:[.5,1,.5]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},b=t=>{var m=t,{width:i="100%",height:s="1rem",className:a=""}=m,r=ne(m,["width","height","className"]);return e.jsx(z.div,$($({className:`bg-default-200 rounded ${a}`,style:{width:i,height:s}},Oe),r))},We=({className:i=""})=>e.jsxs(M,{className:`border border-divider ${i}`,children:[e.jsx(re,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsx(b,{width:"50%",height:"1.5rem"}),e.jsx(b,{width:"4rem",height:"1.25rem",className:"rounded-full"})]})}),e.jsx(L,{className:"pt-0",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{width:"100%",height:"0.875rem"}),e.jsx(b,{width:"90%",height:"0.875rem"}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 pt-2",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(b,{width:"2rem",height:"1.5rem",className:"mx-auto mb-1"}),e.jsx(b,{width:"100%",height:"0.75rem"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(b,{width:"2rem",height:"1.5rem",className:"mx-auto mb-1"}),e.jsx(b,{width:"100%",height:"0.75rem"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx(b,{width:"2rem",height:"1.5rem",className:"mx-auto mb-1"}),e.jsx(b,{width:"100%",height:"0.75rem"})]})]}),e.jsx(b,{width:"100%",height:"0.5rem",className:"rounded-full"})]})})]}),oe=({className:i=""})=>e.jsx(M,{className:`border border-divider ${i}`,children:e.jsx(L,{className:"p-6 text-center",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx(b,{width:"3rem",height:"3rem",className:"mx-auto rounded-lg"}),e.jsx(b,{width:"4rem",height:"2rem",className:"mx-auto"}),e.jsx(b,{width:"6rem",height:"0.875rem",className:"mx-auto"})]})})}),fe=({className:i=""})=>e.jsxs(M,{className:`border border-divider ${i}`,children:[e.jsx(re,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsx(b,{width:"8rem",height:"1.25rem"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{width:"2rem",height:"1.5rem",className:"rounded"}),e.jsx(b,{width:"2rem",height:"1.5rem",className:"rounded"}),e.jsx(b,{width:"2rem",height:"1.5rem",className:"rounded"})]})]})}),e.jsx(L,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"h-48 bg-default-100 rounded relative overflow-hidden",children:[e.jsx(z.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent",animate:{x:[-100,400]},transition:{duration:2,repeat:1/0,ease:"linear"}}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 flex items-end justify-around p-4",children:[40,60,35,80,45,70,55].map((s,a)=>e.jsx(b,{width:"1rem",height:`${s}%`,className:"rounded-t"},a))})]}),e.jsxs("div",{className:"flex items-center justify-center gap-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(b,{width:"1rem",height:"0.25rem"}),e.jsx(b,{width:"4rem",height:"0.875rem"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(b,{width:"1rem",height:"0.25rem"}),e.jsx(b,{width:"3rem",height:"0.875rem"})]})]})]})})]}),Be=({rows:i=5,columns:s=4,className:a=""})=>e.jsxs(M,{className:`border border-divider ${a}`,children:[e.jsx(re,{children:e.jsx(b,{width:"10rem",height:"1.25rem"})}),e.jsx(L,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((r,t)=>e.jsx(b,{width:"80%",height:"1rem"},t))}),Array.from({length:i}).map((r,t)=>e.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((m,g)=>e.jsx(b,{width:g===0?"100%":"60%",height:"0.875rem"},g))},t))]})})]}),be=({fields:i=4,className:s=""})=>e.jsxs(M,{className:`border border-divider ${s}`,children:[e.jsx(re,{children:e.jsx(b,{width:"8rem",height:"1.5rem"})}),e.jsx(L,{children:e.jsxs("div",{className:"space-y-6",children:[Array.from({length:i}).map((a,r)=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(b,{width:"6rem",height:"1rem"}),e.jsx(b,{width:"100%",height:"2.5rem",className:"rounded-lg"})]},r)),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx(b,{width:"4rem",height:"2.5rem",className:"rounded-lg"}),e.jsx(b,{width:"5rem",height:"2.5rem",className:"rounded-lg"})]})]})})]}),se=({className:i=""})=>e.jsxs("div",{className:`space-y-4 ${i}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(b,{width:"8rem",height:"2rem"}),e.jsx(b,{width:"2.5rem",height:"2.5rem",className:"rounded-full"})]}),e.jsx("div",{className:"flex gap-4",children:Array.from({length:4}).map((s,a)=>e.jsx(b,{width:"6rem",height:"2.5rem",className:"rounded-lg"},a))})]}),Ve=({type:i="default",className:s=""})=>{const a={track:e.jsxs("div",{className:"space-y-8",children:[e.jsx(se,{}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsx(be,{fields:3})}),e.jsx("div",{children:e.jsx(oe,{})})]})]}),earn:e.jsxs("div",{className:"space-y-8",children:[e.jsx(se,{}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:Array.from({length:4}).map((r,t)=>e.jsx(oe,{},t))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsx(fe,{}),e.jsx(Be,{rows:6,columns:3})]})]}),project:e.jsxs("div",{className:"space-y-8",children:[e.jsx(se,{}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((r,t)=>e.jsx(We,{},t))}),e.jsx(fe,{})]}),default:e.jsxs("div",{className:"space-y-8",children:[e.jsx(se,{}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsx(be,{}),e.jsx(oe,{})]})]})};return e.jsx("div",{className:`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${s}`,children:e.jsx("div",{className:"container mx-auto max-w-6xl",children:a[i]||a.default})})};SkeletonScreens;const st=({currentUser:i,className:s=""})=>{var D;const[a,r]=c.useState("tracker"),[t,m]=c.useState(!1),{syncTriggers:g,triggerContributionSync:o}=$e(),{currentUser:p}=c.useContext(ie),[x,y]=c.useState([]),[C,n]=c.useState([]),[l,j]=c.useState({}),[S,h]=c.useState(!1),[_,v]=c.useState(!1),k=i||p;He({onNavigateToTab:f=>{const w=["tracker","submit","progress","analytics"];w[f]&&r(w[f])},onToggleFilters:()=>v(!_),onCreateNew:()=>r("submit"),currentCanvas:"contributions"});const P=f=>{o(),m(!0),setTimeout(()=>m(!1),3e3)},N=f=>{o()},B=()=>{switch(a){case"tracker":return 1;case"submit":return 2;case"progress":return 3;default:return 1}},F=()=>{switch(a){case"tracker":return["Start time tracking for your current task","Submit contributions when work is complete","Review progress and analytics"];case"submit":return["Fill out contribution details","Set difficulty rating and hours","Submit for validation"];case"progress":return["Review your contribution metrics","Check validation status","Plan next tasks"];case"analytics":return["Analyze contribution trends","Review performance metrics","Identify improvement opportunities"];default:return[]}},{data:E,loading:d}=Fe(k==null?void 0:k.id,{pageSize:50,enableRealtime:!0,filters:l});c.useEffect(()=>{E!=null&&E.data&&(y(E.data),u(E.data,l))},[E,l]);const u=(f,w)=>{var Z,ee;let H=[...f];if(w.search){const I=w.search.toLowerCase();H=H.filter(U=>{var O,Y,me,he;return((O=U.title)==null?void 0:O.toLowerCase().includes(I))||((Y=U.description)==null?void 0:Y.toLowerCase().includes(I))||((he=(me=U.projects)==null?void 0:me.name)==null?void 0:he.toLowerCase().includes(I))})}if(w.status&&w.status!=="all"&&(H=H.filter(I=>I.status===w.status)),w.difficulty){const[I,U]=w.difficulty;H=H.filter(O=>O.difficulty_rating>=I&&O.difficulty_rating<=U)}((Z=w.dateRange)!=null&&Z.start||(ee=w.dateRange)!=null&&ee.end)&&(H=H.filter(I=>{const U=new Date(I.created_at),O=w.dateRange.start?new Date(w.dateRange.start):null,Y=w.dateRange.end?new Date(w.dateRange.end):null;return!(O&&U<O||Y&&U>Y)})),w.showOnlyMine&&(H=H.filter(I=>I.user_id===k.id)),w.sortBy&&H.sort((I,U)=>{const O=I[w.sortBy],Y=U[w.sortBy];return w.sortOrder==="desc"?Y>O?1:-1:O>Y?1:-1}),n(H)},R=f=>{j(f),u(x,f)};c.useEffect(()=>{h(d)},[d]);const T=[{key:"tracker",title:"⏱️ Time Tracker",description:"Track time as you work"},{key:"submit",title:"📤 Submit Work",description:"Log completed contributions"},{key:"progress",title:"📊 Progress",description:"View your contribution stats"},{key:"analytics",title:"📈 Analytics",description:"Analyze trends and performance"}];return S&&!x.length?e.jsx(Ve,{type:"track",className:s}):e.jsxs("div",{className:`min-h-screen bg-gradient-to-br from-slate-900 via-orange-900 to-red-900 ${s}`,children:[e.jsx(z.div,{className:"relative z-10 pt-8 pb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:e.jsxs("div",{className:"container mx-auto px-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(z.div,{className:"text-6xl mb-4",animate:{scale:[1,1.1,1],rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatType:"reverse"},children:"⏱️"}),e.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:"Track Your Work"}),e.jsx("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Log your contributions, track your time, and monitor your progress. Every moment of work counts toward your royalty share."})]}),e.jsx(de,{children:t&&e.jsx(z.div,{className:"fixed top-4 right-4 z-50",initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},exit:{opacity:0,scale:0},children:e.jsx(M,{className:"bg-green-500/20 border border-green-500/50",children:e.jsx(L,{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-2 text-green-400",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-sm font-medium",children:"Tracking Active"})]})})})})})]})}),e.jsxs("div",{className:"container mx-auto px-6 pb-12",children:[e.jsx(Ae,{currentTile:"track",className:"mb-6"}),e.jsx(Me,{currentJourney:"track",currentStep:B(),totalSteps:3,nextSteps:F(),className:"mb-6"}),e.jsxs(z.div,{className:"max-w-6xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:[e.jsx(M,{className:"mb-8 bg-white/5 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsx(De,{selectedKey:a,onSelectionChange:r,variant:"underlined",classNames:{tabList:"gap-6 w-full relative rounded-none p-0 border-b border-divider",cursor:"w-full bg-gradient-to-r from-orange-500 to-red-500",tab:"max-w-fit px-0 h-12",tabContent:"group-data-[selected=true]:text-white text-white/70"},children:T.map(f=>e.jsx(Pe,{title:e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("span",{children:f.title})})},f.key))}),e.jsx(z.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4",children:e.jsx("p",{className:"text-white/70 text-sm",children:(D=T.find(f=>f.key===a))==null?void 0:D.description})},a)]})}),e.jsx(de,{mode:"wait",children:e.jsxs(z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:[a==="tracker"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-2","data-tour":"time-tracker",children:e.jsx(Ie,{onTrackingStart:()=>m(!0),onTrackingStop:()=>m(!1),onContributionComplete:N,refreshTrigger:g.contributions})}),e.jsx("div",{"data-tour":"progress-view",children:e.jsx(ce,{refreshTrigger:g.contributions})})]}),a==="submit"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsx("div",{"data-tour":"submit-form",children:e.jsx(ze,{onSubmit:P,refreshTrigger:g.contributions})}),e.jsx(ce,{refreshTrigger:g.contributions})]}),a==="progress"&&e.jsxs("div",{className:"grid grid-cols-1 gap-8",children:[e.jsx(pe,{filterType:"contributions",onFiltersChange:R}),e.jsx(ce,{refreshTrigger:g.contributions}),e.jsx(M,{className:"bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Insights & Tips"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"💡 Productivity Tips"}),e.jsxs("ul",{className:"space-y-2 text-white/80 text-sm",children:[e.jsx("li",{children:"• Break large tasks into smaller, trackable chunks"}),e.jsx("li",{children:"• Use the time tracker for focused work sessions"}),e.jsx("li",{children:"• Log contributions immediately after completion"}),e.jsx("li",{children:"• Set realistic daily and weekly goals"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"🎯 Royalty Impact"}),e.jsxs("ul",{className:"space-y-2 text-white/80 text-sm",children:[e.jsx("li",{children:"• Higher difficulty tasks increase your share"}),e.jsx("li",{children:"• Consistent contributions build long-term value"}),e.jsx("li",{children:"• Quality over quantity - focus on meaningful work"}),e.jsx("li",{children:"• Approved contributions count toward royalties"})]})]})]})]})})]}),a==="analytics"&&e.jsxs("div",{className:"grid grid-cols-1 gap-8","data-tour":"analytics",children:[e.jsx(pe,{filterType:"contributions",onFiltersChange:R}),e.jsx(Ee,{contributionData:C,earningsData:[]}),e.jsx(M,{className:"bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-white/10",children:e.jsxs(L,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-white mb-4",children:"Performance Analytics"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-3xl font-bold text-purple-400",children:C.length}),e.jsx("p",{className:"text-white/80 text-sm",children:"Filtered Results"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-3xl font-bold text-pink-400",children:C.reduce((f,w)=>f+(w.hours_tracked||0),0).toFixed(1)}),e.jsx("p",{className:"text-white/80 text-sm",children:"Total Hours"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-3xl font-bold text-blue-400",children:C.length>0?(C.reduce((f,w)=>f+(w.difficulty_rating||3),0)/C.length).toFixed(1):"0.0"}),e.jsx("p",{className:"text-white/80 text-sm",children:"Avg Difficulty"})]})]})]})})]})]},a)}),e.jsxs(z.div,{className:"mt-8 flex justify-center gap-4 flex-wrap",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:[e.jsx(q,{onClick:()=>r("tracker"),className:`${a==="tracker"?"bg-orange-500":"bg-white/10"} text-white`,variant:a==="tracker"?"solid":"ghost",children:"Start Tracking"}),e.jsx(q,{onClick:()=>r("submit"),className:`${a==="submit"?"bg-orange-500":"bg-white/10"} text-white`,variant:a==="submit"?"solid":"ghost",children:"Submit Work"}),e.jsx(q,{onClick:()=>r("progress"),className:`${a==="progress"?"bg-orange-500":"bg-white/10"} text-white`,variant:a==="progress"?"solid":"ghost",children:"View Progress"}),e.jsx(q,{onClick:()=>r("analytics"),className:`${a==="analytics"?"bg-orange-500":"bg-white/10"} text-white`,variant:a==="analytics"?"solid":"ghost",children:"Analytics"})]})]})]}),e.jsx(Le,{context:"track"})]})};export{st as default};
