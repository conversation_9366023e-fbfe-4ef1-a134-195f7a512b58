import React, { useState as reactUseState, useContext, useEffect } from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import ExperimentalNavigation from "./components/navigation/ExperimentalNavigation";
import SimpleLogin from "./pages/user/SimpleLogin";
import ModernDashboard from "./pages/user/ModernDashboard";
import ProfilePage from "./pages/user/ProfilePage";
import PublicProfile from "./pages/user/PublicProfile";
import RetroProfilePage from "./pages/user/RetroProfilePage";
import PasswordResetPage from "./pages/user/PasswordResetPage";
import UpdatePasswordPage from "./pages/user/UpdatePasswordPage";
import SettingsPage from "./pages/user/SettingsPage";
import AuthCallback from "./pages/auth/Callback";
import SimpleLoading from "./components/layout/SimpleLoading";
import RoadmapPage from "./pages/admin/Roadmap";
import RoadmapManagerPage from "./pages/admin/RoadmapManager";
import ProfileMigration from "./pages/admin/ProfileMigration";
import AdminDashboard from "./pages/admin/AdminDashboard";
import ComponentPlayground from "./pages/admin/ComponentPlayground";
import BugManager from "./pages/admin/BugManager";
import IntegrationTestDataPage from "./pages/admin/IntegrationTestDataPage";
import BugReportPage from "./pages/bug/BugReportPage";
import ProjectWizard from "./pages/project/ProjectWizard";
import NotificationDebugPage from "./pages/debug/NotificationDebugPage";
// ModernProjectCreator has been replaced by ProjectWizard
import ProjectDetail from "./pages/project/ProjectDetail";
import ProjectsList from "./pages/project/ProjectsList";
import ContributionTrackerPage from "./pages/contribution/ContributionTrackerPage";
import ContributionsPage from "./pages/contribution/ContributionsPage";
import ContributionValidatePage from "./pages/contribution/validate";
import ValidationMetricsPage from "./pages/validation/ValidationMetricsPage";
import ContributionAnalyticsPage from "./pages/analytics/ContributionAnalyticsPage";
import RevenuePage from "./pages/revenue/RevenuePage";
import RoyaltyCalculatorPage from "./pages/royalty/RoyaltyCalculatorPage";
import KanbanPage from "./pages/kanban/KanbanPage";
import ProjectAgreements from "./pages/project/ProjectAgreements";

import NotFound from "./pages/NotFound";
import TrackPage from "./pages/track/TrackPage";
import StartPage from "./pages/start/StartPage";
import EarnPage from "./pages/earn/EarnPage";
import LearnPage from "./pages/learn/LearnPage";
import NotificationsPage from "./pages/notification/NotificationsPage";
import InvitationsPage from "./pages/invitation/InvitationsPage";
import SocialPage from "./pages/social/SocialPage";
import TeamList from "./components/team/TeamList";
import TeamDetail from "./components/team/TeamDetail";
import TeamManage from "./components/team/TeamManage";
import TeamInvitations from "./components/team/TeamInvitations";

// Studio Components (New Terminology)
import StudioList from "./components/studio/StudioList";
import StudioDashboard from "./components/studio/StudioDashboard";
import StudioCreationWizard from "./components/studio/StudioCreationWizard";
import StudioManage from "./components/studio/StudioManage";
import StudioInvitations from "./components/studio/StudioInvitations";

// Mission Components (New Terminology)
import MissionBoard from "./components/missions/MissionBoard";
import MissionCreator from "./components/missions/MissionCreator";
import MissionDetail from "./components/missions/MissionDetail";
import AuthRoute from "./components/auth/AuthRoute";
import TestUI from "./components/ui/test-ui";

import SimpleNavHeader from "./components/navigation/SimpleNavHeader";
import ModernNavigation from "./components/navigation/ModernNavigation";
import axios from "axios";
import { UserContext } from "./contexts/supabase-auth.context.jsx";
import { DataSyncProvider } from "./contexts/DataSyncContext";
import { NavigationProvider } from "./contexts/NavigationContext";
import { Toaster } from "react-hot-toast";
import Footer from "./components/layout/Footer";
import { fixRouting } from "./utils/routing-fix";
import GlobalErrorBoundary from "./components/error/GlobalErrorBoundary";
import EnhancedErrorBoundary from "./components/error/EnhancedErrorBoundary";
import { NetworkStatus } from "./components/common/LoadingStates";
import { SkipLinks as AccessibilitySkipLinks } from "./components/accessibility/AccessibilityEnhancements";

// Use Netlify Functions for API
axios.defaults.baseURL = '/.netlify/functions';
axios.defaults.withCredentials = true;

const navLinks = [
  { title: "Home", path: "/" },
];

// ScrollToTop component to reset scroll position on navigation
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

// Disable focus-aware loading hooks - they can cause issues when tabbing
// We'll use the original React.useState directly

function App() {
  console.log('🔍 DEBUG: App.jsx component executing');
  const { currentUser, isLoading, signOut } = useContext(UserContext);
  const location = useLocation();
  console.log('🔍 DEBUG: App.jsx location:', location.pathname);

  // Log navigation for debugging and fix routing issues
  useEffect(() => {
    console.log('Current location:', location.pathname);
    // Fix routing issues
    fixRouting();
  }, [location]);

  // Track loading state changes with specific debug text
  useEffect(() => {
    console.log('🔍 APP.JSX: isLoading state changed to:', isLoading);

    // Add stack trace to see where the loading state is being changed
    if (isLoading) {
      console.log('🔍 APP.JSX: Loading triggered from:', new Error().stack);
    }
  }, [isLoading]);

  // Show loading screen when needed
  if (isLoading) {
    return <SimpleLoading text="Loading..." fullPage={true} />;
  }
  // Use modern navigation by default - clean, focused UX
  const useExperimentalNavigation = window.location.search.includes('experimental=true');

  if (useExperimentalNavigation) {
    return (
      <EnhancedErrorBoundary userId={currentUser?.id} level="page">
        <AccessibilitySkipLinks />
        <NetworkStatus />
        <ScrollToTop />
        <Toaster position="bottom-right" toastOptions={{ duration: 5000 }} />
        <DataSyncProvider>
          <NavigationProvider currentUser={currentUser}>
            <main id="main-content" role="main">
              <ExperimentalNavigation currentUser={currentUser} />
            </main>
          </NavigationProvider>
        </DataSyncProvider>
        <Footer navLinks={navLinks} />
      </EnhancedErrorBoundary>
    );
  }

  // Default: Modern Navigation
  return (
    <EnhancedErrorBoundary userId={currentUser?.id} level="page">
      <AccessibilitySkipLinks />
      <NetworkStatus />
      <ScrollToTop />
      <Toaster position="bottom-right" toastOptions={{ duration: 5000 }} />
      <DataSyncProvider>
        <NavigationProvider currentUser={currentUser}>
          <ModernNavigation
            currentUser={currentUser}
            onLogout={async () => {
              try {
                console.log('🔐 Logging out user...');
                await signOut();
                console.log('🔐 Logout successful, redirecting to login');
                window.location.href = '/login';
              } catch (error) {
                console.error('🔐 Logout error:', error);
                // Force redirect even if logout fails
                window.location.href = '/login';
              }
            }}
          />
          <main id="main-content" role="main" className="min-h-screen bg-gray-50 dark:bg-gray-900 pt-16 pb-24 md:pt-0 md:pb-0">
            <Routes>
                {/* Authentication Routes */}
                <Route path="/" element={
                  isLoading ? <SimpleLoading text="Loading..." fullPage={true} /> :
                  currentUser ? <ModernDashboard /> : <SimpleLogin />
                } />
                <Route path="/dashboard" element={<AuthRoute><ModernDashboard /></AuthRoute>} />
                <Route path="/login" element={currentUser ? <Navigate to="/" /> : <SimpleLogin />} />
                <Route path="/auth/callback" element={<AuthCallback />} />
                <Route path="/password-reset" element={<PasswordResetPage />} />
                <Route path="/reset-password" element={<UpdatePasswordPage />} />

                {/* Core Navigation Routes */}
                <Route path="/start" element={<AuthRoute><StartPage /></AuthRoute>} />
                <Route path="/track" element={<AuthRoute><TrackPage /></AuthRoute>} />
                <Route path="/earn" element={<AuthRoute><EarnPage /></AuthRoute>} />
                <Route path="/learn" element={<AuthRoute><LearnPage /></AuthRoute>} />

                {/* Social Routes */}
                <Route path="/social" element={<AuthRoute><SocialPage /></AuthRoute>} />
                <Route path="/friends" element={<AuthRoute><SocialPage /></AuthRoute>} />

                {/* Analytics Routes */}
                <Route path="/analytics" element={<AuthRoute><ContributionAnalyticsPage /></AuthRoute>} />
                <Route path="/analytics/contributions" element={<AuthRoute><ContributionAnalyticsPage /></AuthRoute>} />

                {/* Revenue Routes */}
                <Route path="/revenue" element={<AuthRoute><RevenuePage /></AuthRoute>} />

                {/* Validation Routes */}
                <Route path="/validate" element={<AuthRoute><ValidationMetricsPage /></AuthRoute>} />
                <Route path="/validate/pending" element={<AuthRoute><ContributionValidatePage /></AuthRoute>} />

                {/* Contribution Routes */}
                <Route path="/contributions" element={<AuthRoute><ContributionsPage /></AuthRoute>} />
                <Route path="/contributions/dashboard" element={<AuthRoute><ContributionsPage /></AuthRoute>} />
                <Route path="/contributions/tracker" element={<AuthRoute><ContributionTrackerPage /></AuthRoute>} />

                {/* Profile Routes */}
                <Route path="/profile" element={<AuthRoute><ProfilePage /></AuthRoute>} />
                <Route path="/profile/retro" element={<AuthRoute><RetroProfilePage /></AuthRoute>} />
                <Route path="/profile/public/:userId" element={<PublicProfile />} />
                <Route path="/profile/migration" element={<AuthRoute><ProfileMigration /></AuthRoute>} />

                {/* Settings Routes */}
                <Route path="/settings" element={<AuthRoute><SettingsPage /></AuthRoute>} />
                <Route path="/notifications" element={<AuthRoute><NotificationsPage /></AuthRoute>} />
                <Route path="/invitations" element={<AuthRoute><InvitationsPage /></AuthRoute>} />

                {/* Project Routes */}
                <Route path="/projects" element={<AuthRoute><ProjectsList /></AuthRoute>} />
                <Route path="/project/create" element={
                  (() => {
                    console.log('🔍 DEBUG: /project/create route matched!');
                    return <AuthRoute><ProjectWizard /></AuthRoute>;
                  })()
                } />
                <Route path="/project/wizard" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
                <Route path="/project/wizard/:id" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
                <Route path="/project/wizard/:id/step/:step" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
                <Route path="/project/:id" element={<AuthRoute><ProjectDetail /></AuthRoute>} />
                <Route path="/project/:id/edit" element={<AuthRoute><ProjectWizard /></AuthRoute>} />
                <Route path="/project/:id/edit/step/:step" element={<AuthRoute><ProjectWizard /></AuthRoute>} />

                {/* Kanban Routes */}
                <Route path="/project/:id/tasks" element={currentUser ? <KanbanPage /> : <Navigate to="/login" />} />

                {/* Team Routes */}
                <Route path="/teams" element={<AuthRoute><TeamList /></AuthRoute>} />
                <Route path="/teams/:id" element={<AuthRoute><TeamDetail /></AuthRoute>} />
                <Route path="/teams/:id/manage" element={<AuthRoute><TeamManage /></AuthRoute>} />
                <Route path="/teams/:id/invitations" element={<AuthRoute><TeamInvitations /></AuthRoute>} />

                {/* Studio Routes (New Terminology) */}
                <Route path="/studios" element={<AuthRoute><StudioList /></AuthRoute>} />
                <Route path="/studios/create" element={<AuthRoute><StudioCreationWizard /></AuthRoute>} />
                <Route path="/studios/:id" element={<AuthRoute><StudioDashboard /></AuthRoute>} />
                <Route path="/studios/:id/manage" element={<AuthRoute><StudioManage /></AuthRoute>} />
                <Route path="/studios/:id/invitations" element={<AuthRoute><StudioInvitations /></AuthRoute>} />

                {/* Mission Routes (New Terminology) */}
                <Route path="/missions" element={<AuthRoute><MissionBoard /></AuthRoute>} />
                <Route path="/missions/create" element={<AuthRoute><MissionCreator /></AuthRoute>} />
                <Route path="/missions/:id" element={<AuthRoute><MissionDetail /></AuthRoute>} />

                {/* Backward Compatibility Redirects */}
                <Route path="/alliances" element={<Navigate to="/studios" replace />} />
                <Route path="/alliances/create" element={<Navigate to="/studios/create" replace />} />
                <Route path="/alliances/:id" element={<Navigate to="/studios/:id" replace />} />
                <Route path="/ventures" element={<Navigate to="/projects" replace />} />
                <Route path="/ventures/create" element={<Navigate to="/project/create" replace />} />
                <Route path="/ventures/:id" element={<Navigate to="/project/:id" replace />} />
                <Route path="/quests" element={<Navigate to="/missions" replace />} />
                <Route path="/quests/:id" element={<Navigate to="/missions/:id" replace />} />

                {/* Admin Routes - Always available, AdminDashboard handles access control */}
                <Route path="/admin" element={<AuthRoute><AdminDashboard /></AuthRoute>} />
                <Route path="/admin/roadmap" element={<AuthRoute><RoadmapPage /></AuthRoute>} />
                <Route path="/admin/roadmap/manage" element={<AuthRoute><RoadmapManagerPage /></AuthRoute>} />
                <Route path="/admin/playground" element={<AuthRoute><ComponentPlayground /></AuthRoute>} />
                <Route path="/admin/bugs" element={<AuthRoute><BugManager /></AuthRoute>} />
                <Route path="/admin/integration-test-data" element={<AuthRoute><IntegrationTestDataPage /></AuthRoute>} />

                {/* Bug Reporting */}
                <Route path="/bugs/report" element={<AuthRoute><BugReportPage /></AuthRoute>} />

                {/* Debug Routes */}
                <Route path="/debug/notifications" element={<AuthRoute><NotificationDebugPage /></AuthRoute>} />

                {/* Test Routes */}
                <Route path="/test/ui" element={<TestUI />} />


                {/* Catch-all route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </main>
          </NavigationProvider>
        </DataSyncProvider>
        <Footer navLinks={navLinks} />
      </EnhancedErrorBoundary>
    );
}

export default App;
