import { test, expect } from '@playwright/test';

test.describe('Project Wizard Debug Tests', () => {
  test('Debug Project Wizard Loading Issues', async ({ page }) => {
    console.log('🚀 Debugging project wizard loading...');
    
    // Capture console logs
    const consoleLogs = [];
    const jsErrors = [];
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`);
      console.log(`BROWSER: ${msg.type()}: ${msg.text()}`);

      // Capture JavaScript errors specifically
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    // Capture page errors (uncaught exceptions)
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
      console.log(`PAGE ERROR: ${error.message}`);
    });

    // Capture network failures
    const networkFailures = [];
    page.on('requestfailed', request => {
      networkFailures.push(`${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
    });

    // Capture response errors
    const responseErrors = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        responseErrors.push(`${response.status()} ${response.url()}`);
      }
    });
    
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Check if main JavaScript bundle loaded
    const jsLoaded = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      const mainScript = scripts.find(script => script.src.includes('main-') || script.src.includes('assets/'));
      return {
        hasMainScript: !!mainScript,
        mainScriptSrc: mainScript?.src || 'none',
        totalScripts: scripts.length,
        allScripts: scripts.map(s => s.src)
      };
    });
    console.log('📦 JavaScript bundle info:', jsLoaded);
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Navigate to Start page and click the create project button
    console.log('📝 Going to Start page...');
    await page.goto('https://royalty.technology/#/start');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Take a screenshot to see what's actually displayed
    await page.screenshot({ path: 'test-results/start-page-debug.png', fullPage: true });
    console.log('📸 Screenshot saved to test-results/start-page-debug.png');

    // Log all visible text on the page
    const pageText = await page.textContent('body');
    console.log('📄 Page text content:');
    console.log(pageText.substring(0, 500) + '...');

    // Check for tabs
    const tabs = await page.locator('[role="tab"], .tab, [data-key]').all();
    console.log(`📑 Found ${tabs.length} tabs`);
    for (let i = 0; i < tabs.length; i++) {
      const tabText = await tabs[i].textContent();
      console.log(`  Tab ${i + 1}: "${tabText}"`);
    }

    // Wait for the page to fully load and look for the specific project wizard button
    console.log('🔍 Looking for project wizard button...');

    let buttonClicked = false;

    // Wait for the specific button text to appear
    try {
      const projectWizardButton = page.locator('button').filter({ hasText: /Start Project Wizard/i });
      await projectWizardButton.waitFor({ timeout: 10000 });
      console.log('🎯 Found "Start Project Wizard" button!');
      await projectWizardButton.click();
      buttonClicked = true;
    } catch (error) {
      console.log('⚠️ "Start Project Wizard" button not found, looking for alternatives...');

      // Look for any buttons with project/wizard/create text
      const createButtons = await page.locator('button, a').filter({ hasText: /create|new|start|project|wizard/i }).all();
      console.log(`🎯 Found ${createButtons.length} potential buttons`);

      for (let i = 0; i < createButtons.length; i++) {
        const buttonText = await createButtons[i].textContent();
        console.log(`  Button ${i + 1}: "${buttonText}"`);

        // Only click buttons that specifically mention project creation/wizard
        if (buttonText && (buttonText.toLowerCase().includes('project wizard') ||
                           buttonText.toLowerCase().includes('create project') ||
                           buttonText.toLowerCase().includes('new project'))) {
          console.log(`🎯 Clicking button: "${buttonText}"`);
          await createButtons[i].click();
          buttonClicked = true;
          break;
        }
      }
    }

    if (!buttonClicked) {
      console.log('⚠️ No create project button found on Start page (user has existing projects)');
      console.log('🔗 Trying direct navigation to project wizard...');
      await page.goto('https://royalty.technology/#/project/wizard');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }

    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Check if we're redirected to login
    const afterNavigateUrl = page.url();
    console.log(`🔗 URL after navigation: ${afterNavigateUrl}`);

    // If redirected to login, login again
    if (afterNavigateUrl.includes('/login')) {
      console.log('🔐 Redirected to login, logging in again...');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Try navigating to wizard again
      await page.goto('https://royalty.technology/#/project/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/wizard-debug.png',
      fullPage: true 
    });
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`🔗 Current URL: ${currentUrl}`);
    
    // Check if we're still on the wizard page or redirected
    const isOnWizard = currentUrl.includes('/project/create') || currentUrl.includes('/project/wizard');
    console.log(`🧙 Still on wizard page: ${isOnWizard}`);
    
    // Check page content
    const pageContent = await page.textContent('body');
    console.log(`📄 Page content length: ${pageContent.length}`);
    console.log('📄 First 200 chars of wizard page content:');
    console.log(pageContent.substring(0, 200));

    // Take a screenshot of the wizard page
    await page.screenshot({ path: 'test-results/wizard-page-content.png', fullPage: true });
    console.log('📸 Wizard page content screenshot saved');
    
    // Look for specific debug messages
    const hasDebugMessages = consoleLogs.some(log => log.includes('DEBUG: ProjectWizard'));
    console.log(`🐛 Has ProjectWizard debug messages: ${hasDebugMessages}`);
    
    // Look for loading messages
    const hasLoadingMessages = consoleLogs.some(log => log.includes('loading'));
    console.log(`⏳ Has loading messages: ${hasLoadingMessages}`);
    
    // Look for route matching messages
    const hasRouteMessages = consoleLogs.some(log => log.includes('/project/create route matched'));
    console.log(`🛣️ Has route matching messages: ${hasRouteMessages}`);
    
    // Check for React errors
    const hasReactErrors = consoleLogs.some(log => log.includes('Error') && log.includes('React'));
    console.log(`⚛️ Has React errors: ${hasReactErrors}`);

    // Check for JavaScript execution
    const hasJavaScriptExecution = consoleLogs.some(log =>
      log.includes('JavaScript is executing') ||
      log.includes('React root created') ||
      log.includes('React app render')
    );
    console.log(`🚨 Has JavaScript execution: ${hasJavaScriptExecution}`);

    // Check for any JavaScript errors
    const hasJavaScriptErrors = consoleLogs.some(log =>
      log.includes('TypeError') ||
      log.includes('ReferenceError') ||
      log.includes('SyntaxError') ||
      log.includes('Cannot read') ||
      log.includes('is not defined')
    );
    console.log(`💥 Has JavaScript errors: ${hasJavaScriptErrors}`);
    
    // Print all console logs for debugging
    console.log('\n📋 ALL CONSOLE LOGS:');
    consoleLogs.forEach((log, index) => {
      console.log(`  ${index + 1}: ${log}`);
    });
    
    // Check if the wizard header is present
    const wizardHeader = await page.locator('text="Create Your Project"').isVisible();
    console.log(`🎯 Wizard header visible: ${wizardHeader}`);
    
    // Check for loading animation
    const loadingAnimation = await page.locator('[class*="loading"], [class*="spinner"]').isVisible();
    console.log(`⏳ Loading animation visible: ${loadingAnimation}`);
    
    // Try alternative routes
    console.log('\n🔄 Trying alternative routes...');
    
    // Try /project/wizard
    await page.goto('https://royalty.technology/#/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const wizardUrl = page.url();
    console.log(`🔗 Wizard URL: ${wizardUrl}`);
    
    const wizardContent = await page.textContent('body');
    const hasWizardContent = wizardContent.includes('Project Basics') || wizardContent.includes('Create Your Project');
    console.log(`🧙 Has wizard content on /project/wizard: ${hasWizardContent}`);
    
    // Take final screenshot
    await page.screenshot({
      path: 'test-results/wizard-debug-final.png',
      fullPage: true
    });

    // Report network issues
    if (networkFailures.length > 0) {
      console.log('\n🌐 NETWORK FAILURES:');
      networkFailures.forEach((failure, index) => {
        console.log(`  ${index + 1}: ${failure}`);
      });
    } else {
      console.log('\n🌐 No network failures detected');
    }

    if (responseErrors.length > 0) {
      console.log('\n❌ HTTP ERRORS:');
      responseErrors.forEach((error, index) => {
        console.log(`  ${index + 1}: ${error}`);
      });
    } else {
      console.log('\n✅ No HTTP errors detected');
    }

    // Report JavaScript errors
    if (jsErrors.length > 0) {
      console.log('\n💥 JAVASCRIPT CONSOLE ERRORS:');
      jsErrors.forEach((error, index) => {
        console.log(`  ${index + 1}: ${error}`);
      });
    } else {
      console.log('\n✅ No JavaScript console errors detected');
    }

    if (pageErrors.length > 0) {
      console.log('\n🚨 PAGE ERRORS (Uncaught Exceptions):');
      pageErrors.forEach((error, index) => {
        console.log(`  ${index + 1}: ${error}`);
      });
    } else {
      console.log('\n✅ No page errors detected');
    }

    console.log('✅ Debug test completed!');
  });
});
