import { test, expect } from '@playwright/test';

test.describe('Project Wizard Debug Tests', () => {
  test('Debug Project Wizard Loading Issues', async ({ page }) => {
    console.log('🚀 Debugging project wizard loading...');
    
    // Capture console logs
    const consoleLogs = [];
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`);
      console.log(`BROWSER: ${msg.type()}: ${msg.text()}`);
    });
    
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Navigate directly to project creation wizard
    console.log('📝 Navigating to project wizard...');
    await page.goto('https://royalty.technology/#/project/create');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Wait longer to see what happens
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/wizard-debug.png',
      fullPage: true 
    });
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`🔗 Current URL: ${currentUrl}`);
    
    // Check if we're still on the wizard page or redirected
    const isOnWizard = currentUrl.includes('/project/create') || currentUrl.includes('/project/wizard');
    console.log(`🧙 Still on wizard page: ${isOnWizard}`);
    
    // Check page content
    const pageContent = await page.textContent('body');
    console.log(`📄 Page content length: ${pageContent.length}`);
    
    // Look for specific debug messages
    const hasDebugMessages = consoleLogs.some(log => log.includes('DEBUG: ProjectWizard'));
    console.log(`🐛 Has ProjectWizard debug messages: ${hasDebugMessages}`);
    
    // Look for loading messages
    const hasLoadingMessages = consoleLogs.some(log => log.includes('loading'));
    console.log(`⏳ Has loading messages: ${hasLoadingMessages}`);
    
    // Look for route matching messages
    const hasRouteMessages = consoleLogs.some(log => log.includes('/project/create route matched'));
    console.log(`🛣️ Has route matching messages: ${hasRouteMessages}`);
    
    // Check for React errors
    const hasReactErrors = consoleLogs.some(log => log.includes('Error') && log.includes('React'));
    console.log(`⚛️ Has React errors: ${hasReactErrors}`);
    
    // Print all console logs for debugging
    console.log('\n📋 ALL CONSOLE LOGS:');
    consoleLogs.forEach((log, index) => {
      console.log(`  ${index + 1}: ${log}`);
    });
    
    // Check if the wizard header is present
    const wizardHeader = await page.locator('text="Create Your Project"').isVisible();
    console.log(`🎯 Wizard header visible: ${wizardHeader}`);
    
    // Check for loading animation
    const loadingAnimation = await page.locator('[class*="loading"], [class*="spinner"]').isVisible();
    console.log(`⏳ Loading animation visible: ${loadingAnimation}`);
    
    // Try alternative routes
    console.log('\n🔄 Trying alternative routes...');
    
    // Try /project/wizard
    await page.goto('https://royalty.technology/#/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const wizardUrl = page.url();
    console.log(`🔗 Wizard URL: ${wizardUrl}`);
    
    const wizardContent = await page.textContent('body');
    const hasWizardContent = wizardContent.includes('Project Basics') || wizardContent.includes('Create Your Project');
    console.log(`🧙 Has wizard content on /project/wizard: ${hasWizardContent}`);
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/wizard-debug-final.png',
      fullPage: true 
    });
    
    console.log('✅ Debug test completed!');
  });
});
