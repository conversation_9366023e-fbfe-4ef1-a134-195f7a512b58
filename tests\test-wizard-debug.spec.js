import { test, expect } from '@playwright/test';

test.describe('Project Wizard Debug Tests', () => {
  test('Debug Project Wizard Loading Issues', async ({ page }) => {
    console.log('🚀 Debugging project wizard loading...');
    
    // Capture console logs
    const consoleLogs = [];
    const jsErrors = [];
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`);
      console.log(`BROWSER: ${msg.type()}: ${msg.text()}`);

      // Capture JavaScript errors specifically
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    // Capture page errors (uncaught exceptions)
    const pageErrors = [];
    page.on('pageerror', error => {
      pageErrors.push(error.message);
      console.log(`PAGE ERROR: ${error.message}`);
    });

    // Capture network failures
    const networkFailures = [];
    page.on('requestfailed', request => {
      networkFailures.push(`${request.method()} ${request.url()} - ${request.failure()?.errorText}`);
    });

    // Capture response errors
    const responseErrors = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        responseErrors.push(`${response.status()} ${response.url()}`);
      }
    });
    
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    // Check if main JavaScript bundle loaded
    const jsLoaded = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      const mainScript = scripts.find(script => script.src.includes('main-') || script.src.includes('assets/'));
      return {
        hasMainScript: !!mainScript,
        mainScriptSrc: mainScript?.src || 'none',
        totalScripts: scripts.length,
        allScripts: scripts.map(s => s.src)
      };
    });
    console.log('📦 JavaScript bundle info:', jsLoaded);
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Navigate to Start page and click the create project button
    console.log('📝 Going to Start page...');
    await page.goto('https://royalty.technology/#/start');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Take a screenshot to see what's actually displayed
    await page.screenshot({ path: 'test-results/start-page-debug.png', fullPage: true });
    console.log('📸 Screenshot saved to test-results/start-page-debug.png');

    // Log all visible text on the page
    const pageText = await page.textContent('body');
    console.log('📄 Page text content:');
    console.log(pageText.substring(0, 500) + '...');

    // Check for tabs
    const tabs = await page.locator('[role="tab"], .tab, [data-key]').all();
    console.log(`📑 Found ${tabs.length} tabs`);
    for (let i = 0; i < tabs.length; i++) {
      const tabText = await tabs[i].textContent();
      console.log(`  Tab ${i + 1}: "${tabText}"`);
    }

    // Follow the correct user flow: Start button -> Start Project Wizard button
    console.log('🔍 Following correct user flow...');

    let wizardReached = false;

    // Step 1: Click the "Start" button first
    console.log('🎯 Step 1: Looking for Start button...');
    try {
      const startButton = page.locator('button, a').filter({ hasText: /^Start$/i }).first();
      await startButton.waitFor({ timeout: 5000 });
      console.log('✅ Found "Start" button, clicking...');
      await startButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Step 2: Now look for "Start Project Wizard" button
      console.log('🎯 Step 2: Looking for "Start Project Wizard" button...');
      try {
        const startWizardButton = page.locator('button, a').filter({ hasText: /Start Project Wizard/i });
        await startWizardButton.waitFor({ timeout: 5000 });
        console.log('✅ Found "Start Project Wizard" button, clicking...');
        await startWizardButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        wizardReached = true;
      } catch (error) {
        console.log('⚠️ "Start Project Wizard" button not found after clicking Start');

        // Look for any wizard-related buttons
        const wizardButtons = await page.locator('button, a').filter({ hasText: /wizard|create.*project|new.*project/i }).all();
        console.log(`🔍 Found ${wizardButtons.length} potential wizard buttons after Start click`);

        for (let i = 0; i < Math.min(wizardButtons.length, 3); i++) {
          const buttonText = await wizardButtons[i].textContent();
          console.log(`  Wizard Button ${i + 1}: "${buttonText}"`);
        }

        if (wizardButtons.length > 0) {
          console.log('🎯 Trying first wizard button...');
          await wizardButtons[0].click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          wizardReached = true;
        }
      }
    } catch (error) {
      console.log('⚠️ "Start" button not found, trying direct wizard search...');

      // Fallback: Look directly for project wizard button
      try {
        const projectWizardButton = page.locator('button, a').filter({ hasText: /Start Project Wizard|Create Project|New Project/i });
        await projectWizardButton.waitFor({ timeout: 5000 });
        console.log('✅ Found project wizard button directly, clicking...');
        await projectWizardButton.first().click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        wizardReached = true;
      } catch (error) {
        console.log('⚠️ No project wizard buttons found anywhere');
      }
    }

    if (!wizardReached) {
      console.log('❌ Could not reach project wizard through UI, test may need manual verification');
    }

    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Check if we're redirected to login
    const afterNavigateUrl = page.url();
    console.log(`🔗 URL after navigation: ${afterNavigateUrl}`);

    // If redirected to login, login again
    if (afterNavigateUrl.includes('/login')) {
      console.log('🔐 Redirected to login, logging in again...');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Try navigating to wizard again
      await page.goto('https://royalty.technology/#/project/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/wizard-debug.png',
      fullPage: true 
    });
    
    // Check current URL
    const currentUrl = page.url();
    console.log(`🔗 Current URL: ${currentUrl}`);
    
    // Check if we're still on the wizard page or redirected
    const isOnWizard = currentUrl.includes('/project/create') || currentUrl.includes('/project/wizard');
    console.log(`🧙 Still on wizard page: ${isOnWizard}`);
    
    // Check page content
    const pageContent = await page.textContent('body');
    console.log(`📄 Page content length: ${pageContent.length}`);
    console.log('📄 First 200 chars of wizard page content:');
    console.log(pageContent.substring(0, 200));

    // Take a screenshot of the wizard page
    await page.screenshot({ path: 'test-results/wizard-page-content.png', fullPage: true });
    console.log('📸 Wizard page content screenshot saved');
    
    // Look for specific debug messages
    const hasDebugMessages = consoleLogs.some(log => log.includes('DEBUG: ProjectWizard'));
    console.log(`🐛 Has ProjectWizard debug messages: ${hasDebugMessages}`);
    
    // Look for loading messages
    const hasLoadingMessages = consoleLogs.some(log => log.includes('loading'));
    console.log(`⏳ Has loading messages: ${hasLoadingMessages}`);
    
    // Look for route matching messages
    const hasRouteMessages = consoleLogs.some(log => log.includes('/project/create route matched'));
    console.log(`🛣️ Has route matching messages: ${hasRouteMessages}`);
    
    // Check for React errors
    const hasReactErrors = consoleLogs.some(log => log.includes('Error') && log.includes('React'));
    console.log(`⚛️ Has React errors: ${hasReactErrors}`);

    // Check for JavaScript execution
    const hasJavaScriptExecution = consoleLogs.some(log =>
      log.includes('JavaScript is executing') ||
      log.includes('React root created') ||
      log.includes('React app render')
    );
    console.log(`🚨 Has JavaScript execution: ${hasJavaScriptExecution}`);

    // Check for any JavaScript errors
    const hasJavaScriptErrors = consoleLogs.some(log =>
      log.includes('TypeError') ||
      log.includes('ReferenceError') ||
      log.includes('SyntaxError') ||
      log.includes('Cannot read') ||
      log.includes('is not defined')
    );
    console.log(`💥 Has JavaScript errors: ${hasJavaScriptErrors}`);
    
    // Print all console logs for debugging
    console.log('\n📋 ALL CONSOLE LOGS:');
    consoleLogs.forEach((log, index) => {
      console.log(`  ${index + 1}: ${log}`);
    });
    
    // Check if the wizard header is present (try multiple variations)
    const wizardHeader1 = await page.locator('text="Create Your Project"').isVisible();
    const wizardHeader2 = await page.locator(':text("Create Your Project")').isVisible();
    const wizardHeader3 = await page.getByText('Create Your Project').isVisible();
    const currentPageContent = await page.textContent('body');
    const wizardHeaderInContent = currentPageContent.includes('Create Your Project');

    console.log(`🎯 Wizard header visible (exact): ${wizardHeader1}`);
    console.log(`🎯 Wizard header visible (contains): ${wizardHeader2}`);
    console.log(`🎯 Wizard header visible (getByText): ${wizardHeader3}`);
    console.log(`🎯 Wizard header in content: ${wizardHeaderInContent}`);
    
    // Check for loading animation
    const loadingAnimation = await page.locator('[class*="loading"], [class*="spinner"]').isVisible();
    console.log(`⏳ Loading animation visible: ${loadingAnimation}`);

    // Check for wizard form elements
    const hasInputFields = await page.locator('input').count();
    const hasButtons = await page.locator('button').count();
    const hasFormElements = await page.locator('form, [role="form"]').count();

    console.log(`📝 Input fields found: ${hasInputFields}`);
    console.log(`🔘 Buttons found: ${hasButtons}`);
    console.log(`📋 Form elements found: ${hasFormElements}`);
    
    // Try alternative routes
    console.log('\n🔄 Trying alternative routes...');
    
    // Try /project/wizard
    await page.goto('https://royalty.technology/#/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const wizardUrl = page.url();
    console.log(`🔗 Wizard URL: ${wizardUrl}`);
    
    const wizardContent = await page.textContent('body');
    const hasWizardContent = wizardContent.includes('Project Basics') || wizardContent.includes('Create Your Project');
    console.log(`🧙 Has wizard content on /project/wizard: ${hasWizardContent}`);
    
    // Take final screenshot
    await page.screenshot({
      path: 'test-results/wizard-debug-final.png',
      fullPage: true
    });

    // Report network issues
    if (networkFailures.length > 0) {
      console.log('\n🌐 NETWORK FAILURES:');
      networkFailures.forEach((failure, index) => {
        console.log(`  ${index + 1}: ${failure}`);
      });
    } else {
      console.log('\n🌐 No network failures detected');
    }

    if (responseErrors.length > 0) {
      console.log('\n❌ HTTP ERRORS:');
      responseErrors.forEach((error, index) => {
        console.log(`  ${index + 1}: ${error}`);
      });
    } else {
      console.log('\n✅ No HTTP errors detected');
    }

    // Report JavaScript errors
    if (jsErrors.length > 0) {
      console.log('\n💥 JAVASCRIPT CONSOLE ERRORS:');
      jsErrors.forEach((error, index) => {
        console.log(`  ${index + 1}: ${error}`);
      });
    } else {
      console.log('\n✅ No JavaScript console errors detected');
    }

    if (pageErrors.length > 0) {
      console.log('\n🚨 PAGE ERRORS (Uncaught Exceptions):');
      pageErrors.forEach((error, index) => {
        console.log(`  ${index + 1}: ${error}`);
      });
    } else {
      console.log('\n✅ No page errors detected');
    }

    console.log('✅ Debug test completed!');
  });
});
