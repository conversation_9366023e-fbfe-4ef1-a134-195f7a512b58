import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate using the immersive auth flow
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');

  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  // Check if we're already authenticated by looking for login form
  const hasLoginForm = await page.locator('input[type="email"]').isVisible();

  if (!hasLoginForm) {
    console.log('✅ Already authenticated');
    return true;
  }

  console.log('📝 Need to authenticate - filling login form...');

  const hasLoginButton = await page.locator('text="Log In"').isVisible();
  if (!hasLoginButton) {
    throw new Error('Log In button not found on auth landing page');
  }
  
  const hasEmailInput = await page.locator('input[type="email"]').isVisible();
  const hasPasswordInput = await page.locator('input[type="password"]').isVisible();
  
  if (!hasEmailInput || !hasPasswordInput) {
    throw new Error('Login form not found after clicking LOGIN');
  }
  
  console.log('📝 Filling in credentials...');
  await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
  await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
  
  const submitButton = page.locator('button[type="submit"]').first();
  await submitButton.click();
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  const stillHasSignInButton = await page.locator('text="Sign In"').isVisible();
  if (stillHasSignInButton) {
    throw new Error('Authentication failed - Sign In button still visible');
  }
  
  console.log('✅ Authentication successful');
  return true;
}

test.describe('Comprehensive Platform Audit', () => {
  test('Complete functionality audit', async ({ page }) => {
    console.log('🔍 Starting comprehensive platform audit...');
    
    await authenticate(page);
    
    const auditResults = {
      authentication: { status: 'working', details: 'Authentication flow works correctly' },
      navigation: {},
      pages: {},
      userInterface: {},
      mobile: {},
      errors: []
    };
    
    // Test Dashboard/Home
    console.log('\n📊 Testing Dashboard...');
    await page.goto(`${PRODUCTION_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const dashboardUrl = page.url();
    const dashboardLoads = !dashboardUrl.includes('/login');
    auditResults.pages.dashboard = {
      status: dashboardLoads ? 'working' : 'broken',
      url: dashboardUrl,
      details: dashboardLoads ? 'Dashboard loads correctly' : 'Dashboard redirects to login'
    };
    console.log(`Dashboard: ${dashboardLoads ? '✅ WORKING' : '❌ BROKEN'} - ${dashboardUrl}`);
    
    // Test Navigation Buttons
    console.log('\n🧭 Testing Navigation...');
    const navButtons = ['Start', 'Track', 'Earn'];
    
    for (const buttonName of navButtons) {
      try {
        const buttonExists = await page.locator(`button:has-text("${buttonName}")`).first().isVisible();
        console.log(`${buttonName} button visible: ${buttonExists ? '✅' : '❌'}`);
        
        if (buttonExists) {
          // Try clicking the button
          await page.locator(`button:has-text("${buttonName}")`).first().click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);
          
          const currentUrl = page.url();
          const expectedPath = `/${buttonName.toLowerCase()}`;
          const correctNavigation = currentUrl.includes(expectedPath);
          
          auditResults.navigation[buttonName.toLowerCase()] = {
            status: correctNavigation ? 'working' : 'broken',
            url: currentUrl,
            expected: expectedPath,
            details: correctNavigation ? 'Navigation works correctly' : `Expected ${expectedPath}, got ${currentUrl}`
          };
          
          console.log(`${buttonName} navigation: ${correctNavigation ? '✅ WORKING' : '❌ BROKEN'} - ${currentUrl}`);
          
          // Check for page-specific content/errors
          const pageText = await page.textContent('body');
          if (pageText.includes('Failed to initialize')) {
            auditResults.errors.push(`${buttonName} page has initialization error`);
            console.log(`⚠️ ${buttonName} page has initialization error`);
          }
          if (pageText.includes('Under Construction')) {
            auditResults.errors.push(`${buttonName} page shows "Under Construction"`);
            console.log(`⚠️ ${buttonName} page shows "Under Construction"`);
          }
          
        } else {
          auditResults.navigation[buttonName.toLowerCase()] = {
            status: 'broken',
            details: 'Button not visible'
          };
        }
      } catch (error) {
        auditResults.navigation[buttonName.toLowerCase()] = {
          status: 'broken',
          error: error.message,
          details: `Error clicking ${buttonName}: ${error.message}`
        };
        console.log(`❌ Error testing ${buttonName}: ${error.message}`);
      }
    }
    
    // Test User Interface Elements
    console.log('\n👤 Testing User Interface...');
    await page.goto(`${PRODUCTION_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check for user dropdown/profile
    const userDropdownSelectors = [
      'button:has-text("the")',
      '[data-testid="user-menu"]',
      '.user-menu',
      'button[aria-expanded]',
      '[class*="dropdown"]'
    ];
    
    let userDropdownFound = false;
    for (const selector of userDropdownSelectors) {
      if (await page.locator(selector).isVisible()) {
        userDropdownFound = true;
        console.log(`✅ User dropdown found: ${selector}`);
        break;
      }
    }
    
    auditResults.userInterface.userDropdown = {
      status: userDropdownFound ? 'working' : 'broken',
      details: userDropdownFound ? 'User dropdown/menu found' : 'No user dropdown or profile menu found'
    };
    
    // Test Project Creation
    console.log('\n🚀 Testing Project Creation...');
    await page.goto(`${PRODUCTION_URL}/project/create`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const projectCreateUrl = page.url();
    const projectCreateWorks = projectCreateUrl.includes('/project/create');
    
    if (projectCreateWorks) {
      const hasTraditionalWizard = await page.locator('text="Use Traditional Wizard"').isVisible();
      const hasEnhancedWizard = await page.locator('text="Enhanced Project Wizard"').isVisible();
      const hasCreateProject = await page.locator('text="🚀 Create Your Project"').isVisible();
      
      auditResults.pages.projectCreate = {
        status: (hasTraditionalWizard || hasEnhancedWizard || hasCreateProject) ? 'working' : 'partial',
        url: projectCreateUrl,
        features: {
          traditionalWizard: hasTraditionalWizard,
          enhancedWizard: hasEnhancedWizard,
          createProject: hasCreateProject
        },
        details: 'Project creation page loads but may have missing elements'
      };
      
      console.log(`Project Creation: ${hasTraditionalWizard || hasEnhancedWizard ? '✅ WORKING' : '⚠️ PARTIAL'}`);
      console.log(`  - Traditional Wizard: ${hasTraditionalWizard ? '✅' : '❌'}`);
      console.log(`  - Enhanced Wizard: ${hasEnhancedWizard ? '✅' : '❌'}`);
      console.log(`  - Create Project text: ${hasCreateProject ? '✅' : '❌'}`);
    } else {
      auditResults.pages.projectCreate = {
        status: 'broken',
        url: projectCreateUrl,
        details: 'Project creation page does not load correctly'
      };
      console.log(`❌ Project Creation page broken - redirected to ${projectCreateUrl}`);
    }
    
    // Test Mobile Navigation
    console.log('\n📱 Testing Mobile Navigation...');
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone size
    await page.goto(`${PRODUCTION_URL}/dashboard`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
    
    const mobileNavButtons = await Promise.all([
      page.locator('button:has-text("Start")').first().isVisible(),
      page.locator('button:has-text("Track")').first().isVisible(),
      page.locator('button:has-text("Earn")').first().isVisible()
    ]);
    
    const mobileMenuSelectors = [
      '[data-testid="mobile-menu"]',
      '.mobile-menu',
      'button[aria-label*="menu"]',
      'button[aria-label*="Menu"]',
      '[class*="hamburger"]'
    ];
    
    let mobileMenuFound = false;
    for (const selector of mobileMenuSelectors) {
      if (await page.locator(selector).isVisible()) {
        mobileMenuFound = true;
        break;
      }
    }
    
    const visibleMobileButtons = mobileNavButtons.filter(Boolean).length;
    auditResults.mobile = {
      status: (visibleMobileButtons > 0 || mobileMenuFound) ? 'working' : 'broken',
      visibleButtons: visibleMobileButtons,
      mobileMenu: mobileMenuFound,
      details: `${visibleMobileButtons} navigation buttons visible, mobile menu: ${mobileMenuFound}`
    };
    
    console.log(`Mobile Navigation: ${visibleMobileButtons > 0 || mobileMenuFound ? '✅ WORKING' : '❌ BROKEN'}`);
    console.log(`  - Visible nav buttons: ${visibleMobileButtons}/3`);
    console.log(`  - Mobile menu: ${mobileMenuFound ? '✅' : '❌'}`);
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Generate summary
    console.log('\n📋 AUDIT SUMMARY:');
    console.log('==================');
    
    const workingFeatures = [];
    const brokenFeatures = [];
    const partialFeatures = [];
    
    // Categorize results
    Object.entries(auditResults).forEach(([category, data]) => {
      if (category === 'errors') return;
      
      if (typeof data === 'object' && data.status) {
        if (data.status === 'working') workingFeatures.push(`${category}: ${data.details}`);
        else if (data.status === 'partial') partialFeatures.push(`${category}: ${data.details}`);
        else brokenFeatures.push(`${category}: ${data.details}`);
      } else if (typeof data === 'object') {
        Object.entries(data).forEach(([subCategory, subData]) => {
          if (subData.status === 'working') workingFeatures.push(`${category}.${subCategory}: ${subData.details}`);
          else if (subData.status === 'partial') partialFeatures.push(`${category}.${subCategory}: ${subData.details}`);
          else brokenFeatures.push(`${category}.${subCategory}: ${subData.details}`);
        });
      }
    });
    
    console.log(`\n✅ WORKING FEATURES (${workingFeatures.length}):`);
    workingFeatures.forEach(feature => console.log(`  - ${feature}`));
    
    console.log(`\n⚠️ PARTIAL FEATURES (${partialFeatures.length}):`);
    partialFeatures.forEach(feature => console.log(`  - ${feature}`));
    
    console.log(`\n❌ BROKEN FEATURES (${brokenFeatures.length}):`);
    brokenFeatures.forEach(feature => console.log(`  - ${feature}`));
    
    console.log(`\n🚨 ERRORS FOUND (${auditResults.errors.length}):`);
    auditResults.errors.forEach(error => console.log(`  - ${error}`));
    
    // Save detailed results
    await page.evaluate((results) => {
      window.auditResults = results;
    }, auditResults);
    
    console.log('\n📸 Taking final screenshot...');
    await page.screenshot({ path: 'test-results/comprehensive-audit-final.png', fullPage: true });
    
    console.log('\n✅ Comprehensive audit completed!');
  });
});
