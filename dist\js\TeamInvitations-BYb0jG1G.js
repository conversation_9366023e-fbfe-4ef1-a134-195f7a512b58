var L=Object.defineProperty,M=Object.defineProperties;var T=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var k=(i,a,s)=>a in i?L(i,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[a]=s,q=(i,a)=>{for(var s in a||(a={}))U.call(a,s)&&k(i,s,a[s]);if(S)for(var s of S(a))z.call(a,s)&&k(i,s,a[s]);return i},E=(i,a)=>M(i,T(a));var N=(i,a,s)=>new Promise((x,w)=>{var v=l=>{try{h(s.next(l))}catch(o){w(o)}},b=l=>{try{h(s.throw(l))}catch(o){w(o)}},h=l=>l.done?x(l.value):Promise.resolve(l.value).then(v,b);h((s=s.apply(i,a)).next())});import{r as p,j as e,c as g,b as _,m as I,h as m,i as F}from"./chunk-DX4Z_LyS.js";import{U as H,s as j,L as O}from"../assets/main-CGUKzV0x.js";import{c as f}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const W=({canvasId:i,sectionId:a})=>{const{currentUser:s}=p.useContext(H),[x,w]=p.useState([]),[v,b]=p.useState([]),[h,l]=p.useState(!0);p.useEffect(()=>{s&&o()},[s]);const o=()=>N(null,null,function*(){try{const{data:t,error:n}=yield j.from("team_invitations").select(`
          *,
          teams:team_id (
            id,
            name,
            description,
            studio_type
          ),
          inviter:invited_by (
            email,
            user_metadata
          )
        `).eq("invited_user_id",s.id).eq("status","pending");n||w(t||[]);const{data:r,error:c}=yield j.from("team_invitations").select(`
          *,
          teams:team_id (
            id,
            name,
            description,
            studio_type
          ),
          invitee:invited_user_id (
            email,
            user_metadata
          )
        `).eq("invited_by",s.id).in("status",["pending","accepted","declined"]);c||b(r||[])}catch(t){}finally{l(!1)}}),C=(t,n)=>N(null,null,function*(){try{const{error:r}=yield j.from("team_invitations").update({status:n,responded_at:new Date().toISOString()}).eq("id",t);if(r)throw r;if(n==="accepted"){const c=x.find(u=>u.id===t),{error:d}=yield j.from("team_members").insert({team_id:c.team_id,user_id:s.id,role:c.role||"member",is_admin:c.role==="admin"});if(d)throw d;f.success("Invitation accepted! You are now a member of the team.")}else f.success("Invitation declined.");o()}catch(r){f.error("Failed to respond to invitation")}}),R=t=>{switch(t){case"pending":return"warning";case"accepted":return"success";case"declined":return"danger";default:return"default"}},D=t=>{switch(t){case"pending":return"⏳";case"accepted":return"✅";case"declined":return"❌";default:return"📧"}};return h?e.jsx("div",{className:"p-6",children:e.jsx(g,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsx(_,{className:"p-8",children:e.jsx(O,{})})})}):e.jsxs(I.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"p-6 space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-white mb-2",children:"📧 Team Invitations"}),e.jsx("p",{className:"text-white/70",children:"Manage your team invitations and collaboration requests"})]}),e.jsx(g,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(_,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"📥 Received Invitations"}),x.length>0?e.jsx("div",{className:"space-y-4",children:x.map(t=>{var n,r,c,d;return e.jsx(I.div,{whileHover:{scale:1.02},className:"bg-white/5 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-3xl",children:"🏰"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-white",children:(n=t.teams)==null?void 0:n.name}),e.jsxs("p",{className:"text-white/70 text-sm",children:["Invited by: ",((c=(r=t.inviter)==null?void 0:r.user_metadata)==null?void 0:c.full_name)||((d=t.inviter)==null?void 0:d.email)]}),e.jsxs("p",{className:"text-white/60 text-sm",children:["Role: ",t.role||"Member"]}),t.message&&e.jsxs("p",{className:"text-white/60 text-sm mt-2 italic",children:['"',t.message,'"']})]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(m,{color:"success",size:"sm",startContent:"✅",onClick:()=>C(t.id,"accepted"),children:"Accept"}),e.jsx(m,{color:"danger",variant:"flat",size:"sm",startContent:"❌",onClick:()=>C(t.id,"declined"),children:"Decline"})]})]})},t.id)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📭"}),e.jsx("p",{className:"text-white/70",children:"No pending invitations"})]})]})}),e.jsx(g,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(_,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"📤 Sent Invitations"}),v.length>0?e.jsx("div",{className:"space-y-4",children:v.map(t=>{var n,r,c,d;return e.jsx(I.div,{whileHover:{scale:1.02},className:"bg-white/5 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-3xl",children:D(t.status)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-white",children:(n=t.teams)==null?void 0:n.name}),e.jsxs("p",{className:"text-white/70 text-sm",children:["Invited: ",((c=(r=t.invitee)==null?void 0:r.user_metadata)==null?void 0:c.full_name)||((d=t.invitee)==null?void 0:d.email)]}),e.jsxs("p",{className:"text-white/60 text-sm",children:["Role: ",t.role||"Member"]}),e.jsxs("p",{className:"text-white/60 text-xs",children:["Sent: ",new Date(t.created_at).toLocaleDateString()]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(F,{color:R(t.status),variant:"flat",children:t.status.charAt(0).toUpperCase()+t.status.slice(1)}),t.status==="pending"&&e.jsx(m,{color:"warning",variant:"flat",size:"sm",onClick:()=>N(null,null,function*(){try{const{error:u}=yield j.from("team_invitations").update({status:"cancelled"}).eq("id",t.id);if(u)throw u;f.success("Invitation cancelled successfully"),b(A=>A.map(y=>y.id===t.id?E(q({},y),{status:"cancelled"}):y))}catch(u){f.error("Failed to cancel invitation")}}),children:"Cancel"})]})]})},t.id)})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📤"}),e.jsx("p",{className:"text-white/70",children:"No invitations sent"})]})]})}),e.jsx(g,{className:"bg-white/10 backdrop-blur-md border-white/20",children:e.jsxs(_,{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"🚀 Quick Actions"}),e.jsxs("div",{className:"flex flex-wrap gap-4 justify-center",children:[e.jsx(m,{color:"primary",variant:"solid",startContent:"🏰",onClick:()=>window.location.href="/teams/create",children:"Create New Studio"}),e.jsx(m,{color:"secondary",variant:"solid",startContent:"🔍",onClick:()=>window.location.href="/teams/discover",children:"Discover Teams"}),e.jsx(m,{color:"success",variant:"solid",startContent:"👥",onClick:()=>window.location.href="/teams",children:"View My Teams"})]})]})})]})};export{W as default};
