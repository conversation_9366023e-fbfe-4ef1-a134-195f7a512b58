var A=Object.defineProperty,$=Object.defineProperties;var q=Object.getOwnPropertyDescriptors;var b=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var N=(a,s,r)=>s in a?A(a,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[s]=r,g=(a,s)=>{for(var r in s||(s={}))S.call(s,r)&&N(a,r,s[r]);if(b)for(var r of b(s))U.call(s,r)&&N(a,r,s[r]);return a},j=(a,s)=>$(a,q(s));var f=(a,s,r)=>new Promise((y,x)=>{var l=c=>{try{u(r.next(c))}catch(h){x(h)}},d=c=>{try{u(r.throw(c))}catch(h){x(h)}},u=c=>c.done?y(c.value):Promise.resolve(c.value).then(l,d);u((r=r.apply(a,s)).next())});import{r as p,j as e,m as w,c as E,w as R,i as I,h as _,b as M,A as z}from"./chunk-DX4Z_LyS.js";import{U as D,s as v}from"../assets/main-CGUKzV0x.js";import{j as L}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const J=()=>{const{currentUser:a}=p.useContext(D),[s,r]=p.useState([]),[y,x]=p.useState(!0),[l,d]=p.useState(0);p.useEffect(()=>{f(null,null,function*(){if(a)try{const{data:i,error:o}=yield v.from("notifications").select("*").eq("user_id",a.id).order("created_at",{ascending:!1}).limit(20);if(o){r([]),d(0);return}const m=(i==null?void 0:i.map(n=>({id:n.id,type:n.type,title:n.title,message:n.message,timestamp:new Date(n.created_at),read:n.is_read,icon:u(n.type),color:c(n.type)})))||[];r(m),d(m.filter(n=>!n.read).length)}catch(i){}finally{x(!1)}})},[a]);const u=t=>({contribution_approved:"✅",project_invitation:"📨",revenue_update:"💰",system_update:"🔄",team_invitation:"👥",milestone_completed:"🎯",skill_verified:"🏆",achievement_unlocked:"🏅",message_received:"💬",payment_received:"💳"})[t]||"📢",c=t=>({contribution_approved:"success",project_invitation:"primary",revenue_update:"warning",system_update:"secondary",team_invitation:"primary",milestone_completed:"success",skill_verified:"success",achievement_unlocked:"warning",message_received:"primary",payment_received:"success"})[t]||"default",h=t=>f(null,null,function*(){try{const{error:i}=yield v.from("notifications").update({is_read:!0}).eq("id",t);if(i)throw i;r(o=>o.map(m=>m.id===t?j(g({},m),{read:!0}):m)),d(o=>Math.max(0,o-1))}catch(i){}}),k=()=>f(null,null,function*(){try{const{error:t}=yield v.from("notifications").update({is_read:!0}).eq("user_id",a.id).eq("is_read",!1);if(t)throw t;r(i=>i.map(o=>j(g({},o),{read:!0}))),d(0)}catch(t){}}),C=t=>{switch(t){case"contribution_approved":return"from-green-500 to-emerald-500";case"project_invitation":return"from-blue-500 to-cyan-500";case"revenue_update":return"from-yellow-500 to-orange-500";case"system_update":return"from-purple-500 to-pink-500";default:return"from-gray-500 to-slate-500"}};return y?e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsx(w.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"})}):e.jsx("div",{className:"p-6",children:e.jsx(w.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsxs(E,{className:"bg-white/10 backdrop-blur-md border-white/20",children:[e.jsx(R,{children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center relative",children:[e.jsx("span",{className:"text-xl",children:"🔔"}),l>0&&e.jsx(I,{content:l,color:"danger",className:"absolute -top-1 -right-1"})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Notifications"}),e.jsx("p",{className:"text-white/60 text-sm",children:l>0?`${l} unread notifications`:"All caught up!"})]})]}),l>0&&e.jsx(_,{size:"sm",variant:"flat",className:"bg-white/10 text-white hover:bg-white/20",onClick:k,children:"Mark all read"})]})}),e.jsxs(M,{children:[s.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("span",{className:"text-6xl mb-4 block",children:"🔕"}),e.jsx("h3",{className:"text-white text-lg font-medium mb-2",children:"No notifications"}),e.jsx("p",{className:"text-white/60",children:"You're all caught up! Check back later for updates."})]}):e.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:e.jsx(z,{children:s.map((t,i)=>e.jsxs(w.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3,delay:.05*i},className:`
                        flex items-start gap-4 p-4 rounded-lg transition-all duration-200 cursor-pointer
                        ${t.read?"bg-white/5 hover:bg-white/10":"bg-white/10 hover:bg-white/15 border border-white/20"}
                      `,onClick:()=>!t.read&&h(t.id),children:[e.jsx("div",{className:`w-10 h-10 rounded-lg bg-gradient-to-r ${C(t.type)} flex items-center justify-center flex-shrink-0`,children:e.jsx("span",{className:"text-lg",children:t.icon})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-start justify-between gap-2",children:[e.jsx("h4",{className:`font-medium ${t.read?"text-white/70":"text-white"}`,children:t.title}),!t.read&&e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"})]}),e.jsx("p",{className:`text-sm mt-1 ${t.read?"text-white/50":"text-white/70"}`,children:t.message}),e.jsx("p",{className:"text-white/40 text-xs mt-2",children:L(t.timestamp,{addSuffix:!0})})]})]},t.id))})}),s.length>0&&e.jsx(w.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.5},className:"mt-4 pt-4 border-t border-white/10 text-center",children:e.jsx(_,{variant:"flat",className:"bg-white/10 text-white hover:bg-white/20",onClick:()=>window.location.href="/notifications",children:"View All Notifications"})})]})]})})})};export{J as default};
