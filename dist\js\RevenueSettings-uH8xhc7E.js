var W=Object.defineProperty,D=Object.defineProperties;var _=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var P=(l,i,n)=>i in l?W(l,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):l[i]=n,p=(l,i)=>{for(var n in i||(i={}))F.call(i,n)&&P(l,n,i[n]);if(S)for(var n of S(i))z.call(i,n)&&P(l,n,i[n]);return l},v=(l,i)=>D(l,_(i));var R=(l,i,n)=>new Promise((u,d)=>{var h=t=>{try{m(n.next(t))}catch(x){d(x)}},a=t=>{try{m(n.throw(t))}catch(x){d(x)}},m=t=>t.done?u(t.value):Promise.resolve(t.value).then(h,a);m((n=n.apply(l,i)).next())});import{r as y,j as e,i as E,c as g,w as j,b as f,u as N,v as c,p as o,s as b,t as r,h as w}from"./chunk-DX4Z_LyS.js";import{U as M}from"../assets/main-CGUKzV0x.js";import{e as U,aD as B,av as G,aB as $,Q as q,B as K,f as O,z as H,ac as I,d as L}from"./chunk-BiNxGM8y.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-D8IZ3rty.js";const te=({className:l=""})=>{const{currentUser:i}=y.useContext(M),[n,u]=y.useState(!1),[d,h]=y.useState(!1),[a,m]=y.useState({payment:{preferredMethod:"bank_transfer",minimumPayout:100,autoWithdraw:!1,autoWithdrawThreshold:500,currency:"USD",taxWithholding:!1,taxRate:0},notifications:{paymentReceived:!0,payoutProcessed:!0,goalAchieved:!0,weeklyReports:!0,monthlyReports:!0,thresholdAlerts:!0,thresholdAmount:1e3},privacy:{showEarnings:!1,showGoals:!0,showRanking:!0,publicProfile:!1,shareAnalytics:!0},goals:{monthlyTarget:4e3,yearlyTarget:48e3,orbTarget:5e3,trackProgress:!0,reminderFrequency:"weekly"},orb:{autoConvert:!1,conversionThreshold:1e3,conversionPercentage:50,stakingEnabled:!1,stakingAmount:0}}),t=(s,k,T)=>{m(C=>v(p({},C),{[s]:v(p({},C[s]),{[k]:T})})),h(!0)},x=()=>R(null,null,function*(){try{u(!0),yield new Promise(s=>setTimeout(s,1e3)),h(!1),alert("Settings saved successfully!")}catch(s){alert("Failed to save settings. Please try again.")}finally{u(!1)}}),A=()=>{confirm("Are you sure you want to reset all settings to default?")&&(m({payment:{preferredMethod:"bank_transfer",minimumPayout:100,autoWithdraw:!1,autoWithdrawThreshold:500,currency:"USD",taxWithholding:!1,taxRate:0},notifications:{paymentReceived:!0,payoutProcessed:!0,goalAchieved:!0,weeklyReports:!0,monthlyReports:!0,thresholdAlerts:!0,thresholdAmount:1e3},privacy:{showEarnings:!1,showGoals:!0,showRanking:!0,publicProfile:!1,shareAnalytics:!0},goals:{monthlyTarget:4e3,yearlyTarget:48e3,orbTarget:5e3,trackProgress:!0,reminderFrequency:"weekly"},orb:{autoConvert:!1,conversionThreshold:1e3,conversionPercentage:50,stakingEnabled:!1,stakingAmount:0}}),h(!0))};return e.jsxs("div",{className:`revenue-settings space-y-6 ${l}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[e.jsx(U,{className:"text-gray-600",size:28}),"Revenue Settings"]}),e.jsx("p",{className:"text-gray-600",children:"Configure your revenue preferences and payment settings"})]}),d&&e.jsx(E,{color:"warning",variant:"flat",children:"Unsaved Changes"})]}),e.jsxs(g,{children:[e.jsx(j,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"text-green-500",size:20}),e.jsx("h3",{className:"text-lg font-semibold",children:"Payment & Withdrawal"})]})}),e.jsx(f,{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs(N,{label:"Preferred Payment Method",selectedKeys:[a.payment.preferredMethod],onSelectionChange:s=>t("payment","preferredMethod",Array.from(s)[0]),children:[e.jsx(c,{startContent:e.jsx(G,{size:16}),children:"Bank Transfer"},"bank_transfer"),e.jsx(c,{startContent:e.jsx($,{size:16}),children:"PayPal"},"paypal"),e.jsx(c,{startContent:e.jsx(q,{size:16}),children:"Cryptocurrency"},"crypto")]}),e.jsxs(N,{label:"Currency",selectedKeys:[a.payment.currency],onSelectionChange:s=>t("payment","currency",Array.from(s)[0]),children:[e.jsx(c,{children:"USD ($)"},"USD"),e.jsx(c,{children:"EUR (€)"},"EUR"),e.jsx(c,{children:"GBP (£)"},"GBP"),e.jsx(c,{children:"CAD ($)"},"CAD")]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(o,{label:"Minimum Payout Amount",type:"number",value:a.payment.minimumPayout.toString(),onChange:s=>t("payment","minimumPayout",parseFloat(s.target.value)||0),startContent:"$",description:"Minimum amount before payout is processed"}),e.jsx(o,{label:"Auto-Withdraw Threshold",type:"number",value:a.payment.autoWithdrawThreshold.toString(),onChange:s=>t("payment","autoWithdrawThreshold",parseFloat(s.target.value)||0),startContent:"$",description:"Automatically withdraw when balance reaches this amount",isDisabled:!a.payment.autoWithdraw})]}),e.jsx(b,{}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Auto-Withdraw"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Automatically withdraw funds when threshold is reached"})]}),e.jsx(r,{isSelected:a.payment.autoWithdraw,onChange:s=>t("payment","autoWithdraw",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Tax Withholding"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Automatically withhold taxes from payments"})]}),e.jsx(r,{isSelected:a.payment.taxWithholding,onChange:s=>t("payment","taxWithholding",s)})]}),a.payment.taxWithholding&&e.jsx(o,{label:"Tax Rate (%)",type:"number",value:a.payment.taxRate.toString(),onChange:s=>t("payment","taxRate",parseFloat(s.target.value)||0),endContent:"%",max:"50",min:"0"})]})})]}),e.jsxs(g,{children:[e.jsx(j,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(K,{className:"text-blue-500",size:20}),e.jsx("h3",{className:"text-lg font-semibold",children:"Notifications"})]})}),e.jsx(f,{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Payment Received"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Notify when payments are received"})]}),e.jsx(r,{isSelected:a.notifications.paymentReceived,onChange:s=>t("notifications","paymentReceived",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Payout Processed"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Notify when payouts are processed"})]}),e.jsx(r,{isSelected:a.notifications.payoutProcessed,onChange:s=>t("notifications","payoutProcessed",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Goal Achieved"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Notify when revenue goals are achieved"})]}),e.jsx(r,{isSelected:a.notifications.goalAchieved,onChange:s=>t("notifications","goalAchieved",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Weekly Reports"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Receive weekly revenue summaries"})]}),e.jsx(r,{isSelected:a.notifications.weeklyReports,onChange:s=>t("notifications","weeklyReports",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Monthly Reports"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Receive detailed monthly reports"})]}),e.jsx(r,{isSelected:a.notifications.monthlyReports,onChange:s=>t("notifications","monthlyReports",s)})]}),e.jsx(b,{}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Threshold Alerts"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Alert when earnings reach specified amount"})]}),e.jsx(r,{isSelected:a.notifications.thresholdAlerts,onChange:s=>t("notifications","thresholdAlerts",s)})]}),a.notifications.thresholdAlerts&&e.jsx(o,{label:"Alert Threshold",type:"number",value:a.notifications.thresholdAmount.toString(),onChange:s=>t("notifications","thresholdAmount",parseFloat(s.target.value)||0),startContent:"$"})]})})]}),e.jsxs(g,{children:[e.jsx(j,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(O,{className:"text-purple-500",size:20}),e.jsx("h3",{className:"text-lg font-semibold",children:"Privacy & Visibility"})]})}),e.jsx(f,{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Show Earnings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Display earnings on public profile"})]}),e.jsx(r,{isSelected:a.privacy.showEarnings,onChange:s=>t("privacy","showEarnings",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Show Goals"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Display revenue goals publicly"})]}),e.jsx(r,{isSelected:a.privacy.showGoals,onChange:s=>t("privacy","showGoals",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Show Ranking"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Display global ranking on profile"})]}),e.jsx(r,{isSelected:a.privacy.showRanking,onChange:s=>t("privacy","showRanking",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Public Profile"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Make revenue dashboard publicly viewable"})]}),e.jsx(r,{isSelected:a.privacy.publicProfile,onChange:s=>t("privacy","publicProfile",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Share Analytics"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Allow platform to use anonymized data for insights"})]}),e.jsx(r,{isSelected:a.privacy.shareAnalytics,onChange:s=>t("privacy","shareAnalytics",s)})]})]})})]}),e.jsxs(g,{children:[e.jsx(j,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"text-orange-500",size:20}),e.jsx("h3",{className:"text-lg font-semibold",children:"Revenue Goals"})]})}),e.jsx(f,{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx(o,{label:"Monthly Target",type:"number",value:a.goals.monthlyTarget.toString(),onChange:s=>t("goals","monthlyTarget",parseFloat(s.target.value)||0),startContent:"$"}),e.jsx(o,{label:"Yearly Target",type:"number",value:a.goals.yearlyTarget.toString(),onChange:s=>t("goals","yearlyTarget",parseFloat(s.target.value)||0),startContent:"$"}),e.jsx(o,{label:"ORB Target",type:"number",value:a.goals.orbTarget.toString(),onChange:s=>t("goals","orbTarget",parseFloat(s.target.value)||0),endContent:"ORB"})]}),e.jsx(b,{}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:"Track Progress"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Enable goal progress tracking and analytics"})]}),e.jsx(r,{isSelected:a.goals.trackProgress,onChange:s=>t("goals","trackProgress",s)})]}),e.jsxs(N,{label:"Reminder Frequency",selectedKeys:[a.goals.reminderFrequency],onSelectionChange:s=>t("goals","reminderFrequency",Array.from(s)[0]),isDisabled:!a.goals.trackProgress,children:[e.jsx(c,{children:"Daily"},"daily"),e.jsx(c,{children:"Weekly"},"weekly"),e.jsx(c,{children:"Monthly"},"monthly"),e.jsx(c,{children:"Never"},"never")]})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(w,{variant:"light",color:"danger",startContent:e.jsx(I,{size:18}),onPress:A,children:"Reset to Defaults"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(w,{variant:"light",onPress:()=>h(!1),isDisabled:!d,children:"Cancel"}),e.jsx(w,{color:"primary",startContent:e.jsx(L,{size:18}),onPress:x,isLoading:n,isDisabled:!d,children:"Save Settings"})]})]})]})};export{te as default};
