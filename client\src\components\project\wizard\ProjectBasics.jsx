import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import projectTemplates from '../../../data/project-templates';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  Accordion,
  AccordionItem
} from '../../ui/heroui';

const ProjectBasics = ({ projectData, setProjectData }) => {
  console.log('🔍 DEBUG: ProjectBasics component rendering, projectData:', projectData);
  const [uploading, setUploading] = useState(false);

  // Project types
  const projectTypes = [
    { value: 'game', label: 'Game' },
    { value: 'app', label: 'App' },
    { value: 'website', label: 'Website' },
    { value: 'plugin', label: 'Plugin/Extension' },
    { value: 'art', label: 'Art/Asset Pack' },
    { value: 'music', label: 'Music/Sound' },
    { value: 'other', label: 'Other' }
  ];

  // State for template selection
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Apply template when project type changes
  useEffect(() => {
    // Only suggest template if this is a new project (name is empty)
    if (projectData.project_type && !projectData.name) {
      setShowTemplateModal(true);
    }
  }, [projectData.project_type]);

  // Apply selected template
  const applyTemplate = (templateKey) => {
    const template = projectTemplates[templateKey];
    if (!template) {
      toast.error('Template not found');
      return;
    }

    // Keep the current project type and any existing data
    const updatedData = {
      ...template,
      project_type: projectData.project_type,
      // Preserve any existing data that shouldn't be overwritten
      thumbnail_url: projectData.thumbnail_url || template.thumbnail_url || '',
      start_date: projectData.start_date || template.start_date || new Date(),
      launch_date: projectData.launch_date || template.launch_date || null
    };

    setProjectData(updatedData);
    setShowTemplateModal(false);
    toast.success(`Applied ${templateKey} template`);
  };

  // Skip template
  const skipTemplate = () => {
    setShowTemplateModal(false);
    toast.info('Template skipped');
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const fileExt = file.name.split('.').pop();
    const allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    if (!allowedExts.includes(fileExt.toLowerCase())) {
      toast.error('Invalid file type. Please upload an image file.');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size too large. Maximum size is 2MB.');
      return;
    }

    setUploading(true);
    const uploadToastId = toast.loading('Uploading thumbnail...');

    try {
      // Create a unique file name
      const fileName = `project-${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `project-thumbnails/${fileName}`;

      // Use the avatars bucket
      const bucketName = 'avatars';

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        // If the error is because the bucket doesn't exist, try to create it
        if (uploadError.message.includes('bucket') && uploadError.message.includes('not found')) {
          toast.error('Storage bucket not found. Please contact an administrator.', { id: uploadToastId });
          setUploading(false);
          return;
        }
        throw new Error(`Upload error: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      if (!urlData || !urlData.publicUrl) {
        throw new Error('Failed to get public URL for uploaded file');
      }

      // Update project data
      setProjectData({
        ...projectData,
        thumbnail_url: urlData.publicUrl
      });

      toast.success('Thumbnail uploaded successfully', { id: uploadToastId });
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      toast.error(`Failed to upload thumbnail: ${error.message}`, { id: uploadToastId });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="wizard-step-content">
      {/* Step Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-foreground mb-2">Project Basics</h2>
        <p className="text-default-600">
          Let's start with the basic information about your project. This will help us set up the foundation for your collaboration.
        </p>
      </div>

      {/* Template Selection Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-lg mx-4 shadow-2xl">
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold">Use Project Template?</h3>
                <Button
                  variant="light"
                  size="sm"
                  onClick={skipTemplate}
                  aria-label="Close"
                  isIconOnly
                >
                  <i className="bi bi-x-lg"></i>
                </Button>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <p className="text-default-600">
                Would you like to use a template for your {projectData.project_type} project?
                This will pre-populate settings, milestones, and contribution tracking.
              </p>

              <div className="p-4 bg-default-100 rounded-lg">
                <h4 className="font-semibold mb-3 text-foreground">Template includes:</h4>
                <ul className="text-sm text-default-600 space-y-2">
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Pre-configured royalty model
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Relevant contribution categories and task types
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Standard milestones for {projectData.project_type} projects
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Recommended revenue tranches
                  </li>
                </ul>
              </div>

              <div className="flex gap-3 pt-2">
                <Button
                  onClick={() => applyTemplate(projectData.project_type)}
                  className="flex-1"
                  color="primary"
                  size="lg"
                >
                  <i className="bi bi-magic mr-2"></i>
                  Use Template
                </Button>
                <Button
                  variant="bordered"
                  onClick={skipTemplate}
                  className="flex-1"
                  size="lg"
                >
                  Start from Scratch
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Left Column - Main Form Fields */}
        <div className="xl:col-span-2 space-y-8">
          {/* Basic Project Information */}
          <Card className="p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-foreground mb-4">Basic Information</h3>
              </div>

              <div>
                <Input
                  label="Project Name"
                  placeholder="Enter a clear, descriptive project name"
                  value={projectData.name || ""}
                  onValueChange={(value) => setProjectData({ ...projectData, name: value })}
                  isRequired
                  description="This will be the main identifier for your project"
                  size="lg"
                  variant="bordered"
                  classNames={{
                    input: "text-lg",
                    inputWrapper: "h-14"
                  }}
                />
              </div>

              <div>
                <Textarea
                  label="Project Description"
                  placeholder="Describe what your project is about, its goals, and what makes it unique"
                  value={projectData.description || ""}
                  onValueChange={(value) => setProjectData({ ...projectData, description: value })}
                  minRows={4}
                  description="A clear description helps potential contributors understand your vision"
                  variant="bordered"
                  classNames={{
                    input: "text-base"
                  }}
                />
              </div>
            </div>
          </Card>

          {/* Project Type and Timeline */}
          <Card className="p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-foreground mb-4">Project Details</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <div className="space-y-3">
                    <Select
                      label="Project Type"
                      placeholder="Select your project category"
                      selectedKeys={projectData.project_type ? [projectData.project_type] : []}
                      onSelectionChange={(keys) => {
                        const value = Array.from(keys)[0];
                        setProjectData({ ...projectData, project_type: value });
                      }}
                      size="lg"
                      variant="bordered"
                      description="Choose the category that best describes your project"
                    >
                      {projectTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </Select>

                    {projectData.project_type && (
                      <div className="flex items-center justify-between p-3 bg-primary/10 rounded-lg">
                        <div className="flex items-center text-sm text-primary">
                          <i className="bi bi-magic mr-2"></i>
                          Template available for {projectData.project_type} projects
                        </div>
                        <Button
                          type="button"
                          variant="flat"
                          color="primary"
                          onClick={() => setShowTemplateModal(true)}
                          size="sm"
                        >
                          <i className="bi bi-file-earmark-text mr-1"></i>
                          Use Template
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">Start Date</label>
                    <DatePicker
                      selected={projectData.start_date ? new Date(projectData.start_date) : new Date()}
                      onChange={(date) => setProjectData({ ...projectData, start_date: date })}
                      className="flex h-14 w-full rounded-lg border-2 border-default-200 bg-default-50 px-4 py-3 text-base placeholder:text-default-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                      dateFormat="MMMM d, yyyy"
                    />
                    <p className="text-sm text-default-500">
                      When did or will this project officially start?
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <Input
                    type="number"
                    label="Estimated Duration (months)"
                    min={1}
                    max={60}
                    value={projectData.estimated_duration?.toString() || "6"}
                    onValueChange={(value) => setProjectData({
                      ...projectData,
                      estimated_duration: parseInt(value) || 6
                    })}
                    description="How long do you expect this project to take?"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">Launch Date (optional)</label>
                    <DatePicker
                      selected={projectData.launch_date ? new Date(projectData.launch_date) : null}
                      onChange={(date) => setProjectData({ ...projectData, launch_date: date })}
                      className="flex h-14 w-full rounded-lg border-2 border-default-200 bg-default-50 px-4 py-3 text-base placeholder:text-default-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                      placeholderText="Select launch date"
                      dateFormat="MMMM d, yyyy"
                      minDate={projectData.start_date || new Date()}
                    />
                    <p className="text-sm text-default-500">
                      When do you plan to launch? (Can be updated later)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Company Information Section */}
          <Card className="p-6">
            <div className="space-y-6">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-xl mr-4">
                  <i className="bi bi-building text-primary text-xl"></i>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Company Information</h3>
                  <p className="text-sm text-default-500">Required for legal agreement generation</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <Input
                    label="Company Name"
                    value={projectData.company_name || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_name: value })}
                    placeholder="Enter your company name"
                    description="Legal name of the company that owns this project"
                    isRequired
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <Input
                    label="Contact Email"
                    type="email"
                    value={projectData.company_email || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_email: value })}
                    placeholder="<EMAIL>"
                    description="Primary contact email for this project"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div className="lg:col-span-2">
                  <Input
                    label="Company Address"
                    value={projectData.company_address || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_address: value })}
                    placeholder="Enter full company address"
                    description="Complete street address of your company"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <Input
                    label="City"
                    value={projectData.company_city || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_city: value })}
                    placeholder="Enter city"
                    description="City where your company is located"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <Input
                    label="State/Province"
                    value={projectData.company_state || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_state: value })}
                    placeholder="Enter state or province"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <Input
                    label="County/Region"
                    value={projectData.company_county || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_county: value })}
                    placeholder="Enter county or region"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <Input
                    label="Authorized Signer Name"
                    value={projectData.signer_name || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, signer_name: value })}
                    placeholder="Full name of authorized signer"
                    description="Person who will sign legal agreements on behalf of the company"
                    isRequired
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div>
                  <Input
                    label="Signer Title"
                    value={projectData.signer_title || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, signer_title: value })}
                    placeholder="e.g. CEO, President, Authorized Representative"
                    description="Official title of the authorized signer"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>
              </div>

              <div className="mt-6 p-4 bg-primary/10 border border-primary/20 rounded-lg">
                <div className="flex items-start">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary/20 rounded-full mr-3">
                    <i className="bi bi-info-circle-fill text-primary"></i>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-foreground mb-1">Legal Agreement Information</p>
                    <p className="text-sm text-default-600">
                      This information is required for generating legal agreements and will be used to identify the company in contributor agreements.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Project-Specific Fields Section */}
          {projectData.project_type && (
            <Accordion className="mt-4 mb-4">
              <AccordionItem
                key="project-specific"
                aria-label="Project Specific Details"
                title={
                  <div className="flex items-center">
                    <i className="bi bi-gear me-2"></i> {projectData.project_type.charAt(0).toUpperCase() + projectData.project_type.slice(1)} Specific Details
                    <span className="text-muted-foreground ms-2">(Required for Agreement Generation)</span>
                  </div>
                }
              >
                  {projectData.project_type === 'game' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Input
                          label="Game Engine"
                          value={projectData.engine || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, engine: value })}
                          placeholder="e.g. Unity, Unreal Engine, Godot"
                          description="The game engine or framework used for development."
                        />
                      </div>

                      <div className="space-y-2">
                        <Input
                          label="Platforms"
                          value={projectData.platforms || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, platforms: value })}
                          placeholder="e.g. PC, Mac, Mobile, Console"
                          description="The platforms the game will be released on."
                        />
                      </div>
                    </div>
                  )}

                  {projectData.project_type === 'music' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Input
                          label="Genre"
                          value={projectData.genre || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, genre: value })}
                          placeholder="e.g. Rock, Pop, Electronic"
                        />
                      </div>

                      <div className="space-y-2">
                        <Input
                          label="Distribution Platforms"
                          value={projectData.distribution_platforms || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, distribution_platforms: value })}
                          placeholder="e.g. Spotify, Apple Music, Bandcamp"
                        />
                      </div>
                    </div>
                  )}

                  {(projectData.project_type === 'app' || projectData.project_type === 'website' || projectData.project_type === 'plugin') && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Input
                          label="Technology Stack"
                          value={projectData.technology_stack || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, technology_stack: value })}
                          placeholder="e.g. React, Node.js, MongoDB"
                        />
                      </div>

                      <div className="space-y-2">
                        <Input
                          label="Platforms"
                          value={projectData.platforms || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, platforms: value })}
                          placeholder="e.g. Web, iOS, Android, Windows"
                        />
                      </div>
                    </div>
                  )}

                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start">
                      <i className="bi bi-info-circle-fill text-blue-500 mr-2 mt-0.5"></i>
                      <p className="text-sm text-blue-700">
                        These details will be included in the project specifications section of the contributor agreement.
                      </p>
                    </div>
                  </div>
              </AccordionItem>
            </Accordion>
          )}
        </div>

        {/* Right Column - Thumbnail and Settings */}
        <div className="xl:col-span-1 space-y-6">
          {/* Project Thumbnail */}
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg mr-3">
                  <i className="bi bi-image text-primary text-lg"></i>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-foreground">Project Thumbnail</h4>
                  <p className="text-sm text-default-500">Visual representation of your project</p>
                </div>
              </div>

              <div className="thumbnail-upload-container relative">
                {projectData.thumbnail_url ? (
                  <div className="thumbnail-preview relative">
                    <img
                      src={projectData.thumbnail_url}
                      alt="Project thumbnail"
                      className="w-full h-48 object-cover rounded-xl shadow-md"
                    />
                    <Button
                      type="button"
                      variant="flat"
                      size="sm"
                      className="absolute top-3 right-3 bg-danger/90 text-white hover:bg-danger shadow-lg"
                      onClick={() => setProjectData({ ...projectData, thumbnail_url: '' })}
                      isIconOnly
                    >
                      <i className="bi bi-x-lg"></i>
                    </Button>
                  </div>
                ) : (
                  <div className="thumbnail-placeholder border-2 border-dashed border-default-300 rounded-xl p-8 text-center hover:border-primary/50 hover:bg-primary/5 transition-all cursor-pointer h-48 flex flex-col items-center justify-center">
                    <i className="bi bi-cloud-upload text-4xl text-default-400 mb-3"></i>
                    <p className="text-default-600 font-medium mb-1">Upload Thumbnail</p>
                    <p className="text-sm text-default-400">Click to select an image</p>
                  </div>
                )}

                <input
                  type="file"
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                  onChange={handleThumbnailUpload}
                  accept="image/*"
                  disabled={uploading}
                />

                {uploading && (
                  <div className="absolute inset-0 bg-white/95 backdrop-blur-sm rounded-xl flex items-center justify-center z-20">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
                      <span className="text-sm font-medium text-foreground">Uploading...</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="text-xs text-default-500 bg-default-100 p-3 rounded-lg">
                <p className="font-medium mb-1">💡 Tips for best results:</p>
                <ul className="space-y-1">
                  <li>• Recommended size: 800x600 pixels</li>
                  <li>• Use high-quality images (PNG or JPG)</li>
                  <li>• Keep file size under 5MB</li>
                </ul>
              </div>
            </div>
          </Card>

          {/* Project Privacy */}
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg mr-3">
                  <i className="bi bi-shield-check text-primary text-lg"></i>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-foreground">Project Privacy</h4>
                  <p className="text-sm text-default-500">Control who can see your project</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 rounded-lg border-2 transition-colors"
                     style={{
                       borderColor: projectData.is_public ? 'hsl(var(--primary))' : 'hsl(var(--default-200))',
                       backgroundColor: projectData.is_public ? 'hsl(var(--primary) / 0.05)' : 'transparent'
                     }}>
                  <input
                    type="checkbox"
                    id="projectPrivacy"
                    checked={projectData.is_public}
                    onChange={(e) => setProjectData({ ...projectData, is_public: e.target.checked })}
                    className="h-5 w-5 rounded border-default-300 text-primary focus:ring-primary focus:ring-offset-0 mt-1"
                  />
                  <div className="flex-1">
                    <label htmlFor="projectPrivacy" className="text-base font-semibold text-foreground cursor-pointer">
                      {projectData.is_public ? (
                        <span className="flex items-center">
                          <i className="bi bi-globe mr-2 text-primary"></i>
                          Public Project
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <i className="bi bi-lock mr-2 text-default-500"></i>
                          Private Project
                        </span>
                      )}
                    </label>
                    <p className="text-sm text-default-600 mt-1">
                      {projectData.is_public
                        ? 'Visible to all users and can be discovered by others. Great for open-source projects and community building.'
                        : 'Only visible to contributors and invited members. Perfect for proprietary or sensitive projects.'}
                    </p>
                  </div>
                </div>

                <div className="text-xs text-default-500 bg-default-100 p-3 rounded-lg">
                  <p className="font-medium mb-1">
                    <i className="bi bi-info-circle mr-1"></i>
                    Privacy Settings
                  </p>
                  <p>You can change this setting at any time in your project settings. Public projects may attract more contributors but private projects offer better control.</p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProjectBasics;
