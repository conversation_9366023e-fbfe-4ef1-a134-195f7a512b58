var ae=Object.defineProperty,le=Object.defineProperties;var re=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var O=(t,f,h)=>f in t?ae(t,f,{enumerable:!0,configurable:!0,writable:!0,value:h}):t[f]=h,c=(t,f)=>{for(var h in f||(f={}))ne.call(f,h)&&O(t,h,f[h]);if(U)for(var h of U(f))ce.call(f,h)&&O(t,h,f[h]);return t},o=(t,f)=>le(t,re(f));var W=(t,f,h)=>new Promise((b,r)=>{var d=m=>{try{p(h.next(m))}catch(k){r(k)}},g=m=>{try{p(h.throw(m))}catch(k){r(k)}},p=m=>m.done?b(m.value):Promise.resolve(m.value).then(d,g);p((h=h.apply(t,f)).next())});import{j as e,m as oe,c as B,w as de,e as C,b as P,G as me,h as A,r as y,a as H,C as Q,l as G,n as J,o as X,F as _,p as L,E as Y,u as T,v as u,t as E,A as xe}from"./chunk-DX4Z_LyS.js";import{U as ue}from"../assets/main-CGUKzV0x.js";import{c as V}from"./chunk-D8IZ3rty.js";import"./chunk-Cai8ouo_.js";import"./chunk-BV1TipCO.js";import"./chunk-D0ItFSwi.js";import"./chunk-BiNxGM8y.js";const fe=({bounty:t,index:f,onApply:h,onView:b,currentUser:r,formatCurrency:d,getDifficultyColor:g,getTimeSincePosted:p,getTimeUntilDeadline:m})=>{const k=v=>({development:"💻",design:"🎨",testing:"🧪",writing:"✍️",marketing:"📈"})[v]||"💼",D=(v=>{const l=v-new Date,s=Math.floor(l/(1e3*60*60));return s<=24?"urgent":s<=72?"soon":"normal"})(t.deadline),S=D==="urgent",R=D==="soon";return e.jsx(oe.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,delay:f*.05},whileHover:{scale:1.02},className:"h-full",children:e.jsxs(B,{className:`h-full hover:shadow-lg transition-shadow duration-200 ${t.featured?"ring-2 ring-orange-400":""} ${S?"ring-2 ring-red-500":""}`,children:[e.jsx(de,{className:"pb-2",children:e.jsxs("div",{className:"flex justify-between items-start w-full",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[t.featured&&e.jsx("span",{className:"text-yellow-500",children:"⭐"}),S&&e.jsx("span",{className:"text-red-500",children:"🔥"}),R&&e.jsx("span",{className:"text-orange-500",children:"⚡"}),e.jsx("h3",{className:"text-lg font-semibold line-clamp-2",children:t.title})]}),e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(C,{color:"primary",size:"sm",variant:"flat",startContent:k(t.category),children:t.category}),e.jsxs(C,{color:g(t.difficulty),size:"sm",variant:"flat",children:["Level ",t.difficulty]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:d(t.value)}),e.jsx("div",{className:"text-xs text-default-500",children:t.paymentType})]})]})}),e.jsxs(P,{className:"pt-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(me,{name:t.poster.name,size:"sm"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:t.poster.name}),e.jsxs("div",{className:"text-xs text-default-500",children:["⭐ ",t.poster.rating," (",t.poster.reviewCount," reviews)",t.poster.verified&&e.jsx("span",{className:"text-green-500 ml-1",children:"✓"})]})]})]}),e.jsx("p",{className:"text-sm text-default-600 line-clamp-3 mb-3",children:t.description}),t.skillsRequired.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-default-500 mb-1",children:"Skills Required:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[t.skillsRequired.slice(0,3).map((v,n)=>e.jsx(C,{size:"sm",variant:"bordered",className:"text-xs",children:v},n)),t.skillsRequired.length>3&&e.jsxs(C,{size:"sm",variant:"bordered",className:"text-xs",children:["+",t.skillsRequired.length-3]})]})]}),t.requirements&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-default-500 mb-1",children:"Requirements:"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[t.requirements.portfolio&&e.jsx(C,{size:"sm",color:"secondary",variant:"flat",className:"text-xs",children:"Portfolio"}),t.requirements.skillVerification&&e.jsx(C,{size:"sm",color:"primary",variant:"flat",className:"text-xs",children:"Verified Skills"}),t.requirements.certification&&e.jsx(C,{size:"sm",color:"success",variant:"flat",className:"text-xs",children:"Certified"}),t.requirements.minRating&&e.jsxs(C,{size:"sm",color:"warning",variant:"flat",className:"text-xs",children:[t.requirements.minRating,"★ Min"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-blue-500",children:"👥"}),e.jsxs("span",{className:"font-medium",children:[t.applicantCount," applicants"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-gray-500",children:"👁️"}),e.jsxs("span",{children:[t.viewCount," views"]})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs text-default-500",children:[e.jsxs("div",{children:["Posted ",p(t.postedAt)]}),e.jsx("div",{className:S?"text-red-500 font-medium":R?"text-orange-500":"",children:m(t.deadline)})]}),e.jsxs("div",{className:"text-xs text-default-500 mt-1",children:["Timeline: ",t.timeline]})]}),t.paymentType==="milestone"&&t.milestones&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-xs text-default-500 mb-1",children:"Payment Milestones:"}),e.jsxs("div",{className:"space-y-1",children:[t.milestones.slice(0,2).map((v,n)=>e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{children:v.name}),e.jsx("span",{className:"font-medium",children:d(v.amount)})]},n)),t.milestones.length>2&&e.jsxs("div",{className:"text-xs text-default-400",children:["+",t.milestones.length-2," more milestones"]})]})]}),t.tags&&t.tags.length>0&&e.jsx("div",{className:"mb-3",children:e.jsx("div",{className:"flex flex-wrap gap-1",children:t.tags.slice(0,3).map((v,n)=>e.jsxs("span",{className:"text-xs bg-default-100 dark:bg-default-800 px-2 py-1 rounded",children:["#",v]},n))})}),e.jsxs("div",{className:"flex gap-2 mt-auto",children:[e.jsx(A,{size:"sm",variant:"flat",onClick:b,className:"flex-1",children:"View Details"}),e.jsx(A,{size:"sm",color:"primary",onClick:h,className:"flex-1",disabled:t.applicantCount>=20,children:t.applicantCount>=20?"Applications Full":"Apply Now"})]})]})]})})},he=({bounty:t,isOpen:f,onClose:h,onSubmit:b,currentUser:r})=>{const[d,g]=y.useState({coverLetter:"",proposedTimeline:"",proposedBudget:t.value,portfolioLinks:[""],relevantExperience:"",skillsHighlight:[],questions:""}),[p,m]=y.useState(null),k=[{name:"React",level:"Expert",verified:!0},{name:"Python",level:"Advanced",verified:!0},{name:"AI/ML",level:"Intermediate",verified:!1},{name:"APIs",level:"Expert",verified:!0},{name:"Data Science",level:"Beginner",verified:!1}];H.useEffect(()=>{if(t&&k){const n=t.skillsRequired,l=[],s=[];n.forEach(w=>{const I=k.find(F=>F.name.toLowerCase().includes(w.toLowerCase())||w.toLowerCase().includes(F.name.toLowerCase()));I?l.push({required:w,user:I}):s.push(w)});const a=Math.round(l.length/n.length*100);m({score:a,matched:l,missing:s})}},[t]);const M=()=>{if(!d.coverLetter.trim()){alert("Please provide a cover letter");return}if(!d.proposedTimeline.trim()){alert("Please provide your proposed timeline");return}b(o(c({},d),{skillCompatibility:p,submittedAt:new Date}))},D=()=>{g(n=>o(c({},n),{portfolioLinks:[...n.portfolioLinks,""]}))},S=(n,l)=>{g(s=>o(c({},s),{portfolioLinks:s.portfolioLinks.map((a,w)=>w===n?l:a)}))},R=n=>{g(l=>o(c({},l),{portfolioLinks:l.portfolioLinks.filter((s,a)=>a!==n)}))},v=n=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(n);return e.jsx(Q,{isOpen:f,onClose:h,size:"4xl",scrollBehavior:"inside",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(G,{children:[e.jsxs(J,{className:"flex flex-col gap-1",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Apply for Bounty"}),e.jsx("p",{className:"text-default-600 font-normal",children:t.title})]}),e.jsx(X,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(B,{children:e.jsxs(P,{className:"p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:t.title}),e.jsx("p",{className:"text-sm text-default-600",children:t.poster.name})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:v(t.value)}),e.jsx("div",{className:"text-sm text-default-500",children:t.timeline})]})]}),e.jsx("p",{className:"text-sm text-default-600 line-clamp-2",children:t.description})]})}),p&&e.jsx(B,{children:e.jsxs(P,{className:"p-4",children:[e.jsx("h3",{className:"font-semibold mb-3",children:"Skill Compatibility Analysis"}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Compatibility Score:"}),e.jsxs(C,{color:p.score>=80?"success":p.score>=60?"warning":"danger",variant:"flat",children:[p.score,"%"]})]})}),p.matched.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm font-medium mb-2",children:"Matched Skills:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:p.matched.map((n,l)=>e.jsxs(C,{color:"success",variant:"flat",size:"sm",startContent:n.user.verified?"✓":"",children:[n.required," (",n.user.level,")"]},l))})]}),p.missing.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium mb-2",children:"Skills to Highlight:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:p.missing.map((n,l)=>e.jsx(C,{color:"warning",variant:"flat",size:"sm",children:n},l))})]})]})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Cover Letter *"}),e.jsx(_,{placeholder:"Explain why you're the perfect fit for this bounty. Highlight your relevant experience and approach...",value:d.coverLetter,onChange:n=>g(l=>o(c({},l),{coverLetter:n.target.value})),minRows:4,maxRows:8})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Proposed Timeline *"}),e.jsx(L,{placeholder:"e.g., 6 weeks, 2 months",value:d.proposedTimeline,onChange:n=>g(l=>o(c({},l),{proposedTimeline:n.target.value}))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Proposed Budget"}),e.jsx(L,{type:"number",placeholder:"USD",value:d.proposedBudget,onChange:n=>g(l=>o(c({},l),{proposedBudget:parseInt(n.target.value)||t.value})),startContent:"$"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Portfolio Links"}),e.jsxs("div",{className:"space-y-2",children:[d.portfolioLinks.map((n,l)=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx(L,{placeholder:"https://...",value:n,onChange:s=>S(l,s.target.value),className:"flex-1"}),d.portfolioLinks.length>1&&e.jsx(A,{color:"danger",variant:"flat",size:"sm",onClick:()=>R(l),children:"Remove"})]},l)),e.jsx(A,{color:"primary",variant:"flat",size:"sm",onClick:D,children:"Add Portfolio Link"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Relevant Experience"}),e.jsx(_,{placeholder:"Describe your most relevant projects and achievements...",value:d.relevantExperience,onChange:n=>g(l=>o(c({},l),{relevantExperience:n.target.value})),minRows:3})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Questions for Client"}),e.jsx(_,{placeholder:"Any questions about the project requirements, timeline, or deliverables?",value:d.questions,onChange:n=>g(l=>o(c({},l),{questions:n.target.value})),minRows:2})]})]})}),e.jsxs(Y,{children:[e.jsx(A,{color:"danger",variant:"flat",onPress:h,children:"Cancel"}),e.jsx(A,{color:"primary",onPress:M,children:"Submit Application"})]})]})})},pe=({isOpen:t,onClose:f,onSubmit:h,currentUser:b})=>{var l;const[r,d]=y.useState({title:"",description:"",category:"",subcategory:"",value:"",paymentType:"fixed",timeline:"",difficulty:5,skillsRequired:[],requirements:{portfolio:!1,skillVerification:!1,certification:!1,minRating:0,experience:"any"},milestones:[{name:"Project Start",percentage:50,amount:0},{name:"Final Delivery",percentage:50,amount:0}],featured:!1,urgent:!1,maxApplications:10}),[g,p]=y.useState(""),[m,k]=y.useState({}),M=["React","Vue.js","Angular","Node.js","Python","JavaScript","TypeScript","UI/UX Design","Figma","Adobe Creative Suite","Photoshop","Illustrator","Mobile Development","React Native","Flutter","iOS","Android","Backend Development","APIs","Database","MongoDB","PostgreSQL","DevOps","AWS","Docker","Kubernetes","CI/CD","AI/ML","Data Science","Machine Learning","TensorFlow","PyTorch","Blockchain","Solidity","Smart Contracts","Web3","Testing","QA","Automation","Security","Penetration Testing"],D={development:["Frontend","Backend","Full-stack","Mobile","Blockchain","AI/ML"],design:["UI/UX","Graphic Design","Brand Identity","Web Design","Mobile Design"],testing:["QA Testing","Automation","Security Audit","Performance Testing"],writing:["Technical Writing","Content Creation","Documentation","Copywriting"],marketing:["Digital Marketing","SEO","Social Media","Content Strategy"]},S=()=>{g.trim()&&!r.skillsRequired.includes(g.trim())&&(d(s=>o(c({},s),{skillsRequired:[...s.skillsRequired,g.trim()]})),p(""))},R=s=>{d(a=>o(c({},a),{skillsRequired:a.skillsRequired.filter(w=>w!==s)}))};H.useEffect(()=>{if(r.value&&r.paymentType==="milestone"){const s=parseFloat(r.value);d(a=>o(c({},a),{milestones:a.milestones.map(w=>o(c({},w),{amount:Math.round(w.percentage/100*s)}))}))}},[r.value,r.paymentType]);const v=()=>{const s={};return r.title.trim()||(s.title="Title is required"),r.description.trim()||(s.description="Description is required"),r.category||(s.category="Category is required"),(!r.value||parseFloat(r.value)<=0)&&(s.value="Valid budget is required"),r.timeline.trim()||(s.timeline="Timeline is required"),r.skillsRequired.length===0&&(s.skills="At least one skill is required"),k(s),Object.keys(s).length===0},n=()=>{if(!v())return;const s=o(c({},r),{value:parseFloat(r.value),postedAt:new Date,poster:{id:(b==null?void 0:b.id)||"current-user",name:(b==null?void 0:b.display_name)||"Current User",rating:4.8,reviewCount:23,verified:!0},applicantCount:0,viewCount:0,deadline:new Date(Date.now()+30*24*60*60*1e3),tags:r.skillsRequired.slice(0,3)});h(s)};return e.jsx(Q,{isOpen:t,onClose:f,size:"4xl",scrollBehavior:"inside",classNames:{base:"max-h-[90vh]",body:"py-6"},children:e.jsxs(G,{children:[e.jsxs(J,{className:"flex flex-col gap-1",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Post New Bounty"}),e.jsx("p",{className:"text-default-600 font-normal",children:"Create a high-value bounty to attract top talent"})]}),e.jsx(X,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Basic Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Bounty Title *"}),e.jsx(L,{placeholder:"e.g., AI-Powered Analytics Dashboard",value:r.title,onChange:s=>d(a=>o(c({},a),{title:s.target.value})),isInvalid:!!m.title,errorMessage:m.title})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Description *"}),e.jsx(_,{placeholder:"Provide a detailed description of the work required, deliverables, and any specific requirements...",value:r.description,onChange:s=>d(a=>o(c({},a),{description:s.target.value})),minRows:4,isInvalid:!!m.description,errorMessage:m.description})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Category *"}),e.jsx(T,{placeholder:"Select category",selectedKeys:r.category?[r.category]:[],onSelectionChange:s=>d(a=>o(c({},a),{category:Array.from(s)[0]||"",subcategory:""})),isInvalid:!!m.category,errorMessage:m.category,children:Object.keys(D).map(s=>e.jsx(u,{value:s,children:s.charAt(0).toUpperCase()+s.slice(1)},s))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Subcategory"}),e.jsx(T,{placeholder:"Select subcategory",selectedKeys:r.subcategory?[r.subcategory]:[],onSelectionChange:s=>d(a=>o(c({},a),{subcategory:Array.from(s)[0]||""})),isDisabled:!r.category,children:r.category&&((l=D[r.category])==null?void 0:l.map(s=>e.jsx(u,{value:s,children:s},s.toLowerCase().replace(/\s+/g,"-"))))})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Budget & Timeline"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Budget (USD) *"}),e.jsx(L,{type:"number",placeholder:"5000",value:r.value,onChange:s=>d(a=>o(c({},a),{value:s.target.value})),startContent:"$",isInvalid:!!m.value,errorMessage:m.value})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Payment Type"}),e.jsxs(T,{selectedKeys:[r.paymentType],onSelectionChange:s=>d(a=>o(c({},a),{paymentType:Array.from(s)[0]})),children:[e.jsx(u,{children:"Fixed Price"},"fixed"),e.jsx(u,{children:"Milestone-based"},"milestone")]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Timeline *"}),e.jsx(L,{placeholder:"e.g., 4 weeks, 2 months",value:r.timeline,onChange:s=>d(a=>o(c({},a),{timeline:s.target.value})),isInvalid:!!m.timeline,errorMessage:m.timeline})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium mb-2",children:["Difficulty Level: ",r.difficulty,"/10"]}),e.jsx("input",{type:"range",min:"1",max:"10",value:r.difficulty,onChange:s=>d(a=>o(c({},a),{difficulty:parseInt(s.target.value)})),className:"w-full"}),e.jsxs("div",{className:"flex justify-between text-xs text-default-500 mt-1",children:[e.jsx("span",{children:"Beginner"}),e.jsx("span",{children:"Expert"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Skills Required"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(L,{placeholder:"Add required skill",value:g,onChange:s=>p(s.target.value),onKeyPress:s=>s.key==="Enter"&&S(),className:"flex-1"}),e.jsx(A,{color:"primary",onClick:S,children:"Add"})]}),m.skills&&e.jsx("p",{className:"text-red-500 text-sm",children:m.skills}),e.jsx("div",{className:"flex flex-wrap gap-2",children:r.skillsRequired.map((s,a)=>e.jsx(C,{onClose:()=>R(s),variant:"flat",color:"primary",children:s},a))}),e.jsxs("div",{className:"text-sm text-default-500",children:[e.jsx("p",{className:"mb-2",children:"Suggested skills:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:M.slice(0,10).map(s=>e.jsx("button",{onClick:()=>p(s),className:"text-xs bg-default-100 hover:bg-default-200 dark:bg-default-800 dark:hover:bg-default-700 px-2 py-1 rounded transition-colors",children:s},s))})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Application Requirements"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(E,{isSelected:r.requirements.portfolio,onValueChange:s=>d(a=>o(c({},a),{requirements:o(c({},a.requirements),{portfolio:s})})),children:"Require Portfolio"}),e.jsx(E,{isSelected:r.requirements.skillVerification,onValueChange:s=>d(a=>o(c({},a),{requirements:o(c({},a.requirements),{skillVerification:s})})),children:"Require Skill Verification"}),e.jsx(E,{isSelected:r.requirements.certification,onValueChange:s=>d(a=>o(c({},a),{requirements:o(c({},a.requirements),{certification:s})})),children:"Require Certification"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Minimum Rating"}),e.jsxs(T,{selectedKeys:[r.requirements.minRating.toString()],onSelectionChange:s=>d(a=>o(c({},a),{requirements:o(c({},a.requirements),{minRating:parseFloat(Array.from(s)[0])})})),children:[e.jsx(u,{children:"No minimum"},"0"),e.jsx(u,{children:"3.0+ stars"},"3.0"),e.jsx(u,{children:"4.0+ stars"},"4.0"),e.jsx(u,{children:"4.5+ stars"},"4.5")]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Experience Level"}),e.jsxs(T,{selectedKeys:[r.requirements.experience],onSelectionChange:s=>d(a=>o(c({},a),{requirements:o(c({},a.requirements),{experience:Array.from(s)[0]})})),children:[e.jsx(u,{children:"Any level"},"any"),e.jsx(u,{children:"Beginner"},"beginner"),e.jsx(u,{children:"Intermediate"},"intermediate"),e.jsx(u,{children:"Advanced"},"advanced"),e.jsx(u,{children:"Expert"},"expert")]})]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Additional Options"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Maximum Applications"}),e.jsx(L,{type:"number",value:r.maxApplications,onChange:s=>d(a=>o(c({},a),{maxApplications:parseInt(s.target.value)||10})),min:"1",max:"50"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(E,{isSelected:r.featured,onValueChange:s=>d(a=>o(c({},a),{featured:s})),children:"Featured Listing (+$50)"}),e.jsx(E,{isSelected:r.urgent,onValueChange:s=>d(a=>o(c({},a),{urgent:s})),children:"Mark as Urgent"})]})]})]})]})}),e.jsxs(Y,{children:[e.jsx(A,{color:"danger",variant:"flat",onPress:f,children:"Cancel"}),e.jsx(A,{color:"primary",onPress:n,children:"Post Bounty"})]})]})})},Ce=({className:t=""})=>{const{currentUser:f}=y.useContext(ue),[h,b]=y.useState([]),[r,d]=y.useState([]),[g,p]=y.useState(!0),[m,k]=y.useState(""),[M,D]=y.useState(null),[S,R]=y.useState(!1),[v,n]=y.useState(!1),[l,s]=y.useState({category:"all",valueRange:"all",timeline:"all",difficulty:"all",requirements:[]}),[a,w]=y.useState({totalBounties:0,totalValue:0,myApplications:0,myEarnings:0,successRate:0}),I=()=>W(null,null,function*(){try{p(!0);const i=[{id:"1",title:"AI-Powered Analytics Dashboard",description:"Build comprehensive AI analytics dashboard with machine learning models for sales forecasting and customer behavior analysis.",value:15e3,paymentType:"milestone",timeline:"8 weeks",difficulty:9,category:"development",subcategory:"full-stack",skillsRequired:["AI/ML","Python","React","Data Science","APIs"],poster:{id:"poster1",name:"TechCorp Projects",rating:4.9,reviewCount:127,verified:!0},applicantCount:12,viewCount:45,postedAt:new Date(Date.now()-2*24*60*60*1e3),deadline:new Date(Date.now()+5*24*60*60*1e3),featured:!0,urgent:!1,requirements:{portfolio:!0,skillVerification:!0,certification:!0,minRating:4.5,experience:"expert"},milestones:[{name:"Project Start",percentage:25,amount:3750},{name:"Milestone Completion",percentage:50,amount:7500},{name:"Final Delivery",percentage:25,amount:3750}],tags:["AI","Dashboard","Machine Learning"]},{id:"2",title:"Mobile App Security Audit",description:"Comprehensive security audit of React Native financial app with penetration testing and vulnerability assessment.",value:8500,paymentType:"fixed",timeline:"3 weeks",difficulty:8,category:"testing",subcategory:"security",skillsRequired:["Security","Mobile","Penetration Testing","React Native"],poster:{id:"poster2",name:"SecureFinance Studio",rating:4.8,reviewCount:89,verified:!0},applicantCount:8,viewCount:32,postedAt:new Date(Date.now()-1*24*60*60*1e3),deadline:new Date(Date.now()+1*24*60*60*1e3),featured:!0,urgent:!0,requirements:{portfolio:!0,skillVerification:!0,certification:!1,minRating:4,experience:"advanced"},tags:["Security","Mobile","Audit"]},{id:"3",title:"E-commerce Platform Integration",description:"Integrate Shopify API with custom React dashboard for inventory management and sales analytics.",value:4200,paymentType:"fixed",timeline:"4 weeks",difficulty:6,category:"development",subcategory:"frontend",skillsRequired:["React","APIs","E-commerce","Node.js"],poster:{id:"poster3",name:"ShopFlow Projects",rating:4.6,reviewCount:45,verified:!0},applicantCount:5,viewCount:18,postedAt:new Date(Date.now()-3*24*60*60*1e3),deadline:new Date(Date.now()+7*24*60*60*1e3),featured:!1,urgent:!1,requirements:{portfolio:!0,skillVerification:!1,certification:!1,minRating:4,experience:"intermediate"},tags:["E-commerce","Integration","React"]},{id:"4",title:"Blockchain Smart Contract Audit",description:"Security audit of DeFi smart contracts on Ethereum with comprehensive testing and documentation.",value:12e3,paymentType:"milestone",timeline:"6 weeks",difficulty:9,category:"development",subcategory:"blockchain",skillsRequired:["Solidity","Security","Blockchain","Smart Contracts"],poster:{id:"poster4",name:"CryptoSecure Studio",rating:4.9,reviewCount:67,verified:!0},applicantCount:3,viewCount:28,postedAt:new Date(Date.now()-4*24*60*60*1e3),deadline:new Date(Date.now()+10*24*60*60*1e3),featured:!1,urgent:!1,requirements:{portfolio:!0,skillVerification:!0,certification:!0,minRating:4.5,experience:"expert"},tags:["Blockchain","Smart Contracts","Security"]},{id:"5",title:"Real-time Chat System",description:"Build scalable real-time chat system with WebSocket, Redis, and React for messaging platform.",value:3800,paymentType:"fixed",timeline:"6 weeks",difficulty:7,category:"development",subcategory:"backend",skillsRequired:["WebSocket","Redis","Node.js","React","Real-time"],poster:{id:"poster5",name:"ConnectApp Projects",rating:4.7,reviewCount:34,verified:!0},applicantCount:9,viewCount:41,postedAt:new Date(Date.now()-5*24*60*60*1e3),deadline:new Date(Date.now()+14*24*60*60*1e3),featured:!1,urgent:!1,requirements:{portfolio:!0,skillVerification:!1,certification:!1,minRating:4,experience:"advanced"},tags:["Real-time","Chat","WebSocket"]}];b(i),d(i),F(i)}catch(i){V.error("Failed to load bounty marketplace")}finally{p(!1)}}),F=i=>{const x={totalBounties:i.length,totalValue:i.reduce((N,j)=>N+j.value,0),myApplications:3,myEarnings:18400,successRate:85};w(x)},$=i=>i>=1e3?`$${(i/1e3).toFixed(1)}K`:`$${i}`,Z=i=>i>=9?"danger":i>=7?"warning":i>=5?"primary":"success",ee=i=>{const N=new Date-i,j=Math.floor(N/(1e3*60*60*24)),q=Math.floor(N/(1e3*60*60));return j>0?`${j} day${j>1?"s":""} ago`:q>0?`${q} hour${q>1?"s":""} ago`:"Just posted"},se=i=>{const N=i-new Date,j=Math.floor(N/(1e3*60*60*24)),q=Math.floor(N/(1e3*60*60));return j>0?`${j} day${j>1?"s":""} left`:q>0?`${q} hour${q>1?"s":""} left`:"Ending soon"},te=()=>{let i=[...h];if(m){const x=m.toLowerCase();i=i.filter(N=>{var j,q,K;return((j=N.title)==null?void 0:j.toLowerCase().includes(x))||((q=N.description)==null?void 0:q.toLowerCase().includes(x))||N.skillsRequired.some(z=>z.toLowerCase().includes(x))||((K=N.poster.name)==null?void 0:K.toLowerCase().includes(x))||N.tags.some(z=>z.toLowerCase().includes(x))})}l.category!=="all"&&(i=i.filter(x=>x.category===l.category)),l.valueRange!=="all"&&(l.valueRange==="low"?i=i.filter(x=>x.value<5e3):l.valueRange==="medium"?i=i.filter(x=>x.value>=5e3&&x.value<15e3):l.valueRange==="high"&&(i=i.filter(x=>x.value>=15e3))),l.difficulty!=="all"&&(l.difficulty==="beginner"?i=i.filter(x=>x.difficulty<=4):l.difficulty==="intermediate"?i=i.filter(x=>x.difficulty>=5&&x.difficulty<=6):l.difficulty==="advanced"?i=i.filter(x=>x.difficulty>=7&&x.difficulty<=8):l.difficulty==="expert"&&(i=i.filter(x=>x.difficulty>=9))),d(i)},ie=(i,x)=>{try{V.success("Application submitted successfully!"),b(N=>N.map(j=>j.id===i?o(c({},j),{applicantCount:j.applicantCount+1}):j)),R(!1)}catch(N){V.error("Failed to submit application")}};return y.useEffect(()=>{I()},[]),y.useEffect(()=>{te()},[h,m,l]),g?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-default-600",children:"Loading bounty marketplace..."})]})}):e.jsxs("div",{className:`bounty-board ${t}`,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2",children:"🎯 Bounty Board"}),e.jsx("p",{className:"text-default-600",children:"Discover high-value bounties and compete for premium opportunities"})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 mb-6",children:[e.jsx(B,{className:"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20",children:e.jsxs(P,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:a.totalBounties}),e.jsx("div",{className:"text-sm text-default-600",children:"Live Bounties"})]})}),e.jsx(B,{className:"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20",children:e.jsxs(P,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:$(a.totalValue)}),e.jsx("div",{className:"text-sm text-default-600",children:"Total Value"})]})}),e.jsx(B,{className:"bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20",children:e.jsxs(P,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.myApplications}),e.jsx("div",{className:"text-sm text-default-600",children:"My Applications"})]})}),e.jsx(B,{className:"bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-800/20",children:e.jsxs(P,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:$(a.myEarnings)}),e.jsx("div",{className:"text-sm text-default-600",children:"Total Earned"})]})}),e.jsx(B,{className:"bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-800/20",children:e.jsxs(P,{className:"p-4 text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-yellow-600",children:[a.successRate,"%"]}),e.jsx("div",{className:"text-sm text-default-600",children:"Success Rate"})]})})]}),e.jsx(B,{className:"mb-6",children:e.jsx(P,{className:"p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(L,{placeholder:"Search bounties by title, skills, or keywords...",value:m,onChange:i=>k(i.target.value),startContent:e.jsx("span",{className:"text-default-400",children:"🔍"}),className:"w-full"})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs(T,{placeholder:"Category",selectedKeys:[l.category],onSelectionChange:i=>s(o(c({},l),{category:Array.from(i)[0]})),className:"w-32",size:"sm",children:[e.jsx(u,{children:"All Categories"},"all"),e.jsx(u,{children:"Development"},"development"),e.jsx(u,{children:"Design"},"design"),e.jsx(u,{children:"Testing"},"testing"),e.jsx(u,{children:"Writing"},"writing")]}),e.jsxs(T,{placeholder:"Value",selectedKeys:[l.valueRange],onSelectionChange:i=>s(o(c({},l),{valueRange:Array.from(i)[0]})),className:"w-32",size:"sm",children:[e.jsx(u,{children:"All Values"},"all"),e.jsx(u,{children:"Under $5K"},"low"),e.jsx(u,{children:"$5K-$15K"},"medium"),e.jsx(u,{children:"$15K+"},"high")]}),e.jsxs(T,{placeholder:"Difficulty",selectedKeys:[l.difficulty],onSelectionChange:i=>s(o(c({},l),{difficulty:Array.from(i)[0]})),className:"w-32",size:"sm",children:[e.jsx(u,{children:"All Levels"},"all"),e.jsx(u,{children:"Beginner"},"beginner"),e.jsx(u,{children:"Intermediate"},"intermediate"),e.jsx(u,{children:"Advanced"},"advanced"),e.jsx(u,{children:"Expert"},"expert")]}),e.jsx(A,{color:"primary",variant:"flat",onClick:()=>n(!0),className:"whitespace-nowrap",children:"Post Bounty"})]})]})})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.jsx(xe,{children:r.map((i,x)=>e.jsx(fe,{bounty:i,index:x,onApply:()=>{D(i),R(!0)},onView:()=>{D(i)},currentUser:f,formatCurrency:$,getDifficultyColor:Z,getTimeSincePosted:ee,getTimeUntilDeadline:se},i.id))})}),r.length===0&&e.jsx(B,{className:"mt-8",children:e.jsxs(P,{className:"p-8 text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎯"}),e.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No bounties found"}),e.jsx("p",{className:"text-default-600 mb-4",children:m||Object.values(l).some(i=>i!=="all")?"Try adjusting your search or filters":"No bounties available at the moment"}),(m||Object.values(l).some(i=>i!=="all"))&&e.jsx(A,{color:"primary",variant:"flat",onClick:()=>{k(""),s({category:"all",valueRange:"all",timeline:"all",difficulty:"all",requirements:[]})},children:"Clear Filters"})]})}),S&&M&&e.jsx(he,{bounty:M,isOpen:S,onClose:()=>R(!1),onSubmit:i=>ie(M.id),currentUser:f}),v&&e.jsx(pe,{isOpen:v,onClose:()=>n(!1),onSubmit:i=>{V.success("Bounty posted successfully!"),n(!1),I()},currentUser:f})]})};export{Ce as default};
